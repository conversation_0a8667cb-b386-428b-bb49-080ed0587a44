import { Cart, Outlet, OutletNameAPI, OutletPromotion, Product, ProductCartUI, Promotion, PromotionFOCReturnResult, Retailer, UOM } from "@/components/type";
import useRetailerStore from "@/stores/store";
import apiHelper from "../api/apiHelper";
import { DataSource, PicSignedUrl, encodeParams } from "@/stores/utilize";
import { getOutletData, getRetailerData } from "@/stores/authContext";
import moment from "moment";

let retailerAccess: Retailer = useRetailerStore.getState().retailer || {};
let outletNameData: OutletNameAPI = useRetailerStore.getState().outletNameData || {};
let outletId = useRetailerStore.getState().currentOutletData?.id || ""
let uomMap = new Map();


const onRetailerAccess = async () => {
  try {
    if (!Object.keys(retailerAccess).length) {
      const value = await getRetailerData();
      retailerAccess = value;
    }
    return retailerAccess;
  }
  catch (err) {
    return retailerAccess;
  };
}

const onGetRetailerOutletSelectionId = () => {
  if (!outletId) {
    outletId = useRetailerStore.getState().currentOutletData?.id || ""
    return outletId;
  }
  return outletId
}

export const getProduct = async (id: string[] = []) => {

  const tempMap = new Map();
  while (id?.length) {
    const params: any = { id: [] };
    params.id = id?.splice(0, 50);
    const dataSource = new DataSource(
      "productCatalogues",
      encodeParams(params),
      false
    );
    const res: any = await dataSource.load().catch(() => {
      id = []
    });
    res?.items?.map((item: Product) => {
      tempMap.set(item.id, item);
    });
  }
  return tempMap;
};

export const getPromotionProduct = async (
  id: any[] = []
): Promise<OutletPromotion[]> => {
  let result: any = []
  try {
    if (!id.length) return [];
        let activeAt =  moment().startOf("day").add(1, "millisecond").toISOString();

    const outletNameData: any = useRetailerStore.getState().outletNameData || {}
          if (!Object.keys(outletNameData).length) {
            await getOutletData().then((value: any) => {
              activeAt = value?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString()
            })
          } else {
            activeAt = outletNameData?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString()
          }

    while (id.length) {
      const params = {
        outletId: localStorage.getItem("currentOutletId"),
        productId: id.splice(0, 50),
        activeAt: activeAt,
      };
      const dataSource = new DataSource(
        "promotion/outlets",
        encodeParams(params),
        false
      );
      const res: any = await dataSource.load();
      if (res?.items?.length) {
        result = result.concat(res.items);
      }
      if (res?.length) {
        result = result.concat(res);
      }
    }

    return result || [];

  } catch (err) {
    return result || [];
  }
};

export const getPromotionRelatedOutlet = async (
  promotionRelatedOutlet: OutletPromotion[], isNeedFindAutoApply: string = "FALSE", outletCategoryIds: string[], outletCompanBranchId: string
): Promise<Promotion[]> => {
  try {
    if (!promotionRelatedOutlet.length) return [];

    let groupPromotionId: Array<string> = [];

    promotionRelatedOutlet.map((promotion) => {
      promotion.promoIds.map((item) => {
        if (!groupPromotionId.includes(item)) {
          groupPromotionId.push(item);
        }
      });
    });

    if (!groupPromotionId.length) return [];

    let results: Array<Promotion> = [];
    let activeAt =  moment().startOf("day").add(1, "millisecond").toISOString();

    const outletNameData: any = useRetailerStore.getState().outletNameData || {}
          if (!Object.keys(outletNameData).length) {
            await getOutletData().then((value: any) => {
              activeAt = value?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString()
            })
          } else {
            activeAt = outletNameData?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString()
          }

    while (groupPromotionId.length) {
      const params: any = {
        id: groupPromotionId.splice(0, 50),
        isAutoApply: isNeedFindAutoApply,
        outletCategoryIds,
        companyBranchIds: outletCompanBranchId,
        activeAt:activeAt,
        status: "ACTIVE"
      };

      if (isNeedFindAutoApply === "TRUE") {
        delete params.isAutoApply;
      }
      const dataSource = new DataSource(
        "promotions",
        encodeParams(params),
        false
      );
      const res: any = await dataSource.load().catch((err) => {
        groupPromotionId = [];
      });

      const updatedPromotions = await res?.items?.reduce(
        async (accumPromise: any, current: Promotion) => {
          const accum = await accumPromise;

          if (current.images?.length) {
            const image: any = await PicSignedUrl(current.images[0]);
            current.images = [image];
          }

          accum.push(current);
          return accum;
        },
        Promise.resolve([])
      );

      if (updatedPromotions?.length) {
        results = results.concat(updatedPromotions || []);
      }
    }

    return results;
  } catch (err) {
    return [];
  }
};

export const getAllUOMS = async () => {
  try {
    if (uomMap.size) return uomMap;

    const params: any = {
      status: "ACTIVE"
    };
    const dataSource = new DataSource("uoms", encodeParams(params), true);
    const res: any = await dataSource.load()
    res?.map((item: UOM) => {
      uomMap.set(item.id, item);
    });
  }
  catch (err) { }
  finally {
    return uomMap
  }
};

const calculatePromotion = async (tempMap: any) => {

    await onRetailerAccess();

    await getAllUOMS();

    const outlet = await getOutlet()

    await onGetRetailerOutletSelectionId();

    const productCarts: [ProductCartUI[]] = [[]]

    tempMap.forEach((value: any) => {
      productCarts.push(value);
      if (value.length) {
        value.map((item: ProductCartUI) => {
          item.totalDiscount = 0;
          item.promotionApplyIds = [];
          return item;
        })
      }
    })

    const tempMapAfterChange = new Map();

    // to save foc from the api return and prepare for the foc product and UI;
    let focProducts: any = [];

    for (let i = 0 ; i < productCarts.length; i ++) {
      const current  = productCarts[i];

      const payload = {
        outletId: localStorage.getItem("currentOutletId"),
        companyId: retailerAccess.companyId,
        companyBranchId: outlet.companyBranchId,
        productOrdered: current?.filter(item => item.type === 'SALES' && item.checked).map(product => {
          const focProduct: any = {
            productId: product!.productId,
            productUOMId: product.productUOMId,
            quantity: product.quantity,
            price: product.unitPrice,
            promotionIds: Array.from(new Set([...product?.promotionIds || [], ...product.promotionManualApplyIds || []])),
            type: "SALES", //preorder or selling
            discount: product.totalDiscount,
            total: product.totalPrice,
            taxId: product.taxId,
            taxRate: product.taxRate
          }
          if (!focProduct.taxId) delete focProduct.taxId;
          return focProduct;
        })
      }

      if (current.length) {

        const companyId = current[0].companyId;
    
        const url = `promotion/calculateExclude?outletId=${localStorage.getItem("currentOutletId")}`
    
        try {
          if (!payload.productOrdered.length) {
            current.map(item => {
              item.promotionApplyIds = [];
              return item
            })
            tempMapAfterChange.set(companyId, current);
          }
          else {
            const res: any = await apiHelper.POST(url, { salesOrder: payload }, "", "v1")
            
            if (res?.item) {
              const result = res.item || {}
        
              Object.keys(result?.Result).map((key: string) => {
                const { Discount, Error, Foc } = result?.Result[key] || {};
                const index = current.findIndex(item => item.productId === key)
                if (index >= 0) {
                  current[index].totalDiscount = Discount || 0;
                  current[index].promotionApplyIds = foundNotIn(current[index].promotionIds, result.ExcludeIds);
                  current[index].totalNetPrice = (current[index].quantity * current[index].unitPrice) - Discount || 0;
                  // check foc;
                  if (Foc?.length) {
                    Foc.map((item: PromotionFOCReturnResult) => {
                      item.companyId = companyId;
                      item.companyBranchId = current[0].companyBranchId;
                      item.sellingType = current[index].sellingType;
                      return item;
                    })
                    focProducts = focProducts.concat(Foc);
                  }
                }
                else {
                  current[index].totalDiscount = 0;
                  current[index].promotionApplyIds = [];
                }
              })
        
              tempMapAfterChange.set(companyId, current);
            }
          }  
        }
        catch (err) {
          
        }
      }
    }
    
    return {carts: tempMapAfterChange, focProducts: focProducts || []}
}

const foundNotIn = (firstArray: string[], secondArray: string[]) => {
  const result: string[] = []
  firstArray.map((item) => {
    const found = secondArray.includes(item)
    if (!found) {
      result.push(item)
    }
  })
  return result;
}

export const onInitCalculatePromotion = async (tempMap: any): Promise<Map<any, any>> => {
  try {
    const result: any = await calculatePromotion(tempMap);

    // Clear all process
    tempMap.forEach((values: ProductCartUI[], key: string) => {
      const tempCompanyProducts = values.filter((product: ProductCartUI) => product.type !== 'FOC');
      tempMap.set(key, tempCompanyProducts);
    });

    // Add promotion id
    const productIds = result?.focProducts.map((item: PromotionFOCReturnResult) => item.ProductId);
    const productMap = await getProduct(productIds);

    let focProducts = new Map();

    result.focProducts.forEach((item: PromotionFOCReturnResult) => {
      const product: Product = productMap.get(item.ProductId);
      const selectedUom = product.productUOM?.find((val) => val.productUOMId === item.ProductUOMId);

      const forProduct: ProductCartUI = {
        companyBranchId: retailerAccess?.companyBranchId || "",
        companyId: retailerAccess?.companyId || "",
        totalDiscount: 0,
        outletId: outletId,
        retailerId: retailerAccess?.id || "",
        productId: item.ProductId,
        productUOMId: item?.ProductUOMId ?? "",
        promotionIds: [item.PromotionId],
        quantity: item.Quantity || 0,
        unitPrice: 0,
        totalPrice: 0,
        totalNetPrice: 0,
        sellingType: item?.sellingType || "",
        status: "PENDING",
        supplierCompanyId: item?.companyId || "",
        totalTax: 0,
        taxId: "",
        taxRate: 0,
        type: "FOC", 
        name: product.name,
        code: product.sku,
        picture: selectedUom?.pictures?.length ? selectedUom?.pictures[0] : "",
        uom: uomMap.get(item?.ProductUOMId)?.name,
        checked: true,
      };

      let values = focProducts.get(item.companyId);
      if (values?.length) {
        values = values.concat(forProduct);
        focProducts.set(item.companyId, values);
      } else {
        focProducts.set(item.companyId, [forProduct]);
      }
    });

    focProducts.forEach((focProduct, key) => {
      if (focProduct.length) {
        focProduct.map((item: any) => {
          const focPromotionIds = item.promotionIds[0];
          tempMap.forEach((values: ProductCartUI[], key: string) => {
              const index = values.findIndex((product: ProductCartUI) => product?.promotionIds?.includes(focPromotionIds));
              if (index >= 0) {
                let values = tempMap.get(key) || [];
                values = values?.concat(item);
                tempMap.set(key, values);
              }
          })
        })
      }
    });
    return tempMap;
  } catch (err) {
    throw err;
  }
};

export const getOutlet = async () => {

  let currentOutletId = localStorage.getItem("currentOutletId");
  let params: any = {
    id: currentOutletId,
    includedFields: ['id', 'companyBranchId']
  };
  const dataSource = new DataSource("outlets", encodeParams(params), false);

  const res: any = await dataSource.load(); 

  const outlet: Outlet = res?.items?.length ? res.items[0] : {};

  delete outlet?.outletProductList;

  return outlet;
}

export const onInitManualApplyPromotionFromCarts = async (carts: ProductCartUI[]) => {

  const productCatalogueId = carts.map(item => item.productId);
  // get promotion/outlet
  // return productId and promoIds = which product include what promotion
  const promotionRelateds = await getPromotionProduct([...productCatalogueId]);

  // const outlet = await getOutlet();
  // get promotions.
  const promotionRelatedOutlets = await getPromotionRelatedOutlet([...promotionRelateds], 'FALSE', [], "");

  return { promotion: promotionRelatedOutlets, promotionProduct: promotionRelateds }
}

export const getPromotionByProductId = (productId: string, tempPromotions?: Promotion[]) => {
  if (!productId) return []

  const promotionsCheck = tempPromotions;

  const productPromotions = promotionsCheck?.filter(item => {
    const productIds = item.productGroups?.flatMap(product => product.selectedProducts?.flatMap(selected => selected.productId));
    return productIds?.includes(productId)
  }) 
  return productPromotions;
}