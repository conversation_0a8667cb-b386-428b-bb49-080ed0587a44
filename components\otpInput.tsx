import { useMemo } from "react";

//for component to reuse with different props
export type Props = {
  value: string;
  valueLength: number;
  onChange: (value: string) => void;
};


export default function OtpInput({ value, valueLength, onChange }: Props) {
  
  //check the character is digit or not
    const RE_DIGIT = new RegExp(/^\d+$/);

  const inputOnChange = (e: React.ChangeEvent<HTMLInputElement>, idx: number) => {
    const target = e.target;
    let targetValue = target.value.trim();
    const isTargetValueDigit = RE_DIGIT.test(targetValue);


    if (!isTargetValueDigit && targetValue !== "") {
      return;
    }

// if value is digit then get the value if not then put empty string
    targetValue = isTargetValueDigit ? targetValue : " ";

    const targetValueLength = targetValue.length;

    //If the digit length is 1 means that it is a digit.
    if (targetValueLength === 1) {
      const newValue = value.substring(0, idx) + targetValue + value.substring(idx + 1);

      onChange(newValue);

      if (!isTargetValueDigit) {
        return;
      }

      const nextElementSibling = target.nextElementSibling as HTMLInputElement | null;

      if (nextElementSibling) {
        nextElementSibling.focus();
      }
    } else if (targetValueLength === valueLength) {
      onChange(targetValue);

      target.blur();
    }
  };

  const valueItems = useMemo(() => {
    const valueArray = value.split("");
    const items: Array<string> = [];

    for (let i = 0; i < valueLength; i++) {
      const char = valueArray[i];

      if (RE_DIGIT.test(char)) {
        items.push(char);
      } else {
        items.push("");
      }
    }

    return items;
  }, [value, valueLength]);

  const inputOnKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const target = e.target as HTMLInputElement;
    const targetValue = target.value;

    // keep the selection range position
    // if the same digit was typed

    target.setSelectionRange(0, targetValue.length);

    if (e.key !== "Backspace" || target.value !== "") {
      return;
    }

    const previousElementSibling = target.previousElementSibling as HTMLInputElement | null;

    if (previousElementSibling) {
      previousElementSibling.focus();
    }
  };

  const inputOnFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    const { target } = e;

    target.setSelectionRange(0, target.value.length);
  };

  return (
    <div className="otp-group">
      {valueItems.map((digit, idx) => (
        <input key={idx} type="text" inputMode="numeric" autoComplete="one-time-code" pattern="\d{1}" maxLength={valueLength} className="otp-input" value={digit} onChange={(e) => inputOnChange(e, idx)} onFocus={inputOnFocus} onKeyDown={inputOnKeyDown}></input>
      ))}
    </div>
  );
}
