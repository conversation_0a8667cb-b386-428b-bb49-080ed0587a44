/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    screens: {
      "2k": "1920px",
      1800: "1800px",
      1500: "1500px",
      1300: "1300px",
      1250: "1250px",
      1150: "1150px",
      1000: "1000px",
      900: "900px",
      800: "800px",
      700: "700px",
      600: "600px",
      500: "500px",
      xs: "360px",
      sm: "640px",
      md: "769px",
      lg: "1024px",
      xl: "1280px",
      xxl: "1536px",
      // breakPoint: "880px",
      stepBreakPoint: "1080px",
      mobile: "500px",
      tablet: "950px",
      ipad: "820px",
    },
    extend: {
      colors: {
        primaryBlue: "#0c24ff",
        darkBlue: "#131B4F",
        lightOrange: "#FFFAF6",
        blue: "#1890ff",
        lightPurple: "#FBFBFF",
        buttonPurple: "#5151E3",
        white: "#FFFFFF",
        orange: "#FFEDDE",
        purple: "#EAEAFD",
        textBlue: "#151584",
        lightGrey: "#F7F7F7",
        buttonOrange: "#FEA654",
        regalBlue: "#6883E6",
        lightorange: "#FE773D",
        lightorange2: "#FFC85C",
        singlePromoLineGradient: "linear-gradient(to right, #FE773D, #FFC85C)",
        brightBlue: "#7B7BEA",
        lightGrey2: "#F5F5F5",
        darkRed: "#D3232A",
        labelGray: "#808081",
        bgOrange: "#f8f8fa",
        labelDark: "#1A1A1A",
      },
      width: {
        "400px": "400px",
      },
      fontFamily: {
        mulish: ["Mulish", "sans-serif"],
      },
    },
    backgroundImage: {
      loginBg: "url('/img/loginBackground.svg')",
      responsiveLoginBg: "url('/img/responsiveLoginBackground.jpg')",
    },
  },
  plugins: [],
};
