// API URL
// export const API_URL_V1 = "http://34.143.237.115/api/v1/"; // dev
// export const API_URL_V2 = "http://34.143.237.115/api/v2/"; // dev
// export const API_URL_V1 = "https://yltc.neuroforce.zappit.com.my/api/v1/"; // yltc
// export const API_URL_V2 = "https://yltc.neuroforce.zappit.com.my/api/v2/"; // yltc
// export const API_URL_V1 = "https://neuroforce.zappit.com.my/api/v1/"; // ylm
// export const API_URL_V2 = "https://neuroforce.zappit.com.my/api/v2/"; // ylm
// export const API_URL_V1 = "https://ylb.neuroforce.zappit.com.my/api/v1/"; // ylb
// export const API_URL_V2 = "https://ylb.neuroforce.zappit.com.my/api/v2/"; // ylb
// export const API_URL_V1 = "https://dev34.zappit.com.my/api/v1/"; // dev
// export const API_URL_V2 = "https://dev34.zappit.com.my/api/v2/"; // dev
export const API_URL_V1 = "https://sandbox.zappit.com.my/api/v1/"; // dev
export const API_URL_V2 = "https://sandbox.zappit.com.my/api/v2/"; // dev
// export const API_URL_V1 = "http://localhost:5000/api/v1/"; // dev
// export const API_URL_V2 = "http://localhost:5000/api/v2/"; // dev

// export const GOOGLE_PLACE_API_KEY = "AIzaSyAFgF9PwsCdAPKooN8WabpM1zpBDjb-Akc";
export const GOOGLE_PLACE_API_KEY = "AIzaSyA0U0NnkZ6QkW8gc1HD6q-FH5vp9o5Y1V0"; // zappit dev
// export const GOOGLE_PLACE_API_KEY = "AIzaSyDq8iCs45H3xopAoedzArnHgFKrJgPsd00" // ylb
// export const GOOGLE_PLACE_API_KEY = "AIzaSyC8ojRg1r_lpi3ldYGEE1Jz-VLatY8lkw0" // yltc

export const languageDropDownOption = [
  {
    value: "en",
    label: "EN",
  },
  {
    value: "zh",
    label: "CN",
  },
  {
    value: "bm",
    label: "BM",
  },
  {
    value: "ta",
    label: "TA",
  },
];

export const trueFalseOption = [
  {
    value: "TRUE",
    label: "Yes",
  },
  {
    value: "FALSE",
    label: "No",
  },
];

export const returnModeOption = [
  {
    value: "ARRANGE",
    label: "Arrange",
  },
  {
    value: "IMMEDIATE",
    label: "Immediate",
  },
];

export const goodsConditionOption = [
  {
    value: "GOOD",
    label: "Good",
  },
  {
    value: "BAD",
    label: "Bad",
  },
];

export const statusFilterOption1 = [
  {
    key: "ALL",
    label: "All",
  },
  {
    key: "ACTIVE",
    label: "Active",
  },
  {
    key: "INACTIVE",
    label: "Inactive",
  },
  {
    key: "PENDING",
    label: "Pending",
  },
  {
    key: "REJECTED",
    label: "Rejected",
  },
];

export const statementStatusFilterOption = [
  {
    key: "ALL",
    label: "All",
  },
  {
    key: "UNSETTLED",
    label: "Unsettled",
  },
  // {
  //   key: "PARTIALPAID",
  //   label: "Partially Paid",
  // },
  // {
  //   key: "FULLYPAID",
  //   label: "Fully Paid",
  // },
  {
    key: "PENDINGVERIFY",
    label: "Pending Verify",
  },
  {
    key: "COMPLETED",
    label: "Completed",
  },
];

export const SalesOrderOption = [
  {
    value: "CANCELLED",
    label: "Cancelled",
  },
  {
    value: "CONFIRMED",
    label: "Confirmed",
  },
  {
    value: "DELIVERED",
    label: "Delivered",
  },
  {
    value: "GENERATINGINVOICEPDF",
    label: "Generating Invoice PDF",
  },
  {
    value: "INVOICED",
    label: "Invoiced",
  },
  {
    value: "INVOICING",
    label: "Invocing",
  },
  {
    value: "ONHOLD",
    label: "On Hold",
  },
  {
    value: "PICKED",
    label: "Picked",
  },
  {
    value: "UNVERIFIED",
    label: "System Processing",
  },
];

export const SalesOrderFilterOption = [
  {
    key: "ALL",
    label: "All",
  },
  // {
  //   key: "CANCELLED",
  //   label: "Cancelled",
  // },
  {
    key: "CONFIRMED",
    label: "Confirmed",
  },
  // {
  //   key: "DELIVERED",
  //   label: "Delivered",
  // },
  // {
  //   key: "GENERATINGINVOICEPDF",
  //   label: "Generating Invoice PDF",
  // },
  {
    key: "INVOICED",
    label: "Invoiced",
  },
  // {
  //   key: "INVOICING",
  //   label: "Invocing",
  // },
  {
    key: "ONHOLD",
    label: "On Hold",
  },
  // {
  //   key: "PICKED",
  //   label: "Picked",
  // },
  {
    key: "UNVERIFIED",
    label: "System Processing",
  },
];

export const invoiceStatusFilterOption = [
  {
    key: "ALL",
    label: "All",
  },
  {
    key: "CONFIRMED",
    label: "Confirmed",
  },
  {
    key: "STATEMENTED",
    label: "Statemented",
  },
];

export const paymentStatusFilterOption = [
  {
    value: "ALL",
    label: "All",
  },
  // {
  //   value: "VERIFIED",
  //   label: "Verified",
  // },
  {
    value: "UNVERIFIED",
    label: "Unverified",
  },
  {
    value: "PARTIALAPPROVED",
    label: "Partial approved",
  },
  {
    value: "APPROVED",
    label: "Approved",
  },
  {
    value: "REJECTED",
    label: "Rejected",
  },
];

export const creditTypeOption = [
  {
    value: "PRODUCT",
    label: "Product",
  },
  {
    value: "NONPRODUCT",
    label: "Non Product",
  },
  // {
  //   value: "PURCHASERETURN",
  //   label: "Purchase Return",
  // },
];

export const paymentMethodOption = [
  {
    value: "CASH",
    label: "Cash",
  },
  {
    value: "CHEQUE",
    label: "Cheque",
  },
  {
    value: "FPX",
    label: "FPX",
  },
  {
    value: "ONLINETRANSFER",
    label: "Online Transfer",
  },
];

export const BusinessEntity = [
  {
    value: "NGO",
    label:
      "Non-governmental Organization (NGO) / Nonprofit Corporation / Trade Association",
  },
  {
    value: "LIMITED_LIABILITY_PARTNERSHIP",
    label: "Limited Liability of Partnership",
  },
  {
    value: "EDUCATIONAL_INSTITUTION",
    label: "Educational Institution",
  },
  {
    value: "GOVERNMENT",
    label: "Government Institution / Ministry / Government Agency",
  },
  {
    value: "PARTNERSHIP",
    label: "Partnership",
  },
  {
    value: "PROFESSIONAL_SERVICE_PROVIDERS",
    label: "Professional Service Provider",
  },
  {
    value: "PUBLIC_LIMITED",
    label: "Public Limited Company / Public Listed Companies (Berhad)",
  },
  {
    value: "SENDIRIAN_BERHAD",
    label: "Limited Liability Company / Sendirian Berhad (Sdn Bhd)",
  },
  {
    value: "SOLE_PROPRIETORSHIP",
    label: "Sole Proprietorship",
  },
];

export const CurrencyName = [
  { value: "Argentina", label: "Argentina", code: "ARS" },
  { value: "Australia", label: "Australia", code: "AUD" },
  { value: "Bahrain", label: "Bahrain", code: "BHD" },
  { value: "Belarus", label: "Belarus", code: "BYN" },
  { value: "Brazil", label: "Brazil", code: "BRL" },
  { value: "Canada", label: "Canada", code: "CAD" },
  { value: "Chile", label: "Chile", code: "CLP" },
  { value: "China", label: "China", code: "CNY" },
  { value: "Colombia", label: "Colombia", code: "COP" },
  { value: "Costa Rica", label: "Costa Rica", code: "CRC" },
  { value: "Czech Republic", label: "Czech Republic", code: "CZK" },
  { value: "Denmark", label: "Denmark", code: "DKK" },
  { value: "Egypt", label: "Egypt", code: "EGP" },
  { value: "Eurozone", label: "Eurozone", code: "EUR" },
  { value: "Hong Kong", label: "Hong Kong", code: "HKD" },
  { value: "Hungary", label: "Hungary", code: "HUF" },
  { value: "Iceland", label: "Iceland", code: "ISK" },
  { value: "India", label: "India", code: "INR" },
  { value: "Indonesia", label: "Indonesia", code: "IDR" },
  { value: "Israel", label: "Israel", code: "ILS" },
  { value: "Japan", label: "Japan", code: "JPY" },
  { value: "Jordan", label: "Jordan", code: "JOD" },
  { value: "Kuwait", label: "Kuwait", code: "KWD" },
  { value: "Malaysia", label: "Malaysia", code: "MYR" },
  { value: "Mexico", label: "Mexico", code: "MXN" },
  { value: "Morocco", label: "Morocco", code: "MAD" },
  { value: "New Zealand", label: "New Zealand", code: "NZD" },
  { value: "Nigeria", label: "Nigeria", code: "NGN" },
  { value: "Norway", label: "Norway", code: "NOK" },
  { value: "Pakistan", label: "Pakistan", code: "PKR" },
  { value: "Peru", label: "Peru", code: "PEN" },
  { value: "Philippines", label: "Philippines", code: "PHP" },
  { value: "Poland", label: "Poland", code: "PLN" },
  { value: "Qatar", label: "Qatar", code: "QAR" },
  { value: "Romania", label: "Romania", code: "RON" },
  { value: "Russia", label: "Russia", code: "RUB" },
  { value: "Saudi Arabia", label: "Saudi Arabia", code: "SAR" },
  { value: "Serbia", label: "Serbia", code: "RSD" },
  { value: "Singapore", label: "Singapore", code: "SGD" },
  { value: "South Africa", label: "South Africa", code: "ZAR" },
  { value: "South Korea", label: "South Korea", code: "KRW" },
  { value: "Sweden", label: "Sweden", code: "SEK" },
  { value: "Switzerland", label: "Switzerland", code: "CHF" },
  { value: "Thailand", label: "Thailand", code: "THB" },
  { value: "Turkey", label: "Turkey", code: "TRY" },
  { value: "Ukraine", label: "Ukraine", code: "UAH" },
  { value: "United Arab Emirates", label: "United Arab Emirates", code: "AED" },
  { value: "United Kingdom", label: "United Kingdom", code: "GBP" },
  { value: "United States", label: "United States", code: "USD" },
  { value: "Vietnam", label: "Vietnam", code: "VND" },
];

export const stateAbbrevationOptionMyr = [
  // {
  //   label: "-",
  //   value: "-",
  // },
  {
    label: "Johor",
    value: "JOHOR",
  },
  {
    label: "Kedah",
    value: "KEDAH",
  },
  {
    label: "Kelantan",
    value: "KELANTAN",
  },
  {
    label: "Melaka",
    value: "MELAKA",
  },
  {
    label: "Negeri Sembilan",
    value: "NEGERI SEMBILAN",
  },
  {
    label: "Pahang",
    value: "PAHANG",
  },
  {
    label: "Penang",
    value: "PENANG",
  },
  {
    label: "Perak",
    value: "PERAK",
  },
  {
    label: "Perlis",
    value: "PERLIS",
  },
  {
    label: "Sabah",
    value: "SABAH",
  },
  {
    label: "Sarawak",
    value: "SARAWAK",
  },
  {
    label: "Selangor",
    value: "SELANGOR",
  },
  {
    label: "Terengganu",
    value: "TERENGGANU",
  },
  {
    label: "Wilayah Persekutuan Kuala Lumpur",
    value: "wilayah persekutuan kuala lumpur",
  },
  {
    label: "Wilayah Persekutuan Labuan",
    value: "wilayah persekutuan labuan",
  },
  {
    label: "Wilayah Persekutuan Putrajaya",
    value: "wilayah persekutuan putrajaya",
  },
];

export const eInoviceStateSelector = [
  { code: "00", value: "ALL STATES", label: "All States" },
  { code: "01", value: "JOHOR", label: "Johor" },
  { code: "02", value: "KEDAH", label: "Kedah" },
  { code: "03", value: "KELANTAN", label: "Kelantan" },
  { code: "04", value: "MELAKA", label: "Melaka" },
  { code: "05", value: "NEGERI SEMBILAN", label: "Negeri Sembilan" },
  { code: "06", value: "PAHANG", label: "Pahang" },
  { code: "07", value: "PULAU PINANG", label: "Pulau Pinang" },
  { code: "08", value: "PERAK", label: "Perak" },
  { code: "09", value: "PERLIS", label: "Perlis" },
  { code: "10", value: "SELANGOR", label: "Selangor" },
  { code: "11", value: "TERENGGANU", label: "Terengganu" },
  { code: "12", value: "SABAH", label: "Sabah" },
  { code: "13", value: "SARAWAK", label: "Sarawak" },
  {
    code: "14",
    value: "wilayah persekutuan kuala lumpur",
    label: "Wilayah Persekutuan Kuala Lumpur",
  },
  {
    code: "15",
    value: "wilayah persekutuan labuan",
    label: "Wilayah Persekutuan Labuan",
  },
  {
    code: "16",
    value: "wilayah persekutuan putrajaya",
    label: "Wilayah Persekutuan Putrajaya",
  },
  { code: "17", value: "NOT APPLICABLE", label: "Not Applicable" },
];

export const eInvoiceCountryCodeSelector = [
  {
    value: "ABW",
    label: "ARUBA",
  },
  {
    value: "AFG",
    label: "AFGHANISTAN",
  },
  {
    value: "AGO",
    label: "ANGOLA",
  },
  {
    value: "AIA",
    label: "ANGUILLA",
  },
  {
    value: "ALA",
    label: "ALAND ISLANDS",
  },
  {
    value: "ALB",
    label: "ALBANIA",
  },
  {
    value: "AND",
    label: "ANDORA",
  },
  {
    value: "ARE",
    label: "UNITED ARAB EMIRATES",
  },
  {
    value: "ARG",
    label: "ARGENTINA",
  },
  {
    value: "ARM",
    label: "ARMENIA",
  },
  {
    value: "ASM",
    label: "AMERICAN SAMOA",
  },
  {
    value: "ATA",
    label: "ANTARCTICA",
  },
  {
    value: "ATF",
    label: "FRENCH SOUTHERN TERRITORIES",
  },
  {
    value: "ATG",
    label: "ANTIGUA AND BARBUDA",
  },
  {
    value: "AUS",
    label: "AUSTRALIA",
  },
  {
    value: "AUT",
    label: "AUSTRIA",
  },
  {
    value: "AZE",
    label: "AZERBAIDJAN",
  },
  {
    value: "BDI",
    label: "BURUNDI",
  },
  {
    value: "BEL",
    label: "BELGIUM",
  },
  {
    value: "BEN",
    label: "BENIN",
  },
  {
    value: "BES",
    label: "BONAIRE, SINT EUSTATIUS AND SABA",
  },
  {
    value: "BFA",
    label: "BURKINA FASO",
  },
  {
    value: "BGD",
    label: "BANGLADESH",
  },
  {
    value: "BGR",
    label: "BULGARIA",
  },
  {
    value: "BHR",
    label: "BAHRAIN",
  },
  {
    value: "BHS",
    label: "BAHAMAS",
  },
  {
    value: "BIH",
    label: "BOSNIA AND HERZEGOVINA",
  },
  {
    value: "BLM",
    label: "SAINT BARTHELEMY",
  },
  {
    value: "BLR",
    label: "BELARUS",
  },
  {
    value: "BLZ",
    label: "BELIZE",
  },
  {
    value: "BMU",
    label: "BERMUDA",
  },
  {
    value: "BOL",
    label: "BOLIVIA",
  },
  {
    value: "BRA",
    label: "BRAZIL",
  },
  {
    value: "BRB",
    label: "BARBADOS",
  },
  {
    value: "BRN",
    label: "BRUNEI DARUSSALAM",
  },
  {
    value: "BTN",
    label: "BHUTAN",
  },
  {
    value: "BVT",
    label: "BOUVET ISLAND",
  },
  {
    value: "BWA",
    label: "BOTSWANA",
  },
  {
    value: "CAF",
    label: "CENTRAL AFRICAN REPUBLIC",
  },
  {
    value: "CAN",
    label: "CANADA",
  },
  {
    value: "CCK",
    label: "COCOS ISLAND",
  },
  {
    value: "CHE",
    label: "SWITZERLAND",
  },
  {
    value: "CHL",
    label: "CHILE",
  },
  {
    value: "CHN",
    label: "CHINA",
  },
  {
    value: "CIV",
    label: "COTE D'IVOIRE",
  },
  {
    value: "CMR",
    label: "CAMEROON",
  },
  {
    value: "COD",
    label: "CONGO, THE DEMOCRATIC REPUBLIC",
  },
  {
    value: "COG",
    label: "CONGO",
  },
  {
    value: "COK",
    label: "COOK ISLANDS ",
  },
  {
    value: "COL",
    label: "COLOMBIA",
  },
  {
    value: "COM",
    label: "COMOROS",
  },
  {
    value: "CPV",
    label: "CAPE VERDE",
  },
  {
    value: "CRI",
    label: "COSTA RICA",
  },
  {
    value: "CUB",
    label: "CUBA",
  },
  {
    value: "CUW",
    label: "CURACAO",
  },
  {
    value: "CXR",
    label: "CHRISTMAS ISLANDS",
  },
  {
    value: "CYM",
    label: "CAYMAN ISLANDS",
  },
  {
    value: "CYP",
    label: "CYPRUS",
  },
  {
    value: "CZE",
    label: "CZECH REPUBLIC",
  },
  {
    value: "DEU",
    label: "GERMANY",
  },
  {
    value: "DJI",
    label: "DJIBOUTI",
  },
  {
    value: "DMA",
    label: "DOMINICA",
  },
  {
    value: "DNK",
    label: "DENMARK",
  },
  {
    value: "DOM",
    label: "DOMINICAN REPUBLIC",
  },
  {
    value: "DZA",
    label: "ALGERIA",
  },
  {
    value: "ECU",
    label: "ECUADOR",
  },
  {
    value: "EGY",
    label: "EGYPT",
  },
  {
    value: "ERI",
    label: "ERITREA",
  },
  {
    value: "ESH",
    label: "WESTERN SAHARA",
  },
  {
    value: "ESP",
    label: "SPAIN",
  },
  {
    value: "EST",
    label: "ESTONIA",
  },
  {
    value: "ETH",
    label: "ETHIOPIA",
  },
  {
    value: "FIN",
    label: "FINLAND",
  },
  {
    value: "FJI",
    label: "FIJI",
  },
  {
    value: "FLK",
    label: "FALKLAND ISLANDS (MALVINAS)",
  },
  {
    value: "FRA",
    label: "FRANCE",
  },
  {
    value: "FRO",
    label: "FAEROE ISLANDS",
  },
  {
    value: "FSM",
    label: "MICRONESIA, FEDERATED STATES OF",
  },
  {
    value: "GAB",
    label: "GABON",
  },
  {
    value: "GBR",
    label: "UNITED KINGDOM",
  },
  {
    value: "GEO",
    label: "GEORGIA",
  },
  {
    value: "GGY",
    label: "GUERNSEY",
  },
  {
    value: "GHA",
    label: "GHANA",
  },
  {
    value: "GIB",
    label: "GIBRALTAR",
  },
  {
    value: "GIN",
    label: "GUINEA",
  },
  {
    value: "GLP",
    label: "GUADELOUPE",
  },
  {
    value: "GMB",
    label: "GAMBIA",
  },
  {
    value: "GNB",
    label: "GUINEA-BISSAU",
  },
  {
    value: "GNQ",
    label: "EQUATORIAL GUINEA",
  },
  {
    value: "GRC",
    label: "GREECE",
  },
  {
    value: "GRD",
    label: "GRENADA",
  },
  {
    value: "GRL",
    label: "GREENLAND",
  },
  {
    value: "GTM",
    label: "GUATEMALA",
  },
  {
    value: "GUF",
    label: "FRENCH GUIANA",
  },
  {
    value: "GUM",
    label: "GUAM",
  },
  {
    value: "GUY",
    label: "GUYANA",
  },
  {
    value: "HKG",
    label: "HONG KONG",
  },
  {
    value: "HMD",
    label: "HEARD AND MCDONALD ISLANDS",
  },
  {
    value: "HND",
    label: "HONDURAS",
  },
  {
    value: "HRV",
    label: "CROATIA",
  },
  {
    value: "HTI",
    label: "HAITI",
  },
  {
    value: "HUN",
    label: "HUNGARY",
  },
  {
    value: "IDN",
    label: "INDONESIA",
  },
  {
    value: "IMN",
    label: "ISLE OF MAN",
  },
  {
    value: "IND",
    label: "INDIA",
  },
  {
    value: "IOT",
    label: "BRITISH INDIAN OCEAN TERRITORY",
  },
  {
    value: "IRL",
    label: "IRELAND",
  },
  {
    value: "IRN",
    label: "IRAN",
  },
  {
    value: "IRQ",
    label: "IRAQ",
  },
  {
    value: "ISL",
    label: "ICELAND",
  },
  {
    value: "ISR",
    label: "ISRAEL",
  },
  {
    value: "ITA",
    label: "ITALY",
  },
  {
    value: "JAM",
    label: "JAMAICA",
  },
  {
    value: "JEY",
    label: "JERSEY (CHANNEL ISLANDS)",
  },
  {
    value: "JOR",
    label: "JORDAN ",
  },
  {
    value: "JPN",
    label: "JAPAN",
  },
  {
    value: "KAZ",
    label: "KAZAKHSTAN",
  },
  {
    value: "KEN",
    label: "KENYA",
  },
  {
    value: "KGZ",
    label: "KYRGYZSTAN",
  },
  {
    value: "KHM",
    label: "CAMBODIA",
  },
  {
    value: "KIR",
    label: "KIRIBATI",
  },
  {
    value: "KNA",
    label: "ST.KITTS AND NEVIS",
  },
  {
    value: "KOR",
    label: "THE REPUBLIC OF KOREA",
  },
  {
    value: "KWT",
    label: "KUWAIT",
  },
  {
    value: "LAO",
    label: "LAOS",
  },
  {
    value: "LBN",
    label: "LEBANON",
  },
  {
    value: "LBR",
    label: "LIBERIA",
  },
  {
    value: "LBY",
    label: "LIBYAN ARAB JAMAHIRIYA",
  },
  {
    value: "LCA",
    label: "SAINT LUCIA ",
  },
  {
    value: "LIE",
    label: "LIECHTENSTEIN ",
  },
  {
    value: "LKA",
    label: "SRI LANKA  ",
  },
  {
    value: "LSO",
    label: "LESOTHO",
  },
  {
    value: "LTU",
    label: "LITHUANIA",
  },
  {
    value: "LUX",
    label: "LUXEMBOURG",
  },
  {
    value: "LVA",
    label: "LATVIA ",
  },
  {
    value: "MAC",
    label: "MACAO",
  },
  {
    value: "MAF",
    label: "SAINT MARTIN (FRENCH PART)",
  },
  {
    value: "MAR",
    label: "MOROCCO",
  },
  {
    value: "MCO",
    label: "MONACO",
  },
  {
    value: "MDA",
    label: "MOLDOVA, REPUBLIC OF",
  },
  {
    value: "MDG",
    label: "MADAGASCAR",
  },
  {
    value: "MDV",
    label: "MALDIVES",
  },
  {
    value: "MEX",
    label: "MEXICO",
  },
  {
    value: "MHL",
    label: "MARSHALL ISLANDS ",
  },
  {
    value: "MKD",
    label: "MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF",
  },
  {
    value: "MLI",
    label: "MALI",
  },
  {
    value: "MLT",
    label: "MALTA",
  },
  {
    value: "MMR",
    label: "MYANMAR",
  },
  {
    value: "MNE",
    label: "MONTENEGRO",
  },
  {
    value: "MNG",
    label: "MONGOLIA ",
  },
  {
    value: "MNP",
    label: "NORTHERN MARIANA ISLANDS",
  },
  {
    value: "MOZ",
    label: "MOZAMBIQUE",
  },
  {
    value: "MRT",
    label: "MAURITANIA",
  },
  {
    value: "MSR",
    label: "MONTSERRAT",
  },
  {
    value: "MTQ",
    label: "MARTINIQUE",
  },
  {
    value: "MUS",
    label: "MAURITIUS",
  },
  {
    value: "MWI",
    label: "MALAWI",
  },
  {
    value: "MYS",
    label: "MALAYSIA",
  },
  {
    value: "MYT",
    label: "MAYOTTE",
  },
  {
    value: "NAM",
    label: "NAMIBIA",
  },
  {
    value: "NCL",
    label: "NEW CALEDONIA ",
  },
  {
    value: "NER",
    label: "NIGER",
  },
  {
    value: "NFK",
    label: "NORFOLK ISLAND",
  },
  {
    value: "NGA",
    label: "NIGERIA",
  },
  {
    value: "NIC",
    label: "NICARAGUA",
  },
  {
    value: "NIU",
    label: "NIUE",
  },
  {
    value: "NLD",
    label: "NETHERLANDS",
  },
  {
    value: "NOR",
    label: "NORWAY",
  },
  {
    value: "NPL",
    label: "NEPAL",
  },
  {
    value: "NRU",
    label: "NAURU",
  },
  {
    value: "NZL",
    label: "NEW ZEALAND ",
  },
  {
    value: "OMN",
    label: "OMAN",
  },
  {
    value: "PAK",
    label: "PAKISTAN",
  },
  {
    value: "PAN",
    label: "PANAMA",
  },
  {
    value: "PCN",
    label: "PITCAIRN",
  },
  {
    value: "PER",
    label: "PERU",
  },
  {
    value: "PHL",
    label: "PHILIPPINES",
  },
  {
    value: "PLW",
    label: "PALAU",
  },
  {
    value: "PNG",
    label: "PAPUA NEW GUINEA",
  },
  {
    value: "POL",
    label: "POLAND",
  },
  {
    value: "PRI",
    label: "PUERTO RICO",
  },
  {
    value: "PRK",
    label: "DEMOC.PEOPLES REP.OF KOREA",
  },
  {
    value: "PRT",
    label: "PORTUGAL",
  },
  {
    value: "PRY",
    label: "PARAGUAY",
  },
  {
    value: "PSE",
    label: "PALESTINIAN TERRITORY, OCCUPIED",
  },
  {
    value: "PYF",
    label: "FRENCH POLYNESIA",
  },
  {
    value: "QAT",
    label: "QATAR",
  },
  {
    value: "REU",
    label: "REUNION",
  },
  {
    value: "ROU",
    label: "ROMANIA",
  },
  {
    value: "RUS",
    label: "RUSSIAN FEDERATION (USSR)",
  },
  {
    value: "RWA",
    label: "RWANDA",
  },
  {
    value: "SAU",
    label: "SAUDI ARABIA",
  },
  {
    value: "SDN",
    label: "SUDAN",
  },
  {
    value: "SEN",
    label: "SENEGAL",
  },
  {
    value: "SGP",
    label: "SINGAPORE",
  },
  {
    value: "SGS",
    label: "SOUTH GEORGIA AND THE SOUTH SANDWICH ISLAND",
  },
  {
    value: "SHN",
    label: "ST. HELENA ",
  },
  {
    value: "SJM",
    label: "SVALBARD AND JAN MAYEN ISLANDS",
  },
  {
    value: "SLB",
    label: "SOLOMON ISLANDS",
  },
  {
    value: "SLE",
    label: "SIERRA LEONE",
  },
  {
    value: "SLV",
    label: "EL SALVADOR",
  },
  {
    value: "SMR",
    label: "SAN MARINO",
  },
  {
    value: "SOM",
    label: "SOMALIA",
  },
  {
    value: "SPM",
    label: "ST. PIERRE AND MIQUELON",
  },
  {
    value: "SRB",
    label: "SERBIA & MONTENEGRO ",
  },
  {
    value: "SSD",
    label: "SOUTH SUDAN",
  },
  {
    value: "STP",
    label: "SAO TOME AND PRINCIPE",
  },
  {
    value: "SUR",
    label: "SURINAME",
  },
  {
    value: "SVK",
    label: "SLOVAK REPUBLIC",
  },
  {
    value: "SVN",
    label: "SLOVENIA",
  },
  {
    value: "SWE",
    label: "SWEDEN",
  },
  {
    value: "SWZ",
    label: "ESWATINI, KINGDOM OF (SWAZILAND)",
  },
  {
    value: "SXM",
    label: "SINT MAARTEN (DUTCH PART)",
  },
  {
    value: "SYC",
    label: "SEYCHELLES",
  },
  {
    value: "SYR",
    label: "SYRIAN ARAB REPUBLIC",
  },
  {
    value: "TCA",
    label: "TURKS AND CAICOS ISLANDS",
  },
  {
    value: "TCD",
    label: "CHAD",
  },
  {
    value: "TGO",
    label: "TOGO",
  },
  {
    value: "THA",
    label: "THAILAND",
  },
  {
    value: "TJK",
    label: "TAJIKISTAN",
  },
  {
    value: "TKL",
    label: "TOKELAU",
  },
  {
    value: "TKM",
    label: "TURKMENISTAN",
  },
  {
    value: "TLS",
    label: "TIMOR-LESTE ",
  },
  {
    value: "TON",
    label: "TONGA",
  },
  {
    value: "TTO",
    label: "TRINIDAD AND TOBAGO",
  },
  {
    value: "TUN",
    label: "TUNISIA",
  },
  {
    value: "TUR",
    label: "TURKIYE ",
  },
  {
    value: "TUV",
    label: "TUVALU",
  },
  {
    value: "TWN",
    label: "TAIWAN",
  },
  {
    value: "TZA",
    label: "TANZANIA UNITED REPUBLIC",
  },
  {
    value: "UGA",
    label: "UGANDA",
  },
  {
    value: "UKR",
    label: "UKRAINE",
  },
  {
    value: "UMI",
    label: "UNITED STATES MINOR OUTLYING ISLANDS",
  },
  {
    value: "URY",
    label: "URUGUAY",
  },
  {
    value: "USA",
    label: "UNITED STATES OF AMERICA",
  },
  {
    value: "UZB",
    label: "UZBEKISTAN",
  },
  {
    value: "VAT",
    label: "VATICAN CITY STATE (HOLY SEE)",
  },
  {
    value: "VCT",
    label: "SAINT VINCENT AND GRENADINES",
  },
  {
    value: "VEN",
    label: "VENEZUELA",
  },
  {
    value: "VGB",
    label: "VIRGIN ISLANDS(BRITISH)",
  },
  {
    value: "VIR",
    label: "VIRGIN ISLANDS(US)",
  },
  {
    value: "VNM",
    label: "VIETNAM",
  },
  {
    value: "VUT",
    label: "VANUATU",
  },
  {
    value: "WLF",
    label: "WALLIS AND FUTUNA ISLANDS",
  },
  {
    value: "WSM",
    label: "SAMOA",
  },
  {
    value: "YEM",
    label: "YEMEN",
  },
  {
    value: "ZAF",
    label: "SOUTH AFRICA",
  },
  {
    value: "ZMB",
    label: "ZAMBIA",
  },
  {
    value: "ZWE",
    label: "ZIMBABWE",
  },
];

export const eInvoiceGeneralTIN = [
  {
    value: "EI00000000010",
    label: "General Public's TIN (Local) - (EI00000000010)",
  },
  { value: "EI00000000020", label: "Foreign Buyer's TIN - (EI00000000020)" },
  { value: "EI00000000040", label: "Buyer’s TIN - (EI00000000040)" },
];

export const requiredEinvoiceOption = [
  {
    value: "EINVOICE",
    label: "E-Invoice",
  },
  {
    value: "CONSOLIDATED",
    label: "Consolidate",
  },
  {
    value: "ECOMMERCE",
    label: "E-Commerce",
  },
  {
    value: "NOTREQUIRED",
    label: "Not Required",
  },
];