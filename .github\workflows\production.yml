name: Deploy to GCP YLTC VM

on:
  pull_request:
    types: [closed]
    branches:
      - production  # Trigger when a PR is merged into production
jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository
      - name: Checkout code
        uses: actions/checkout@v3

      # Step 2: Set up SSH agent and load the private key from GitHub Secrets
      - name: Set up SSH agent
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}  # SSH key stored in GitHub Secrets

      # Step 3: Deploy to the GCP VM via SSH
      - name: Deploy to GCP VM
        run: |
          ssh -o StrictHostKeyChecking=no zweileow@************* << 'EOF'
          echo "Switching to root and navigating to the app directory..."
          sudo su -  # Ensure the user has passwordless sudo privileges
          
          # Navigate to the application directory
          cd /var/www/yltc/ || exit
          
          # Set the remote URL to the SSH URL
          git remote set-<NAME_EMAIL>:Zappit-Solution/yltc-web.git

          # Fetch the latest changes from the remote repository
          git fetch --all
          git reset --hard origin/production
          git pull origin production

          echo "npm run build"
          npm run build
          
          echo "pm2 delete yltc"
          pm2 delete yltc

          echo "pm2 start npm --name yltc -- start"
          pm2 start npm --name yltc -- start -- --port 3001
          
          echo "Deployment completed successfully!"
          EOF
