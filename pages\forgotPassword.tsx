import React, { useState } from "react";
import { Statistic, Form, Row } from "antd";
import { PasswordInput, PhoneNumberInput } from "../components/input";
import { MessageErrorUI, MessageSuccessUI } from "../components/ui";
import {
  PrimaryButtonUI,
  BackButtonUI,
  ForgotPasswordPreviousButtonUI,
} from "../components/buttonUI";
import apiHelper from "./api/apiHelper";
import OtpInput from "../components/otpInput";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Logo from "../assets/logo/Neuroforce.svg";
import { isValidPhoneNumber } from "react-phone-number-input";
import hashPassword from "../stores/hashPassword";
import { isValidPasswordPattern } from "../stores/utilize";
import { supportedLocales } from "@/components/header";
import { LeftOutlined } from "@ant-design/icons";

const { Countdown } = Statistic;

function ForgotPassword() {
  const { t } = useTranslation("common");
  const [deadline, setDeadline] = useState(0);
  const [phoneNumber, setPhoneNumber] = useState({});
  const [tab, setTab] = useState(0);
  const [otp, setOtp] = useState("");
  const [expired, setExpired] = useState(false);
  const router = useRouter();
  const [otpAttempt, setOtpAttempt] = useState(2);

  //verify Phone number on the first steps
  const getOTP = (values: any): void => {
    let data = {
      contact: values.phoneNumber.substring(1),
    };

    apiHelper
      .POST("retailer/resetOTP", data, "", "v2")
      ?.then(() => {
        MessageSuccessUI(t("Login.forgotPassword.otpSent"));
        setDeadline(Date.now() + 300000);
        setTab(1);
      })
      .catch(() => {
        MessageErrorUI(t("Login.forgotPassword.failSendOtp"));
      });
  };

  // validate OTP is match or not . if match go to next step to set password
  const validateOTP = (otpVal: string) => {
    let data = {
      otp: otpVal,
    };
    apiHelper
      .POST("validateOTP", data)
      ?.then((res: any) => {
        localStorage.setItem("accessToken", res.item.accessToken);
        setTab(2);
      })
      .catch(() => {
        if (otpAttempt > 0) {
          MessageErrorUI(t("Login.forgotPassword.otpIncorrect"));
          setOtpAttempt(otpAttempt - 1);
        } else {
          MessageErrorUI(t("Login.forgotPassword.tryAgainLater"));
          router.push("/login");
        }
      });
  };

  //resend OTP when user make the request.
  const resendOTP = () => {
    setExpired(false);
    getOTP(phoneNumber);
  };

  //save password to backend
  const savePassword = async (values: any) => {
    let data = {
      password: await hashPassword.hashPassword(values.password),
    };

    apiHelper
      .POST("retailer/setPassword", data, "", "v2")
      ?.then(() => {
        MessageSuccessUI(t("Login.forgotPassword.success"));
        router.push("/login");
      })
      .catch(() => {
        MessageErrorUI(t("Login.forgotPassword.fail"));
      });
  };

  //main content
  const showContent = () => {
    if (tab == 0) {
      return (
        <div className="shadow-md mobile:w-[400px] xs:w-[320px] xs:py-6 xs:px-6 flex flex-col px-9 py-10 bg-white rounded-xl">
          <ForgotPasswordPreviousButtonUI
            label="Back"
            onClick={() => router.push("/login")}
            icon={<LeftOutlined />}
          ></ForgotPasswordPreviousButtonUI>
          <p className="text-3xl font-bold mt-6">
            {t("Login.forgotPassword.default")}
          </p>
          <p className="font-normal	text-base mt-6 text-[#666666]">
            {t("Login.forgotPassword.enterNumber")}
          </p>
          <div className="w-full mt-4">
            <Form onFinish={getOTP}>
              <Form.Item
                name="phoneNumber"
                rules={[
                  {
                    required: true,
                    message: t("Login.forgotPassword.notEmptyPhoneNumber"),
                  },
                  {
                    validator(_, value) {
                      if (value && !isValidPhoneNumber(value)) {
                        return Promise.reject(
                          new Error(t("Validation.phoneFormat"))
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <PhoneNumberInput
                  onChange={(val: string) => {
                    setPhoneNumber({ phoneNumber: val });
                  }}
                ></PhoneNumberInput>
              </Form.Item>

              <div className="flex justify-center mt-4">
                <PrimaryButtonUI
                  label={t("Common.next")}
                  disabled={false}
                  htmlType={"submit"}
                  loading={false}
                  className="w-full"
                />
              </div>
            </Form>
          </div>
        </div>
      );
    } else if (tab == 1) {
      const onChange = (value: string) => setOtp(value);
      return (
        <div className="shadow-sm mobile:w-[400px] xs:w-[320px] xs:py-6 xs:px-6 flex flex-col border-2 px-9 py-10 bg-white rounded-xl">
          <p className="text-3xl font-bold ">
            {t("Login.forgotPassword.enterCode")}
          </p>
          <p className="font-normal	text-base mt-6 text-[#666666]">
            {t("Login.forgotPassword.enterOTP")}
          </p>
          <div className="w-full mt-4 ">
            <OtpInput
              value={otp}
              onChange={onChange}
              valueLength={6}
            ></OtpInput>
            <Countdown
              value={deadline}
              format="mm:ss"
              className="mt-4 timerFontColor text-xl text-center"
              onFinish={() => setExpired(true)}
            />
          </div>
          <p className="font-normal	text-base mt-6 text-[#666666]">
            {t("Login.forgotPassword.didntReceive")}
            <span>
              {expired == true ? (
                <a
                  className="text-primaryBlue"
                  onClick={() => {
                    resendOTP();
                  }}
                >
                  {t("Login.forgotPassword.resendCode")}
                </a>
              ) : (
                <a className="cursor-not-allowed text-[#dddddd] hover:text-[#dddddd]">
                  {" "}
                  {t("Login.forgotPassword.resendCode")}
                </a>
              )}
            </span>
          </p>
          <div className="flex justify-center mt-4">
            <PrimaryButtonUI
              label={t("Submit")}
              disabled={false}
              htmlType={"submit"}
              loading={false}
              className="w-full"
              onClick={() => validateOTP(otp)}
            />
          </div>
        </div>
      );
    } else {
      return (
        <div className="shadow-sm mobile:w-[400px] xs:w-[320px] xs:py-6 xs:px-6 flex flex-col border-2 px-9 py-10 bg-white rounded-xl">
          <p className="text-3xl font-bold ">
            {t("Login.forgotPassword.enterPassword")}
          </p>
          <p className="font-normal	text-base mt-6 text-[#666666]">
            {t("Login.forgotPassword.successfulVerification")}
          </p>
          <Form className="mt-4 w-full " onFinish={savePassword}>
            <Row className="flex flex-col gap-y-1">
              <Form.Item
                name="password"
                hasFeedback
                rules={[
                  {
                    required: true,
                    message: t("Login.forgotPassword.enterPassword"),
                  },
                  {
                    validator(_, value) {
                      if (value && value.length < 6) {
                        return Promise.reject(
                          new Error(
                            t("Login.forgotPassword.inputValidPassword")
                          )
                        );
                      }
                      if (value && isValidPasswordPattern(value) == false) {
                        return Promise.reject(
                          new Error(
                            t("Login.forgotPassword.passwordRequirement")
                          )
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <PasswordInput
                  maxLength={20}
                  placeholder={t("PlaceHolder.newPassword")}
                ></PasswordInput>
              </Form.Item>
              <Form.Item
                name="confirmPassword"
                hasFeedback
                rules={[
                  {
                    required: true,
                    message: t("Login.forgotPassword.enterPassword"),
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (value && value.length < 6) {
                        return Promise.reject(
                          new Error(
                            t("Login.forgotPassword.inputValidPassword")
                          )
                        );
                      } else if (
                        !value ||
                        getFieldValue("password") !== value
                      ) {
                        return Promise.reject(
                          new Error(t("Login.forgotPassword.passwordNotMatch"))
                        );
                      }
                      if (value && isValidPasswordPattern(value) == false) {
                        return Promise.reject(
                          new Error(
                            t("Login.forgotPassword.passwordRequirement")
                          )
                        );
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <PasswordInput
                  maxLength={20}
                  placeholder={t("PlaceHolder.confirmPassword")}
                ></PasswordInput>
              </Form.Item>
            </Row>
            <div className="flex justify-center pt-2">
              <PrimaryButtonUI
                label={t("Login.forgotPassword.done")}
                htmlType="submit"
                className="w-full"
              ></PrimaryButtonUI>
            </div>
          </Form>
        </div>
      );
    }
  };

  return (
    <div className="relative w-full h-screen flex">
      <div className="relative w-1/3 xs:hidden mobile:block h-screen bg-loginBg bg-cover" />
      <div className="absolute mobile:hidden xs:block bg-responsiveLoginBg bg-cover inset-0 flex justify-start items-center" />
      <div className="xl:w-2/3 flex flex-col justify-center items-center relative xs:w-full ">
        <div className="mb-3">
          <Logo src={Logo.src} className="h-24 w-48 mb-8" />
        </div>
        {showContent()}
      </div>
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default ForgotPassword;
