import React from "react";
import { Card } from "antd";
import { RightOutlined } from "@ant-design/icons";

interface ClickableCardProps {
  title: string;
  imageComponent: React.ReactNode;
  className?: string;
  onClick?: () => void;
  comingSoon?: boolean;
  isClicked?: boolean;
  width?: string; // Width prop
  height?: string; // Height prop
}

export const ClickableCard: React.FC<ClickableCardProps> = ({
  title,
  imageComponent,
  className,
  onClick,
  comingSoon,
  isClicked = false,
  width = "140px", // Default width
  height = "120px", // Default height
}) => {
  const cardStyle = comingSoon
    ? {
        // border: "1px solid gray",
        // width,
        // height,
        // backgroundColor: "#f0f0f0",
        // opacity: 0.5,
        // cursor: "not-allowed",
        boxShadow:
          "rgba(50, 50, 105, 0.15) 0px 2px 5px 0px, rgba(0, 0, 0, 0.05) 0px 1px 1px 0px",
      }
    : {
        width,
        height,
      };

  const clickColor = isClicked ? "clickableCardOrangeBg" : "clickableCardBg";

  return (
    <Card
      style={{
        width,
        height,
      }}
      // className={`${className} ${clickColor}`}
      className="flex flex-col items-center justify-center gap-3 
                     transition-all duration-300 cursor-pointer
                     shadow-[rgba(50, 50, 105, 0.15) 0px 2px 5px 0px, rgba(0, 0, 0, 0.05) 0px 1px 1px 0px]
                      border border-labelGray/20 hover:border-buttonOrange/30 hover:border-[3px] max-w-280 
                       hover:shadow-[3px_3px_0px_rgba(254,166,84,0.3)] "
      onClick={comingSoon ? undefined : onClick}
    >
      <div
        className={`clickableCardImageComponent   ${
          isClicked ? "orangeImage" : ""
        }`}
      >
        {imageComponent}
      </div>
      <p className="clickableCardText flex text-center break-words">{title}</p>
    </Card>
  );
};

export const LargeClickableCard: React.FC<ClickableCardProps> = ({
  title,
  imageComponent,
  className,
  onClick,
  comingSoon,
  isClicked = false,
}) => {
  const cardStyle = comingSoon
    ? "border-gray-300 w-[120px] bg-gray-200 opacity-50 cursor-not-allowed"
    : null;

  const clickColor = isClicked
    ? "largeClickableCardOrangeBg"
    : "largeClickableCardBg";

  return (
    <Card
      className={`${cardStyle} ${className} largeClickableCard
       transition-all duration-100 
                   shadow-[rgba(50, 50, 105, 0.15) 0px 2px 5px 0px, rgba(0, 0, 0, 0.05) 0px 1px 1px 0px]
                     border border-labelGray/20 hover:border-buttonOrange/30 hover:border-[3px] max-w-280 
                   hover:shadow-[3px_3px_0px_rgba(254,166,84,0.3)]
    `}
      // className={`${cardStyle}  ${className}
      // largeClickableCard flex flex-col items-center gap-3
      //                transition-all duration-200
      //                shadow-[rgba(50, 50, 105, 0.15) 0px 2px 5px 0px, rgba(0, 0, 0, 0.05) 0px 1px 1px 0px]
      //                 border border-labelGray/20 hover:border-buttonOrange/30 hover:border-[3px] max-w-280
      //                  hover:shadow-[3px_3px_0px_rgba(254,166,84,0.3)]`}
      hoverable
      onClick={comingSoon ? undefined : onClick}
    >
      <div
        className={`${
          isClicked ? "orangeImage" : ""
        } flex items-center justify-between`}
      >
        <div className="flex items-center">
          {imageComponent}
          <p className=" pl-[30px] largeClickableCardText text-center">
            {title}
          </p>
        </div>

        <RightOutlined />
      </div>
    </Card>
  );
};
