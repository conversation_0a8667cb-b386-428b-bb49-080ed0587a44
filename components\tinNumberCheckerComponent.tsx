import { SearchOutlined } from "@ant-design/icons";
import { Button, Form, FormInstance, Input, InputProps, Row } from "antd";
import { ValidateStatus } from "antd/es/form/FormItem";
import { useTranslation } from "next-i18next";
import { useEffect, useState } from "react";
import apiHelper from "../pages/api/apiHelper";

interface TaxIncomeNumberCheckerProps extends InputProps {
    formInstance?: FormInstance<any>
    isRequired?: boolean
    fieldName?: string
    checkTINValue: {
        registeredType: string,
        registerSchemeID: string
    }
}

// use under for Form
const TaxIncomeNumberChecker = (props: TaxIncomeNumberCheckerProps) => {

    const {t} = useTranslation();
    const [validateStatus, setValidateStatus] = useState<ValidateStatus>(''); // success | warning | error | validating;
    const [isInitChekedValue, setIsInitCheckedValue] = useState(false);
    const [currentValue, setCurrentValue] = useState(props.value);
    const [helpText, setHelpText] = useState('');

    const HELPTEXT = {
        success: "",
        error: "Tax Income Number Not Found",
        validating: "The information is being validated...",
        warning: "Required Field In value before check valid of Tax Income Number",
        required: 'Tax Income Number is required',
        "": ""
    }

    useEffect(() => {
        setCurrentValue(props.value);
    }, [props.value])

    // Init the checking when detect form and value;
    useEffect(() => {
        if (currentValue && !isInitChekedValue) {
            setIsInitCheckedValue(true);
            if (currentValue) {
                onCheckTaxIncomeNumber();
            }
        }
        else {
            setIsInitCheckedValue(true);
        }
    }, [isInitChekedValue, currentValue])

    useEffect(() => {
        if (!currentValue) {
            setValidateStatus("");
        }
    }, [currentValue])

    // suggest to use useCallback / useMemo prevent performance problem on later.
    const onCheckTaxIncomeNumber = () => {
        // call api to get the result
        // api need authorised key for eInvoices.
        if (currentValue) {
            if (currentValue) {
                setValidateStatus("validating");

                const payload = {
                    tin: currentValue,
                    idType: props.checkTINValue.registeredType,
                    idValue: props.checkTINValue.registerSchemeID
                }

                apiHelper.POST(`eInvoice/validateTIN`, payload).then(() => {
                    setTimeout(() => {
                        setValidateStatus("success");
                        setHelpText(HELPTEXT.success);
                    }, 500)
                }).catch(err => {
                    setValidateStatus("error");
                    setHelpText(err.message + " " + err?.response?.data?.item?.title);
                })
            }
            else {
                setValidateStatus("warning");
                setHelpText(HELPTEXT.warning);
            }
        }
    }

    const validateTaxIncomeNumber = (_: any, value: string) => {
        if (props.isRequired && !value) {
          setValidateStatus('error');
          setHelpText(HELPTEXT.required);
          return Promise.reject(new Error(HELPTEXT.required));
        }

        // if (value && validateStatus === "error") {
        //     return Promise.reject(new Error(HELPTEXT.error))
        // }
        
        if (value) {
            // setValidateStatus("success")
            setHelpText("");
            return Promise.resolve();
        }
    
        return Promise.resolve();
      };
    

    return (
        <Row className="flex flex-1 w-full items-center gap-x-2">
            <Form.Item
                hasFeedback
                label={<p className="text-neutral700 text-[12px]">{t("setting.taxIdentificationNumber")}</p>}
                validateStatus={validateStatus}
                help={helpText}
                rules={[
                    { required: props.isRequired, message: t("setting.taxIdentificationNumber") + " " + t("Validation.requiredField") },
                    { validator: validateTaxIncomeNumber }
                ]}
                className="flex-1"
                name={props?.fieldName ? props.fieldName : "taxIdentificationNumber"}>
                <Input placeholder={t("setting.taxIdentificationNumber")} 
                    maxLength={50}
                    aria-autocomplete="none"
                    disabled={props.disabled}
                    autoComplete="off"
                    className="formTextInput w-full"
                    onChange={(event) => {
                        setCurrentValue(event.target.value)
                    }} value={currentValue}/>
            </Form.Item>
            <Button icon={<SearchOutlined />} onClick={() => onCheckTaxIncomeNumber()} className={helpText.length ? 'mt-[-4]' : 'mt-6'} disabled={props.disabled}>
                {t("Valid")}
            </Button>
        </Row>
    )
}

export default TaxIncomeNumberChecker;