import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { debounce } from "lodash";
import {
  DataSource,
  encodeParams,
  removeDuplicateArray,
} from "../stores/utilize";
import { SelectOption, User } from "./type";
import { Select, SelectProps, Spin } from "antd";
import _ from "lodash";

interface FilterSelect extends SelectProps {
  dbName: string;
  displayExpr: string | Array<string>;
  valueExpr: string;
  searchExpr?: string;
  customParams?: { [key: string]: string | string[] };
  userAccess: User;
  template?: any;
  filterdisplayExpr?: boolean;
}

export const ComponentFilterSelect: React.FC<FilterSelect> = ({
  dropdownMatchSelectWidth,
  value,
  mode,
  allowClear = true,
  showArrow,
  defaultValue,
  disabled,
  onChange,
  placeholder = "",
  dbName = "",
  userAccess,
  displayExpr = "name",
  valueExpr = "id",
  customParams,
  searchExpr: searchExpr = "",
  template,
  filterdisplayExpr,
}) => {
  const [param, setParam] = useState("");
  const [options, setOptions] = useState<SelectOption[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [cursor, setCursor] = useState("0");
  const [isInit, setIsInit] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const searchRef = useRef("");
  const total = 50;
  const debounceTimeout = 1000;
  const className =
    mode === "multiple"
      ? "singleSelectorInput dropDownDesign customSelectDropdown"
      : "filterSingleSelectorInput";

  if (param !== encodeParams(customParams ?? {})) {
    setParam(encodeParams(customParams ?? {}));
  }

  if (!searchExpr) {
    searchExpr = Array.isArray(displayExpr) ? displayExpr[0] : displayExpr;
  }

  const getOptionsByOnline = async () => {
    setIsLoading(true);
    const param: any = {
      [searchExpr]: encodeURIComponent(`${searchRef.current}`),
      cursor,
    };
    if (!value) {
      delete param.params;
    }
    if (param.cursor === "0" || param.cursor === "") {
      delete param.cursor;
    }

    const params: any = Object.assign(param, customParams || {});
    const item = await getSelectOptions({ url: dbName, params });
    setCursor(item.cursor);
    if (isScrolling && displayExpr?.length) {
      const data = filterdisplayExpr
        ? item.options.filter(
            (option: any) =>
              option[Array.isArray(displayExpr) ? displayExpr[0] : displayExpr]
          )
        : item.options;

      setOptions((prev) => removeDuplicateArray([...prev, ...data], valueExpr));
    } else {
      const data = filterdisplayExpr
        ? item.options.filter(
            (option: any) =>
              option[Array.isArray(displayExpr) ? displayExpr[0] : displayExpr]
          )
        : item.options;
      const values = removeDuplicateArray([...data], valueExpr);
      setOptions(values);
    }
    setIsLoading(false);
  };

  const onInitOptions = useCallback(
    async (value: string) => {
      if (!userAccess?.id) return;

      if (!isInit) {
        if (value) searchRef.current = value;
        await getOptionsByOnline();
        setIsInit(true);
      } else {
        searchRef.current = value;
        await getOptionsByOnline();
      }
    },
    [value, userAccess, param]
  );

  useEffect(() => {
    onInitOptions("");
  }, [onInitOptions]);

  const debounceFetcher = useMemo(() => {
    setIsScrolling(false);
    return debounce(onInitOptions, debounceTimeout);
  }, [onInitOptions, debounceTimeout]);

  const filterInput = useMemo(() => {
    return (
      <Select
        value={value}
        mode={mode}
        showSearch
        allowClear={allowClear}
        options={options?.map((item: any) => {
          if (template) {
            return {
              value: item[valueExpr],
              label: template(item),
              status: item.status,
            };
          }
          return {
            value: item[valueExpr],
            label: Array.isArray(displayExpr)
              ? displayExpr
                  ?.map((expr) => {
                    if (expr === "fullName") {
                      return item["firstName"] + " " + item["lastName"];
                    } else if (item[expr]) {
                      return item[expr];
                    }
                    return "";
                  })
                  .filter((label) => label !== "")
                  .join(" - ")
              : displayExpr === "fullName"
              ? item["firstName"] + " " + item["lastName"]
              : item[displayExpr],
            status: item.status,
            data: item,
          };
        })}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        notFoundContent={isLoading ? null : undefined}
        onSearch={debounceFetcher}
        loading={isLoading}
        showArrow={showArrow}
        onSelect={() => {}}
        onPopupScroll={async (e: any) => {
          const { target } = e;
          if (
            (target as any).scrollTop + (target as any).offsetHeight ===
            (target as any).scrollHeight
          ) {
            // if not load all;
            if (options.length >= total && cursor !== "0") {
              await getOptionsByOnline();
              setIsScrolling(true);
            }
          }
        }}
        dropdownRender={(menu) => (
          <>
            {menu}
            {isLoading ? (
              <Spin size="small" style={{ padding: "0 12px" }} />
            ) : null}
          </>
        )}
        defaultValue={defaultValue}
        filterOption={(input, option) =>
          (`${option?.label}` ?? "").toLowerCase().includes(input.toLowerCase())
        }
        className={
          className
            ? `singleSelectorInput dropDownDesign ${className}`
            : "singleSelectorInput dropDownDesign "
        } /* className="singleSelectorInput dropDownDesign" */
        // popupMatchSelectWidth={300}
      />
    );
  }, [disabled, options, isLoading, customParams]);
  return filterInput;
};

/**
 * @param url 'call datasource url'
 * @param state 'react setState'
 * @param displayExpr : what to display.
 * @param valueExpr: what you expected result after select
 */

interface MyParams {
  myself?: string;
}

const getSelectOptions = async ({ url = "", params = {} as MyParams }) => {
  let currentData = {};
  let dataSourceContainingSelf: any = {};
  let resWithOwnData: any = {};

  // Chun haw did this, please check this, cause might have hidden issue.
  if ("myself" in params) {
    currentData = {
      id: params.myself,
    };
    delete params.myself;
    const dataSource = new DataSource(url, encodeParams(currentData), false);
    dataSourceContainingSelf = await dataSource.load();
  }

  const dataSource = new DataSource(url, encodeParams(params), false);
  let res: any = await dataSource.load();

  if (
    Object.keys(dataSourceContainingSelf).length > 0 &&
    Object.values(dataSourceContainingSelf.items).length > 0
  ) {
    // Remove duplicated data
    const existingIds = new Set(
      dataSourceContainingSelf.items.map((item: any) => item.id)
    );
    const filteredItems = res.items.filter(
      (item: any) => !existingIds.has(item.id)
    );

    resWithOwnData = {
      items: [...filteredItems, ...dataSourceContainingSelf.items],
      cursor: "0",
    };
  }

  return {
    options: resWithOwnData?.items || res?.items || [],
    cursor: res?.cursor ?? "0",
    message: res?.message || "",
  };
};
