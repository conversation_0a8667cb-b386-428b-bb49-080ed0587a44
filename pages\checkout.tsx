import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import Header, { supportedLocales } from "../components/header";
import { isValidPhoneNumber } from "react-phone-number-input";
import { PrimaryButtonUI, SecondaryButtonUI, BackButtonUI, IconDescriptionButtonUI } from "../components/buttonUI";
import {
  FormTextInput,
  PhoneNumberInput,
  IconDescriptionRadioButtonInput,
  SingleDateInput,
  DebounceFilterTextInput,
} from "../components/input";
import { ModalUI } from "../components/modalUI";
import { EnvironmentFilled, RightOutlined } from "@ant-design/icons";
import { Form, Col, Row, Collapse } from "antd";
import { FixedBottomBar, ListingTableUI, MessageErrorUI, MessageSuccessUI, ModalInfoUI, constructAddress } from "@/components/ui";
import {
  DataSource,
  NumberThousandSeparator,
  encodeParams,
  formatDateWithShowingMonth,
  formateDate,
  getNextQualifiedDate,
} from "@/stores/utilize";
import {
  Address,
  CompanyGeneralInfo,
  Currency,
  OutletShippingAddress,
  Product,
  ProductCartUI,
  Retailer,
  SalesOrder,
  googleMap,
  Cart as CartInterface,
  ProductOrdered,
  ProductTradeInfo,
  Outlet,
  TradeInfoAggregate,
  ConfigurableField,
} from "@/components/type";
import GoogleMap from "@/components/googleMap";
import apiHelper from "./api/apiHelper";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import moment from "moment";
import { getAllUOMS, onInitCalculatePromotion } from "./api/salesOrderHelper";
import AppFooter from "@/components/footer";
import _, { cloneDeep } from "lodash";

function Checkout() {
  const router = useRouter();
  const { t } = useTranslation("common");
  const { Panel } = Collapse;
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [addForm] = Form.useForm();
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  const [activeAt, setActiveAt] = useState<string>(moment().startOf("day").add(1, "millisecond").toISOString());

  const [companyMap, setCompanyMap] = useState(new Map());
  const [productMap, setProductMap] = useState(new Map());
  const [productTradeMap, setProductTradeMap] = useState(new Map());
  const [uomMap, setUomMap] = useState(new Map());
  const [shippingAddressMap, setShippingAddressMap] = useState(new Map());
  const [groupByCompanyMap, setGroupByCompanyMap] = useState(new Map());
  const [displayMap, setDisplayMap] = useState(new Map());
  const [totalBottomBar, setTotalBottomBar] = useState(0);
  const [discountBottomBar, setDiscountBottomBar] = useState(0);
  const [totalItem, setTotalItem] = useState(0);
  const [productIds, setProductIds] = useState<any>([]);
  const [currency, setCurrency] = useState<Currency>({});
  const [deliveryDate, setDeliveryDate] = useState("");
  const [splitDataMap, setSplitDataMap] = useState(new Map());
  const [preOrderDeliveryDate, setPreOrderDeliveryDate] = useState("");
  const [outletCategoryId, setOutletCategoryId] = useState([]);
  const [outletData, setOutletData] = useState<Outlet>({});
  // const [splitOrderMap, setSplitOrderMap] = useState(new Map());
  // const [preOrderData, setPreOrderData] = useState<PreOrderSetting>({});
  const [isCheckoutButtonLoading, setIsCheckoutButtonLoading] = useState(false);
  const [isProcessingOrderLoading, setIsProcessingOrderLoading] = useState(false);

  const [selectedAddress, setSelectedAddress] = useState<OutletShippingAddress>({});
  const [isSelectAddressVisible, setSelectAddressVisible] = useState(false);
  const [isAddAddressVisible, setAddAddressVisible] = useState(false);
  const [isEditAddressVisible, setEditAddressVisible] = useState(false);

  const [lat, setLat] = useState(0);
  const [lng, setLng] = useState(0);
  const [pinpointedCoordinates, setPinpointedCoordinates] = useState<googleMap>({});

  const [totalValueByCompany, setTotalValueByCompany] = useState(new Map());
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // use the retailStoreState;
  const outletId = useRetailerStore((state) => state.currentOutletData?.id);

  // use for the tradeInfo from the productTradeInfo/aggregate
  const [tradeInfoAggreateMap, setTradeInfoAggreateMap] = useState(new Map());

  // autoCheckout
  const [isAutoCheckout, setIsAutoCheckout] = useState<boolean>(false);
  const [nextQualifiedDate, setNextQualifyDate] = useState("");

  const [checkoutSetting, setCheckoutSetting] = useState<ConfigurableField>({
    id: "",
    name: "",
    value: "",
    status: "",
  });

  // const deliveryDate = "23/02/2024";

  const headerItems = [
    {
      label: t("Header.cart"),
      route: "/cart/myCart",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Checkout.checkout"),
      route: "/checkout",
      className: "labelTextStyle",
    },
  ];

  useEffect(() => {
    // Ensure code runs only on the client-side
    if (typeof window !== "undefined") {
      window.addEventListener("scroll", function () {
        var fixedBottomBar = document.querySelector(".fixed-bottom-bar") as HTMLElement;
        var appFooter = document.querySelector(".app-footer") as HTMLElement;

        if (!appFooter || !fixedBottomBar) {
          return; // Exit early if either appFooter or fixedBottomBar is null
        }

        var footerOffset = appFooter.getBoundingClientRect().top;
        var viewportHeight = window.innerHeight;

        if (footerOffset <= viewportHeight) {
          fixedBottomBar.style.bottom = viewportHeight - footerOffset + "px";
        } else {
          fixedBottomBar.style.bottom = "0";
        }
      });
    }
  }, []);

  useEffect(() => {
    if (router.isReady) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }

      // get outlet info to check autoCheckout eligibility
      const outletNameData: any = useRetailerStore.getState().outletNameData || {};
      if (!Object.keys(outletNameData).length) {
        getOutletData().then((value: any) => {
          setActiveAt(value?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString());
        });
      } else {
        setActiveAt(outletNameData?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString());
      }
    }
  }, [router.isReady]);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0 && outletId) {
      getProductFromCart();
      getShippingAddress();
      getCurrency();
      getOutletCategory(outletId);
      getOutletData(outletId);
    }
  }, [retailerAccess, outletId]);

  useEffect(() => {
    if (
      groupByCompanyMap.size > 0 &&
      companyMap.size > 0 &&
      productMap.size > 0 &&
      productTradeMap.size > 0 &&
      productIds.length > 0 &&
      tradeInfoAggreateMap.size > 0
    ) {
      reMap();
    }
  }, [groupByCompanyMap, companyMap, productMap, productTradeMap, productIds, tradeInfoAggreateMap]);

  useEffect(() => {
    if (selectedAddress) {
      editForm.setFieldsValue({
        addressName: selectedAddress.shippingAddressDescription,
        personInCharge: selectedAddress.shippingContactPerson,
        phoneNumber: selectedAddress.shippingOfficePhone,
        unitNo: selectedAddress.unitNo,
        // location: selectedAddress.location,
        address1: selectedAddress.address1,
        address2: selectedAddress.address2,
        city: selectedAddress.city,
        postalCode: selectedAddress.postalCode,
        state: selectedAddress.state,
        country: selectedAddress.country,
        longitude: selectedAddress.shippinglongitude,
        latitude: selectedAddress.shippinglatitude,
      });
    }
  }, [selectedAddress]);

  useEffect(() => {
    // Function to check screen size and update state
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 950); // Define your breakpoint for small screens
    };

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Call handleResize initially to set initial screen size
    handleResize();

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const getProductFromCart = async () => {
    const ids: string = router.query.id as string;
    let array: string[] = [];
    // if (ids && ids.includes(",")) {
    array = ids.split(",");
    // }

    // let params: any = {
    //   id: array.length > 0 ? array : ids,
    // };

    const uoms = await getAllUOMS();
    setUomMap(uoms);

    // const dataSource = new DataSource(
    //   "retailerCarts",
    //   encodeParams(params),
    //   false,
    //   "v2"
    // );
    // dataSource
    //   .load()
    //   .then((res: any) => {
    //     if (res !== null) {
    //       const productIds = res.items.map(
    //         (item: CartInterface) => item.productId
    //       );

    //       setProductIds(productIds);
    //       const objectMap = res.items.reduce(
    //         (accumulator: any, current: CartInterface) => {
    //           accumulator["companyId"] = accumulator["companyId"] || [];
    //           if (
    //             current.companyId &&
    //             !companyMap.has(current.companyId) &&
    //             !accumulator["companyId"].includes(current.companyId)
    //           ) {
    //             accumulator["companyId"].push(current.companyId ?? "");
    //           }

    //           accumulator["productId"] = accumulator["productId"] || [];
    //           if (
    //             current.productId &&
    //             !productMap.has(current.productId) &&
    //             !accumulator["productId"].includes(current.productId)
    //           ) {
    //             accumulator["productId"].push(current.productId ?? "");
    //           }

    //           accumulator["productUOMId"] = accumulator["productUOMId"] || [];
    //           if (
    //             current.productUOMId &&
    //             !uomMap.has(current.productUOMId) &&
    //             !accumulator["productUOMId"].includes(current.productUOMId)
    //           ) {
    //             accumulator["productUOMId"].push(current.productUOMId ?? "");
    //           }

    //           return accumulator;
    //         },
    //         {}
    //       );

    //       getCompany(objectMap["companyId"]);
    //       getProduct(objectMap["productId"]);
    //       getProductTrade(productIds);

    //       setGroupByCompanyMap((prevDataMap) => {
    //         const newDataMap = new Map(prevDataMap);
    //         res.items.forEach((product: any) => {
    //           const reMap = {
    //             ...product,
    //             checked: false,
    //           };
    //           if (!newDataMap.has(product.companyId)) {
    //             newDataMap.set(product.companyId, [reMap]);
    //           } else {
    //             const existingCompany = newDataMap.get(product.companyId);
    //             if (existingCompany) {
    //               // Check if product ID already exists in the existingCompany array
    //               const productIndex = existingCompany.findIndex(
    //                 (item: any) => item.id === product.id
    //               );

    //               if (productIndex === -1) {
    //                 newDataMap.set(product.companyId, [
    //                   ...existingCompany,
    //                   reMap,
    //                 ]);
    //               } else {
    //                 // If product ID exists, you might want to update the existing product instead
    //                 existingCompany[productIndex] = reMap;
    //                 newDataMap.set(product.companyId, [...existingCompany]);
    //               }
    //             }
    //           }
    //         });

    //         return newDataMap;
    //       });
    //     }
    //   })
    //   .catch(() => {
    //     //* This Part need re-edit*//
    //   });

    let allData: any = [];

    const params: any = {
      id: [],
    };
    try {
      while (array?.length) {
        params.id = array?.splice(0, 50);
        const dataSource = new DataSource("retailerCarts", encodeParams(params), false, "v2");
        const res: any = await dataSource.load();
        if (res !== null && res.items.length > 0) {
          allData = allData.concat(res.items);
        }
      }
    } catch (err) {
      return [];
    }
    const arrayOfProduct: any[] = await allData.map((item: CartInterface) => item.productId);

    if (allData.length > 0) {
      setProductIds(allData.map((item: CartInterface) => item.productId));
      getAggreateProducts(cloneDeep(arrayOfProduct));
      getProductTrade(arrayOfProduct);
    }

    const objectMap = allData.reduce((accumulator: any, current: CartInterface) => {
      accumulator["companyId"] = accumulator["companyId"] || [];
      if (current.companyId && !companyMap.has(current.companyId) && !accumulator["companyId"].includes(current.companyId)) {
        accumulator["companyId"].push(current.companyId ?? "");
      }

      accumulator["productId"] = accumulator["productId"] || [];
      if (current.productId && !productMap.has(current.productId) && !accumulator["productId"].includes(current.productId)) {
        accumulator["productId"].push(current.productId ?? "");
      }

      accumulator["productUOMId"] = accumulator["productUOMId"] || [];
      if (current.productUOMId && !uomMap.has(current.productUOMId) && !accumulator["productUOMId"].includes(current.productUOMId)) {
        accumulator["productUOMId"].push(current.productUOMId ?? "");
      }

      return accumulator;
    }, {});

    getCompany(objectMap["companyId"]);
    await getProduct(objectMap["productId"]);

    setGroupByCompanyMap((prevDataMap) => {
      const newDataMap = new Map(prevDataMap);
      allData.forEach((product: any) => {
        const reMap = {
          ...product,
          checked: false,
        };
        if (!newDataMap.has(product.companyId)) {
          newDataMap.set(product.companyId, [reMap]);
        } else {
          const existingCompany = newDataMap.get(product.companyId);
          if (existingCompany) {
            // Check if product ID already exists in the existingCompany array
            const productIndex = existingCompany.findIndex((item: any) => item.id === product.id);

            if (productIndex === -1) {
              newDataMap.set(product.companyId, [...existingCompany, reMap]);
            } else {
              // If product ID exists, you might want to update the existing product instead
              existingCompany[productIndex] = reMap;
              newDataMap.set(product.companyId, [...existingCompany]);
            }
          }
        }
      });

      return newDataMap;
    });
  };

  const getOutletData = async (id: string[] = []) => {
    const dataSource = new DataSource("outlets", encodeParams({ id: id }), false);
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setOutletData(res.items?.[0]);

          if (res.items?.[0]?.isAutoCheckoutEnabled === "TRUE") {
            const schedule = res.items?.[0]?.autoCheckoutSchedule;

            const today = new Date();
            const formattedDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());

            // get next auto checkout date
            const nextQualifiedDate = getNextQualifiedDate(schedule, formattedDate) ?? "";

            setIsAutoCheckout(true);
            setNextQualifyDate(formatDateWithShowingMonth(nextQualifiedDate));
          }
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getOutletCategory = async (id: string[] = []) => {
    const dataSource = new DataSource("outlets", encodeParams({ id: id }), false);
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setOutletCategoryId(res.items?.[0].outletCategoryIds);
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getCompany = async (id: string[] = []) => {
    const dataSource = new DataSource("companies", encodeParams({ id: id }), false);
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setCompanyMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: CompanyGeneralInfo) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getProduct = async (id: string[] = []) => {
    let tempProductMap = new Map(productMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("productCatalogues", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setProductMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Product) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res?.items.map((item: Product) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getProductTrade = async (id: string[] = []) => {
    // const params: any = {
    //   companyId: retailerAccess.companyId,
    //   productCatalogueId: id,
    // };
    // const dataSource = new DataSource(
    //   "productTradeInfos",
    //   encodeParams(params),
    //   false
    // );
    // dataSource
    //   .load()
    //   .then((res: any) => {
    //     if (res !== null && res.items.length > 0) {
    //       setProductTradeMap((prevDataMap) => {
    //         const newDataMap = new Map(prevDataMap);
    //         res.items.forEach((item: ProductTradeInfo) => {
    //           if (!newDataMap.has(item.productCatalogueId)) {
    //             newDataMap.set(item.productCatalogueId, item);
    //           }
    //         });
    //         return newDataMap;
    //       });
    //     }
    //   })
    //   .catch(() => {
    //     //* This Part need re-edit*//
    //   });

    if (!id?.length) return [];

    let productDatas: any = [];
    const params: any = {
      companyId: retailerAccess.companyId,
      productCatalogueId: [],
    };
    try {
      while (id?.length) {
        params.productCatalogueId = id?.splice(0, 50);
        const dataSource = new DataSource("productTradeInfos", encodeParams(params), false);
        dataSource
          .load()
          .then((res: any) => {
            if (res !== null && res?.items.length > 0) {
              setProductTradeMap((prevDataMap) => {
                const newDataMap = new Map(prevDataMap);
                res.items.forEach((item: ProductTradeInfo) => {
                  if (!newDataMap.has(item.productCatalogueId)) {
                    newDataMap.set(item.productCatalogueId, item);
                  }
                });
                return newDataMap;
              });
            }
          })
          .catch(() => {
            //* This Part need re-edit*//
          });
      }
    } catch (err) {
      return productDatas;
    }
    return productDatas;
  };

  // const getUOM = async (id: string[] = []) => {
  //   let tempProductMap = new Map(uomMap);
  //   if (!id?.length) return tempProductMap;

  //   const params: any = {
  //     status: "ACTIVE",
  //     id: [],
  //   };
  //   try {
  //     while (id?.length) {
  //       params.id = id?.splice(0, 50);
  //       const dataSource = new DataSource("uoms", encodeParams(params), false);
  //       const res: any = await dataSource.load().catch(() => {
  //         id = [];
  //         //* This Part need re-edit*//
  //       });

  //       if (res !== null && res.items.length > 0) {
  //         setUomMap((prevDataMap) => {
  //           const newDataMap = new Map(prevDataMap);
  //           res.items.forEach((item: UOM) => {
  //             if (!newDataMap.has(item.id)) {
  //               newDataMap.set(item.id, item);
  //             }
  //           });
  //           return newDataMap;
  //         });
  //         res.items?.map((item: UOM) => {
  //           tempProductMap.set(item.id, item);
  //         });
  //       }
  //     }
  //   } catch (err) {
  //     return tempProductMap;
  //   }
  //   return tempProductMap;
  // };

  const getShippingAddress = () => {
    const dataSource = new DataSource(
      "shippingAddresses",
      encodeParams({
        outletId: localStorage.getItem("currentOutletId"),
        status: "ACTIVE",
      }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          const temp = res.items.find((val: OutletShippingAddress) => val.isDefaultShipping === "TRUE");
          temp.description = constructAddress(temp);
          setSelectedAddress(temp);
          setShippingAddressMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: OutletShippingAddress) => {
              item.description = constructAddress(item);
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getCurrency = () => {
    const dataSource = new DataSource("currencies", encodeParams({ companyId: `${retailerAccess.companyId}` }), false);
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setCurrency(res.items[0]);
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getPreOrder = async (productIds: string[]) => {
    let preOrderData: any = [];
    try {
      const param = {
        // companyBranchIds: retailerAccess?.companyBranchIds,
        outletIds: outletId,
        productIds: productIds,
        status: "ACTIVE",
        activeAt: activeAt,
        outletCategoryIds: outletCategoryId,
        isOrCondition: "TRUE",
      };
      const dataSource = new DataSource("preorderSettings", encodeParams(param), false, "v2");
      const res: any = await dataSource.load();
      if (res !== null && res.items.length > 0) {
        res.items.map((item: any) => {
          const endDate = new Date(item.endDate);
          const orderLeadTime = item.orderLeadTime;
          let newDate = new Date(endDate);
          newDate.setDate(endDate.getDate() + orderLeadTime);
          item.date = moment(newDate).format("YYYY-MM-DDT00:00:00") + "Z";
          preOrderData.push(item);
        });
      }
      return { preOrderData };
    } catch (err) {
      return { preOrderData };
    }
  };

  const getAggreateProducts = async (productIds: string[] = []): Promise<TradeInfoAggregate[]> => {
    let currentOutletId = localStorage.getItem("currentOutletId");
    let result: any = [];
    try {
      while (productIds.length) {
        const params: any = {
          companyId: retailerAccess.companyId,
          outletId: currentOutletId,
          maxResultsPerPage: 100,
          pageNumber: "1",
          productCatalogueId: productIds.splice(0, 50),
          activeAt: activeAt,
        };

        const dataSource = new DataSource("productTradeInfo/aggregate", encodeParams(params), false);
        const res: any = await dataSource.load();

        if (!res?.items?.length) {
          productIds = [];
          return result;
        }
        const productTradeInfoAggregate: TradeInfoAggregate[] = res?.items;
        result = result.concat(productTradeInfoAggregate);
      }

      const newDataMap = new Map(tradeInfoAggreateMap);
      result.map((item: TradeInfoAggregate) => {
        const key = `${item.companyId}|${item.productCatalogueId}`;
        newDataMap.set(key, item);
      });
      setTradeInfoAggreateMap(newDataMap);
      return result;
    } catch (err) {
      return result;
    }
  };

  const reMap = async () => {
    if (isLoading) return;
    setIsLoading(true);
    setIsCheckoutButtonLoading(true);

    let tempMap = new Map(groupByCompanyMap);
    let totalForEveryCompany = 0;
    let totalDiscountForEveryCompany = 0;
    let totalProductForEveryCompany = 0;

    tempMap.forEach((companyProducts) => {
      companyProducts.map((item: any) => {
        item.companyName = companyMap.get(item.companyId)?.name;
        const product = productMap.get(item.productId);

        // reMap the unit price and totalPrice
        // tradeInfoAggreateMap
        // const key = `${companyProducts[0].companyId}|${item.productId}`;
        // const tradeinfo = tradeInfoAggreateMap.get(key);
        // if (item.unitPrice !== tradeinfo?.sellingPrice) {
        //   item.unitPrice = tradeinfo?.sellingPrice || 0;
        //   item.totalPrice = parseFloat((item.unitPrice * item.quantity).toFixed(2))
        //   // if update the unitprice and total price at the checkout page and it follow the new pricing
        //   // but it need to update back to retailerCart, due to splitorder is getting from the cart
        //   // if it does not update back the totalPrice will be wrong.
        //   apiHelper.PUT("retailerCart?id=" + item.id, item, "", "v2")
        // }

        item.name = product?.name;
        item.code = product?.sku;

        // restructure for the sales order needed payload
        item.price = item.unitPrice;
        item.quantity = parseInt(item.quantity);
        item.checked = true;
        return item;
      });
    });

    onInitCalculatePromotion(tempMap).then(async (res) => {
      // get the promotion and getPromotion
      // let productIds: any = []
      res.forEach((companyProducts) => {
        // productIds = productIds.concat(companyProducts.map((item: CartInterface) => item.productId));

        let totalPriceOrdered = 0;
        let totalDiscountOrdered = 0;
        let totalTaxOrdered = 0;

        let totalPricePreOrder = 0;
        let totalDiscountPreOrder = 0;
        let totalTaxPreOrder = 0;

        companyProducts.map((item: any) => {
          // Calculate total based on sellingType
          if (item.sellingType !== "PREORDER") {
            totalPriceOrdered += item.totalPrice || 0;
            totalDiscountOrdered += item.totalDiscount || 0;
            totalTaxOrdered += item.totalTax || 0;
          } else {
            totalPricePreOrder += item.totalPrice || 0;
            totalDiscountPreOrder += item.totalDiscount || 0;
            totalTaxPreOrder += item.totalTax || 0;
          }
          return item;
        });

        const totalTempMap = new Map(totalValueByCompany);
        totalTempMap.set(companyProducts[0].companyId, {
          totalPriceOrdered,
          totalDiscountOrdered,
          totalForOrder: totalPriceOrdered - (totalDiscountOrdered + totalTaxOrdered),
          totalPricePreOrder,
          totalDiscountPreOrder,
          totalForPreOrder: totalPricePreOrder - (totalDiscountPreOrder + totalTaxPreOrder),
        });

        setTotalValueByCompany(totalTempMap);

        totalForEveryCompany =
          totalForEveryCompany +
          (totalPriceOrdered - totalDiscountOrdered + totalTaxOrdered) +
          (totalPricePreOrder - totalDiscountPreOrder + totalTaxPreOrder);

        totalDiscountForEveryCompany = totalDiscountForEveryCompany + totalDiscountOrdered + totalDiscountPreOrder;

        totalProductForEveryCompany = totalProductForEveryCompany + companyProducts.length;
      });
      // setProductIds(productIds);

      setDisplayMap(res);
      setTotalBottomBar(totalForEveryCompany);
      setDiscountBottomBar(totalDiscountForEveryCompany);
      setTotalItem(totalProductForEveryCompany);

      await submitSplitOrder();
      setIsLoading(false);
      setIsCheckoutButtonLoading(false);
    });
  };

  // get configureSetting
  const getConfigureSetting = async (companyId: string = "") => {
    let params: any = {
      companyId: companyId,
      name: `RetailerWebsiteCheckoutSetting-${retailerAccess?.companyCode}`,
      status: "ACTIVE",
      activeAt: moment().startOf("day").add(1, "millisecond").toISOString(),
    };

    const response: any = await apiHelper.GET(`configurableFields?${encodeParams(params)}`);
    if (response?.items?.length) {
      const items: ConfigurableField = response.items[0];
      if (items) {
        setCheckoutSetting(items);
        return items;
      }
    }

    return {};
  };

  const columns = [
    {
      title: "Product",
      dataIndex: "name",
      key: "name",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.name + " (" + record.code + ")"}</p>;
      },
    },
    {
      title: "Quantity",
      dataIndex: "quantity",
      key: "quantity",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.quantity}</p>;
      },
    },
    {
      title: "Uom",
      dataIndex: "productUOMId",
      key: "productUOMId",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{uomMap.get(record.productUOMId)?.name}</p>;
      },
    },
    {
      title: "Unit Price",
      dataIndex: "unitPrice",
      key: "unitPrice",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign flex justify-end">RM {NumberThousandSeparator(record.unitPrice)}</p>;
      },
    },
    {
      title: t("SalesOrder.discount"),
      dataIndex: "discount",
      key: "discount",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign flex justify-end">{"RM " + NumberThousandSeparator(record.totalDiscount ?? 0)}</p>;
      },
    },
    {
      title: "Tax",
      dataIndex: "totalTax",
      key: "totalTax",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign flex justify-end">RM {NumberThousandSeparator(record.totalTax)}</p>;
      },
    },
    {
      title: "Item Subtotal",
      dataIndex: "totalNetPrice",
      key: "totalNetPrice",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign flex justify-end">RM {NumberThousandSeparator(record.totalNetPrice)}</p>;
      },
    },
  ];

  const submitSplitOrder = async () => {
    if (productIds.length === 0) return;

    // remove duplicated productIds
    // let uniqueProductIds = Array.from(new Set(productIds));

    let tempMap = new Map();
    try {
      while (productIds.length) {
        let dataSubmit = {
          outletId: outletId,
          retailerId: retailerAccess.id || "",
          productIds: productIds.splice(0, 50),
          timeNow: moment().startOf("day").toISOString(),
        };

        const res: any = await apiHelper.POST("retailerCart/splitOrder", dataSubmit, "", "v2");
        if (res) {
          Object.entries(res.items).forEach(([key, value]) => {
            const tempSplitOrderValue: any = value || [];
            const splitOrderValue = tempSplitOrderValue?.map((item: any) => {
              item.checked = true;
              return item;
            });
            if (tempMap.get(key) && tempMap.get(key).length > 0) {
              let array = tempMap.get(key);
              tempMap.set(key, array.concat(splitOrderValue));
            } else tempMap.set(key, splitOrderValue);
          });
        }
        setDeliveryDate(res.cursor);
      }

      onInitCalculatePromotion(tempMap).then((res) => {
        setSplitDataMap(res);
      });
      // return { data: tempMap, deliveryDate: res.cursor };
    } catch (err) {
      // return { data: tempMap, deliveryDate: "" };
    }
  };

  const submitSalesOrder = async () => {
    // setIsProcessingOrderLoading(true)

    try {
      // const { data, deliveryDate } = await submitSplitOrder();
      Array.from(splitDataMap.values()).forEach(async (order) => {
        const orderDetail = displayMap.get(order?.[0]?.companyId);
        console.log("orderDetail: ", orderDetail);

        let totalPriceOrdered = 0;
        let totalDiscountOrdered = 0;
        let totalTaxOrdered = 0;

        let totalPricePreOrder = 0;
        let totalDiscountPreOrder = 0;
        let totalTaxPreOrder = 0;

        order.forEach((val: any) => {
          val.tradeType = productTradeMap.get(val.productId)?.tradeType;
        });

        let orderedItems = order.filter((item: CartInterface) => item.tradeType !== "PREORDER");

        // get all retailerCartId out
        // const orderItemId = orderedItems.map((item: any) => item.id)
        let orderItemId: string[] = [];

        let preOrderItems = order.filter((item: CartInterface) => item.tradeType === "PREORDER");

        // MATCH the foc PRODUCTS
        if (orderedItems?.length) {
          let focProducts: ProductCartUI[] = [];
          let existingProduct: string[] = [];

          orderedItems = orderedItems
            .map((item: ProductCartUI & ProductOrdered & { existedRecord: string }) => {
              orderItemId.push(item?.id ?? "");

              // check whether duplicate product exist
              const newKeyMap =
                item?.companyBranchId +
                item?.productId +
                // item?.productUOMId +
                item?.type;
              // + item?.quantity;

              if (!existingProduct.includes(newKeyMap)) {
                existingProduct.push(newKeyMap);

                // define this line of record is new
                item.existedRecord = "FALSE";

                const { companyId, companyBranchId, promotionIds } = item;
                const focProduct = orderDetail.find(
                  (element: ProductCartUI) =>
                    element?.promotionIds?.find((id: string) => promotionIds?.includes(id)) &&
                    element.companyId === companyId &&
                    element.companyBranchId === companyBranchId &&
                    element.type === "FOC"
                );

                const itself = orderDetail.find(
                  (element: ProductCartUI) =>
                    element?.promotionIds?.find((id: string) => promotionIds?.includes(id)) &&
                    element.companyId === companyId &&
                    element.companyBranchId === companyBranchId &&
                    element.productId == item.productId &&
                    element.productUOMId === element.productUOMId
                );

                if (focProduct) {
                  focProducts.push(focProduct);
                }

                if (itself) {
                  item.promotionApplyIds = itself.promotionApplyIds;
                  item.totalDiscount = itself.totalDiscount;
                }

                totalPriceOrdered += parseFloat(item.totalPrice.toFixed(2)) || 0;
                totalDiscountOrdered += parseFloat(item.totalDiscount.toFixed(2)) || 0;
                totalTaxOrdered += parseFloat(item.totalTax.toFixed(2)) || 0;

                if (item.type !== "FOC") {
                  item.promotionIds = item?.promotionApplyIds || [];
                }

                // setup product sales order payload needed
                item.price = item.unitPrice;
                item.discount = item.totalDiscount;
                item.total = item.unitPrice * item.quantity;
                console.log("item: ", item);

                return item;
              } else {
                // define this line of record is existedRecord
                item.existedRecord = "TRUE";
                return item;
              }
            })
            .filter((item: any) => item && item.existedRecord === "FALSE");
          orderedItems = orderedItems.concat(focProducts).map((item: CartInterface) => {
            if (!item.taxId) {
              delete item.taxId;
            }
            return item;
          });
        }

        if (preOrderItems?.length) {
          let focProducts: ProductCartUI[] = [];
          let existingProduct: string[] = [];

          preOrderItems = preOrderItems
            .map((item: ProductCartUI & ProductOrdered & { existedRecord: string }) => {
              // check whether duplicate product exist
              const newKeyMap =
                item?.companyBranchId +
                item?.productId +
                // item?.productUOMId +
                item?.type;
              // + item?.quantity;
              if (!existingProduct.includes(newKeyMap)) {
                existingProduct.push(newKeyMap);

                // define this line of record is new
                item.existedRecord = "FALSE";

                const { companyId, companyBranchId, promotionIds } = item;
                const focProduct = orderDetail.find(
                  (element: ProductCartUI) =>
                    element?.promotionIds?.find((id: string) => promotionIds?.includes(id)) &&
                    element.companyId === companyId &&
                    element.companyBranchId === companyBranchId &&
                    element.type === "FOC"
                );

                const itself = orderDetail.find(
                  (element: ProductCartUI) =>
                    element?.promotionIds?.find((id: string) => promotionIds?.includes(id)) &&
                    element.companyId === companyId &&
                    element.companyBranchId === companyBranchId &&
                    element.productId == item.productId &&
                    element.productUOMId === element.productUOMId
                );

                if (focProduct) {
                  focProducts.push(focProduct);
                }

                if (itself) {
                  item.promotionApplyIds = itself.promotionApplyIds;
                  item.totalDiscount = itself.totalDiscount;
                }

                totalPricePreOrder += parseFloat(item.totalPrice.toFixed(2)) || 0;
                totalDiscountPreOrder += parseFloat(item.totalDiscount.toFixed(2)) || 0;
                totalTaxPreOrder += parseFloat(item.totalTax.toFixed(2)) || 0;
                if (item.type !== "FOC") {
                  item.promotionIds = item?.promotionApplyIds || [];
                }

                // setup product sales order payload needed
                item.price = item.unitPrice;
                item.discount = parseFloat(item.totalDiscount.toFixed(2));
                item.total = parseFloat((item.unitPrice * item.quantity).toFixed(2));
                item.totalPrice = parseFloat(item.totalPrice.toFixed(2));
                item.totalNetPrice = parseFloat((item.totalNetPrice - item.discount).toFixed(2));
                return item;
              } else {
                // define this line of record is existedRecord
                item.existedRecord = "TRUE";
                return item;
              }
            })
            .filter((item: any) => item && item.existedRecord === "FALSE");

          preOrderItems = preOrderItems.concat(focProducts).map((item: CartInterface) => {
            if (!item.taxId) {
              delete item.taxId;
            }
            return item;
          });
        }

        // orderedItems.map((item: ProductCartUI & ProductOrdered) => {
        //   totalPriceOrdered += parseFloat(item.totalPrice.toFixed(2)) || 0;
        //   totalDiscountOrdered += parseFloat(item.totalDiscount.toFixed(2)) || 0;
        //   totalTaxOrdered += parseFloat(item.totalTax.toFixed(2)) || 0;
        //   if (item.type !== 'FOC') {
        //     item.promotionIds = item?.promotionApplyIds || [];
        //   }

        //   // setup product sales order payload needed
        //   item.price = item.unitPrice;
        //   item.discount = item.totalDiscount;
        //   item.total = item.unitPrice * item.quantity;
        //   return item;
        // });

        // preOrderItems.forEach((item: any) => {
        //   totalPricePreOrder += parseFloat(item.totalPrice.toFixed(2)) || 0;
        //   totalDiscountPreOrder += parseFloat(item.totalDiscount.toFixed(2)) || 0;
        //   totalTaxPreOrder += parseFloat(item.totalTax.toFixed(2)) || 0;
        //   if (item.type !== 'FOC') {
        //     item.promotionIds = item?.promotionApplyIds || [];
        //   }

        //   // setup product sales order payload needed
        //   item.price = item.unitPrice;
        //   item.discount = item.totalDiscount;
        //   item.total = item.unitPrice * item.quantity;
        //   return item;
        // });

        orderedItems.companyBranchId = order[0].companyBranchId;
        orderedItems.totalPriceOrdered = totalPriceOrdered;
        orderedItems.totalDiscountOrdered = totalDiscountOrdered;
        orderedItems.totalTaxOrdered = totalTaxOrdered;

        preOrderItems.companyBranchId = order[0].companyBranchId;
        preOrderItems.totalPricePreOrder = totalPricePreOrder;
        preOrderItems.totalDiscountPreOrder = totalDiscountPreOrder;
        preOrderItems.totalTaxPreOrder = totalTaxPreOrder;
        preOrderItems.supplierCompany = order[0].companyId;

        delete orderedItems.updatedAt;
        delete orderedItems.deletedAt;
        delete orderedItems.createdAt;

        // get all retailer cart id out
        // const preOrderItemId = preOrderItems.map((item: any) => item.id);

        order.forEach((val: any) => {
          if (val.taxId === null) {
            val.taxId === "";
          }
        });

        let sales = false;
        let preorder: boolean = false;
        if (orderedItems.length > 0) {
          console.log("orderedItems: ", orderedItems);
          setIsCheckoutButtonLoading(true);
          sales = await submitNormalOrder(orderedItems, deliveryDate, orderItemId);
        }
        if (preOrderItems.length > 0) {
          setIsCheckoutButtonLoading(true);
          preorder = await submitPreorderProduct(preOrderItems);
        }

        if ((sales || orderedItems.length === 0) && (preorder || preOrderItems.length === 0)) {
          setIsCheckoutButtonLoading(false);
          router.push(`/salesOrder/salesOrderListing`);
        }
      });
    } catch {
      setIsProcessingOrderLoading(false);
    }
  };

  const submitNormalOrder = async (orderDetail: any, deliveryDate: string = "", orderItemId: string[]) => {
    console.log("orderDetail: ", orderDetail);
    // let deleteCart = {
    //   ids: orderItemId,
    // };
    let result = false;
    let dataSubmit: SalesOrder = {
      retailerId: retailerAccess.id,
      outletId: outletId,
      estimatedDeliveredAt: deliveryDate,
      subTotal: orderDetail.totalPriceOrdered,
      subTotalDiscount: orderDetail.totalDiscountOrdered,
      totalTax: orderDetail.totalTaxOrdered,
      currencyId: currency.id,
      currencyRate: currency.currencyRates && currency.currencyRates.length > 0 ? currency.currencyRates[0].sellingRate : 0,
      companyId: retailerAccess.companyId,
      companyBranchId: orderDetail.companyBranchId,
      shippingAddressId: selectedAddress.id,
      productOrdered: orderDetail,
      remark: form.getFieldValue("remark"),
      shippingFee: 0,
      key: "",
      orderDate: new Date().toISOString(),
    };
    console.log("dataSubmit: ", dataSubmit);
    const temp: any = await apiHelper.POST("salesOrder", dataSubmit, "", "v1");
    if (temp.item) {
      MessageSuccessUI(t("Checkout.order") + " " + t("Common.createSuccess"));
      const chunkSize = 50;
      for (let i = 0; i < orderItemId.length; i += chunkSize) {
        const chunk = orderItemId.slice(i, i + chunkSize);
        let deleteCart = {
          ids: chunk,
        };

        try {
          await apiHelper.POST("retailerCart/bulkDelete", deleteCart, "", "v2");
        } catch (error) {}
      }

      result = true;
    } else {
      MessageErrorUI(t("Checkout.order") + " " + t("Common.createUnsuccess"));
      result = false;
    }
    return result;
  };

  const submitPreorderProduct = async (
    orderDetail: any
    // preOrderItemId: string[]
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      setTimeout(async () => {
        const productIds = orderDetail.map((item: ProductCartUI) => item.productId);
        const { preOrderData } = await getPreOrder(productIds);

        let result = false;

        // First, map over the orderDetail to assign preorderSettingId
        orderDetail.map((item: any) => {
          item.preorderSettingId = preOrderData.find((val: any) => val.productIds.includes(item.productId))?.id;
          item.date = preOrderData.find((val: any) => val.productIds.includes(item.productId))?.date;
        });

        // Then, group the items by preorderSettingId
        let groupedById = orderDetail.reduce((acc: any, obj: any) => {
          const key = obj.preorderSettingId;
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key].push(obj);
          return acc;
        }, {});

        // Now, reset the keys of the grouped object to normal indices
        let groupByPreorderSettings: any = Object.keys(groupedById).map((key, index) => ({
          [index]: groupedById[key],
        }));

        // Flatten the result if needed (if you prefer an array of arrays instead of objects with numeric keys)
        groupByPreorderSettings = Object.values(groupedById);

        // if (preOrderData.length && !preOrderData?.id) {
        //   MessageErrorUI(
        //     t("Checkout.order") +
        //       " " +
        //       t("Common.createUnsuccess") +
        //       "due to missing perorder setting for particular product"
        //   );
        //   resolve(result);
        //   return;
        // }

        groupByPreorderSettings.map(async (preorderSetting: any) => {
          let preorderItemId = preorderSetting.map((item: any) => item.id);
          let pricePreOrder = 0;
          let discountPreOrder = 0;
          let taxPreOrder = 0;
          preorderSetting.map((item: any) => {
            pricePreOrder += parseFloat(item.totalPrice.toFixed(2)) || 0;
            discountPreOrder += parseFloat(item.totalDiscount.toFixed(2)) || 0;
            taxPreOrder += parseFloat(item.totalTax.toFixed(2)) || 0;
          });
          const dataSubmit: any = {
            companyId: retailerAccess.companyId,
            // companyBranchId: retailerAccess.companyBranchId,
            companyBranchId: outletData?.companyBranchId ?? "",
            supplierCompanyId: preorderSetting[0].supplierCompanyId,
            retailerId: retailerAccess.id,
            outletId: outletId,
            estimatedDeliveredAt: preorderSetting[0].date,
            totalPrice: parseFloat(pricePreOrder?.toFixed(2)),
            totalDiscount: parseFloat(discountPreOrder?.toFixed(2)),
            totalTax: taxPreOrder,
            totalNetPrice: parseFloat((pricePreOrder - discountPreOrder + taxPreOrder).toFixed(2)),
            currencyId: currency.id,
            shippingAddressId: selectedAddress.id,
            productsPreOrdered: preorderSetting,
            preorderSettingId: preorderSetting[0].preorderSettingId,
            remark: orderDetail.remark,
          };

          const temp: any = await apiHelper.POST("preorder", dataSubmit, "", "v1");
          if (temp.item) {
            MessageSuccessUI(t("Checkout.order") + " " + t("Common.createSuccess"));
            const chunkSize = 50;
            for (let i = 0; i < preorderItemId.length; i += chunkSize) {
              const chunk = preorderItemId.slice(i, i + chunkSize);
              let deleteCart = {
                ids: chunk,
              };

              try {
                await apiHelper.POST("retailerCart/bulkDelete", deleteCart, "", "v2");
              } catch (error) {}
            }
            // router.push(`/salesOrder/salesOrderListing`);
            result = true;
          } else {
            MessageErrorUI(t("Checkout.order") + " " + t("Common.createUnsuccess"));
            result = false;
          }
        });
        resolve(result);
      }, 500);
    });
  };

  // const selectAddress = (value: number | undefined) => {
  //   setSelectAddressVisible(false);
  //   if (value !== undefined) {
  //     const selectedOption = options.find((opt) => opt.value === value);

  //     if (selectedOption) {
  //       setSelectedAddress(value);
  //       setSelectedLabel(selectedOption.label);
  //       setSelectedDescription(selectedOption.description);
  //     }
  //   }
  // };

  const addAddress = (value: any) => {
    // const updatedOptions = [...options, newAddress];
    // setOptions(updatedOptions);
    // setAddAddressVisible(false);
    // selectAddress(newAddress.value);
  };

  // const createNewAddress = (values: any): OptionType => {
  //   return {
  //     id: values.id,
  //     label: values.addressName,
  //     description: "",
  //     personInCharge: values.personInCharge,
  //     phoneNumber: values.contact,
  //     unitNo: values.unitNo,
  //     location: values.location,
  //     address1: values.address1,
  //     address2: values.address2,
  //     country: values.country,
  //     state: values.state,
  //     city: values.city,
  //     postalCode: values.postalCode,
  //     longitude: parseFloat(values.longitude),
  //     latitude: parseFloat(values.latitude),
  //     value: options.length + 1,
  //   };
  // };

  // const editAddress = (value: any) => {
  //   setEditAddressVisible(true);
  //   const selectedOption = shippingAddressMap.get(value);
  //   if (selectedOption) {
  //     setSelectedAddress(selectedOption);
  //   }
  // };

  // const removeAddress = (value: any) => {
  //   const updatedOptions = options.filter((opt) => opt.value !== value.value);
  //   setOptions(updatedOptions);

  //   const nextOption = updatedOptions[0];

  //   if (nextOption) {
  //     setSelectedAddressData(nextOption);
  //     setSelectedAddress(nextOption.value);
  //   } else {
  //     setSelectedAddressData(null);
  //   }
  // };

  const getAddressDetails = (lat: number, lng: number): Promise<Address | null> => {
    return new Promise((resolve) => {
      const latLng = new google.maps.LatLng(lat, lng) as google.maps.LatLng;

      const geocoder = new google.maps.Geocoder();
      geocoder.geocode({ location: latLng }, (results: any, status) => {
        if (status === google.maps.GeocoderStatus.OK && results.length > 0) {
          const addressComponents = results[0].address_components;

          const addressDetails: Address = {
            unitNo: "-",
            address1: "-",
            address2: "-",
            city: "-",
            state: "-",
            country: "-",
            postalCode: "00000",
          };

          addressComponents.forEach((component: any) => {
            const types = component.types;
            if (types.includes("street_number")) {
              addressDetails.unitNo = component.long_name;
            } else if (types.includes("route")) {
              addressDetails.address1 = `${component.long_name}`;
              // address.address1 = `${address.address1} ${component.long_name}`;
            } else if (types.includes("sublocality")) {
              addressDetails.address2 = component.long_name;
            } else if (types.includes("locality")) {
              addressDetails.city = component.long_name;
            } else if (types.includes("administrative_area_level_1")) {
              addressDetails.state = component.short_name;
            } else if (types.includes("country")) {
              addressDetails.country = component.long_name;
            } else if (types.includes("postal_code")) {
              addressDetails.postalCode = component.long_name;
            }
          });

          resolve(addressDetails);
        } else {
          // console.error("Geocoder failed due to: " + status);
          resolve(null); // Pass null to indicate an error or no results
        }
      });
    });
  };

  const handleLocationPinpointed = async (lat: number, lng: number, address: string, type: string) => {
    if (type === "general") {
      // Update the state with the pinpointed coordinates
      const pinpointedLocationObject = {
        lat,
        lng,
        address,
        text: "Pinpointed Location",
      };
      setPinpointedCoordinates(pinpointedLocationObject);
      addForm.setFieldValue("longitude", pinpointedLocationObject.lng?.toFixed(4));
      addForm.setFieldValue("latitude", pinpointedLocationObject.lat?.toFixed(4));

      const addressComponents = address.split(", ");
      if (addressComponents.length >= 2) {
        // Destructure the address components
        const [shippingAddress1, shippingAddress2, fullShippingCity, shippingState] = addressComponents;
        // Split the city into postal code and city (if necessary)
        const shippingCityComponents = fullShippingCity?.split(" ");
        let shippingPostalCode = "";
        let shippingCity = "";

        if (shippingCityComponents?.length >= 2) {
          shippingPostalCode = shippingCityComponents[0];
          shippingCity = shippingCityComponents.slice(1).join(" ");
        } else {
          // If there's no space-separated postal code and city, assume the whole string is the city
          shippingCity = fullShippingCity;
        }

        // Update the state with the pinpointed coordinates
        const pinpointedLocationObject = {
          lat,
          lng,
          address,
          text: "Pinpointed Location",
        };
        setPinpointedCoordinates(pinpointedLocationObject);

        // Call the getAddressDetails function to get the full address
        let addressByGeoCode: Address | null = null;

        try {
          addressByGeoCode = await getAddressDetails(lat, lng);
        } catch (error) {
          console.error("Error getting address details:", error);
        }
        // Set the values in the form fields
        addForm.setFieldValue(
          "address1",
          addressByGeoCode?.address1 === "-" ? shippingAddress1 : (addressByGeoCode?.unitNo ?? "") + " " + addressByGeoCode?.address1
        );
        addForm.setFieldValue("address2", addressByGeoCode?.address2 === "-" ? shippingAddress2 : addressByGeoCode?.address2);

        // Extract the country (assuming it's the last component of the address)
        const shippingCountry = addressComponents[addressComponents.length - 1];

        // Set the country in the form field
        addForm.setFieldValue("country", addressByGeoCode?.country === "-" ? shippingCountry : addressByGeoCode?.country);

        addForm.setFieldValue("state", addressByGeoCode?.state === "-" ? shippingState : addressByGeoCode?.state);
        addForm.setFieldValue("city", addressByGeoCode?.city === "-" ? shippingCity : addressByGeoCode?.city);
        addForm.setFieldValue("postalCode", addressByGeoCode?.postalCode === "-" ? shippingPostalCode : addressByGeoCode?.postalCode);
      }
    }
  };

  const selectDeliveryAddressModal = () => {
    return (
      <Form>
        <p className="text-sm text-gray-400">{t("Checkout.selectAddress")}</p>
        {/* <IconDescriptionButtonUI
          label={t("Checkout.addAddress")}
          onClick={() => {
            setAddAddressVisible(true);
          }}
          icon={<EnvironmentFilled />}
          buttonIcon={<RightOutlined />}
        /> */}
        <div className="">
          <IconDescriptionRadioButtonInput
            icon={<EnvironmentFilled />}
            contentOptions={Array.from(shippingAddressMap.values())}
            value={selectedAddress.id}
            onChange={(value) => {
              if (value) {
                let selectedShippingAddressData = shippingAddressMap.get(value.target.value);
                setSelectedAddress(selectedShippingAddressData);
              }
            }}
            // editButton={{
            //   onClick: (value: any) => editAddress(value),
            // }}
            // removeButton={{

            //   //    onClick: (value: any) => removeAddress(value),
            // }}
          />
        </div>

        <div className="flex justify-end pt-5">
          {/* <div className="pr-5">
            <SecondaryButtonUI
              label={t("Common.cancel")}
              onClick={() => {
                setSelectAddressVisible(false);
              }}
            />
          </div> */}
          <PrimaryButtonUI htmlType="submit" label={t("Common.select")} onClick={() => setSelectAddressVisible(false)} />
        </div>

        <ModalUI
          title={t("Checkout.addAddressTitle")}
          visible={isAddAddressVisible}
          onCancel={() => setAddAddressVisible(false)}
          width={isSmallScreen ? "100%" : "70%"}
          content={deliveryAddressModel()}
        />
        <ModalUI
          title={t("Checkout.editAddressTitle")}
          visible={isEditAddressVisible}
          onCancel={() => setEditAddressVisible(false)}
          width={isSmallScreen ? "100%" : "70%"}
          content={deliveryAddressModel()}
        />
      </Form>
    );
  };

  const deliveryAddressModel = () => {
    return (
      <div className="w-full">
        <Form form={addForm} onFinish={addAddress} className="w-full pt-3.5" layout="vertical" scrollToFirstError>
          <Row className="flex flex-row space-x-4">
            <Form.Item
              label={t("Checkout.addressName")}
              name="addressName"
              className="flex-1 p-2"
              rules={[
                {
                  required: true,
                  message: t("Checkout.validation.addressName"),
                },
              ]}
            >
              <FormTextInput maxLength={100} placeholder="" />
            </Form.Item>
          </Row>

          <Row className="flex flex-row space-x-4">
            <Form.Item
              label={t("Checkout.personInCharge")}
              name="personInCharge"
              className="flex-1 p-2"
              rules={[
                {
                  required: true,
                  message: t("Checkout.validation.personInCharge"),
                },
              ]}
            >
              <FormTextInput maxLength={100} placeholder="" />
            </Form.Item>
            <Form.Item
              label={t("Checkout.phoneNumber")}
              name="contact"
              className="flex-1 p-2"
              rules={[
                {
                  required: true,
                  message: t("Checkout.validation.contactNumber"),
                },
                {
                  validator(_, value) {
                    if (value && !isValidPhoneNumber(value)) {
                      return Promise.reject(new Error(t("Validation.phoneFormat")));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <PhoneNumberInput onChange={(value) => value} />
            </Form.Item>
          </Row>
          <Row className="flex flex-row gap-x-4">
            <Form.Item
              name="gpsLocation"
              className="flex-1"
              // rules={[{ required: true, mess age: t("GPSLocation") + " " + t("Validation.Is.Required") }]}
              label={<p className="text-neutral700 text-[12px]">{t("Login.signUp.outletLocation")}</p>}
            >
              <GoogleMap
                newLat={lat}
                newLng={lng}
                onLocationPinpointed={(lat, lng, address) => handleLocationPinpointed(lat, lng, address, "general")}
                pointLat={0}
                pointLng={0}
              />
              {/* <FormAddressInput maxLength={100} placeholder={""} setAddress={handleAddressInputChange("GPS")} /> */}
            </Form.Item>
          </Row>
          <Row className="flex flex-row space-x-4">
            <Form.Item
              label={t("Checkout.unitNo")}
              name="unitNo"
              className="flex-1 p-2"
              // rules={[
              //   {
              //     required: true,
              //     message: t("Checkout.validation.unitNo"),
              //   },
              // ]}
            >
              <FormTextInput maxLength={100} placeholder="" />
            </Form.Item>
          </Row>

          <Row>
            <Form.Item
              label={t("Checkout.address1")}
              name="address1"
              className="flex-1 p-2"
              rules={[{ required: true, message: t("Checkout.validation.address") }]}
            >
              <FormTextInput maxLength={100} placeholder="" />
            </Form.Item>
          </Row>

          <Row>
            <Form.Item
              label={t("Checkout.address2")}
              name="address2"
              className="flex-1 p-2"
              rules={[{ required: true, message: t("Checkout.validation.address") }]}
            >
              <FormTextInput maxLength={100} placeholder="" />
            </Form.Item>
          </Row>

          <Row className="flex flex-row space-x-4">
            <Form.Item
              label={t("Checkout.city")}
              name="city"
              className="flex-1 p-2"
              rules={[
                {
                  required: true,
                  message: t("Checkout.validation.city"),
                },
              ]}
            >
              <FormTextInput maxLength={100} placeholder="" />
            </Form.Item>
            <Form.Item
              label={t("Checkout.postalCode")}
              name="postalCode"
              className="flex-1 p-2"
              rules={[
                {
                  required: true,
                  message: t("Checkout.validation.postalCode"),
                },
              ]}
            >
              <FormTextInput maxLength={5} placeholder="" />
            </Form.Item>
          </Row>

          <Row className="flex flex-row space-x-4">
            <Form.Item
              label={t("Checkout.state")}
              name="state"
              className="flex-1 p-2"
              rules={[
                {
                  required: true,
                  message: t("Checkout.validation.state"),
                },
              ]}
            >
              <FormTextInput maxLength={100} placeholder="" />
            </Form.Item>
            <Form.Item
              label={t("Checkout.country")}
              name="country"
              className="flex-1 p-2"
              rules={[
                {
                  required: true,
                  message: t("Checkout.validation.country"),
                },
              ]}
            >
              <FormTextInput maxLength={100} placeholder="" />
            </Form.Item>
          </Row>

          <Row className="flex flex-row space-x-4">
            <Form.Item
              label={t("Checkout.longitude")}
              name={"longitude"}
              className="flex-1 p-2"
              rules={[
                {
                  required: true,
                  message: t("Checkout.validation.longitude"),
                },
              ]}
            >
              <FormTextInput maxLength={20} placeholder="" />
            </Form.Item>
            <Form.Item
              label={t("Checkout.latitude")}
              name={"latitude"}
              className="flex-1 p-2"
              rules={[
                {
                  required: true,
                  message: t("Checkout.validation.latitude"),
                },
              ]}
            >
              <FormTextInput maxLength={20} placeholder="" />
            </Form.Item>
          </Row>

          <div className="flex justify-end space-x-3 pt-8">
            <SecondaryButtonUI
              htmlType="reset"
              label={t("Common.cancel")}
              onClick={() => {
                setAddAddressVisible(false);
              }}
            />

            <PrimaryButtonUI
              htmlType="submit"
              label={t("Common.save")}
              onClick={() => {
                addForm.validateFields().then();
              }}
            />
          </div>
        </Form>
      </div>
    );
  };

  const showContent = () => {
    return (
      <div>
        <BackButtonUI title={t("Checkout.checkout")} buttons={[]}></BackButtonUI>

        <Row className="mt-2 mb-5">
          <IconDescriptionButtonUI
            label={
              selectedAddress && selectedAddress.shippingAddressDescription
                ? selectedAddress.shippingAddressDescription
                : t("Checkout.selectAddress")
            }
            onClick={() => setSelectAddressVisible(true)}
            icon={<EnvironmentFilled />}
            buttonIcon={<RightOutlined />}
            description={selectedAddress && selectedAddress.description ? selectedAddress.description : ""}
          />
          <ModalUI
            className="font-bold"
            title={t("Checkout.deliveryAddress")}
            visible={isSelectAddressVisible}
            onCancel={() => setSelectAddressVisible(false)}
            width={isSmallScreen ? "100%" : "70%"}
            content={selectDeliveryAddressModal()}
          />
        </Row>
        <Row>
          {Array.from(displayMap.values()).map((companyProducts, index) => {
            const orderedItems = _.sortBy(
              companyProducts.filter((item: any) => item.sellingType !== "PREORDER"),
              "name"
            );

            const preOrderItems = _.sortBy(
              companyProducts.filter((item: any) => item.sellingType === "PREORDER"),
              "name"
            );
            return (
              <div className="w-full mb-4">
                {/* <Card className="w-full"> */}
                <Collapse defaultActiveKey={["orderedItems"]} className="w-full">
                  <Panel
                    header={<span className="font-bold"> {companyMap.get(companyProducts[0].companyId)?.name}</span>}
                    key="orderedItems"
                    className="w-full"
                  >
                    <div className="w-full ">
                      {/* order items */}
                      {orderedItems.length > 0 ? (
                        <div className="px-4">
                          <p className="mb-4 font-bold text-[20px]">{t("Checkout.orderItems")}</p>
                          <ListingTableUI dataSource={orderedItems} columns={columns} endMessage={""} />
                          <div className="flex flex-col w-full bg-lightGrey p-3">
                            {/* remark form */}
                            <Row className="justify-between pb-5 ">
                              <Form className="w-full " form={form} layout="horizontal" scrollToFirstError>
                                <Row className="w-full  ">
                                  <Row className="w-full flex md:flex-row flex-col gap-x-4 gap-y-2 ">
                                    <Form.Item
                                      name={"remark"}
                                      className="flex-1"
                                      /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                                        <p className="text-neutral700 text-[12px]">{t("Remark")}</p>
                                      }
                                    >
                                      <DebounceFilterTextInput
                                        placeholder={t("PlaceHolder.remark")}
                                        maxLength={100}
                                        debounceTime={300}
                                        onDebouncedChange={(val) => {
                                          let tempMap = new Map(displayMap);
                                          const companyData = displayMap.get(companyProducts[0].companyId);

                                          if (companyData) {
                                            companyData.remark = val;

                                            tempMap.set(companyProducts[0].companyId, companyData);

                                            setDisplayMap(tempMap);
                                          }
                                        }}
                                      />
                                    </Form.Item>
                                    <Col className="md:w-2/6 w-full">
                                      <Form.Item
                                        name={"returnDate"}
                                        className="flex-1"
                                        /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/
                                      >
                                        <SingleDateInput
                                          placeholder={`Estimate delivery: Received by ${formateDate(deliveryDate)}`}
                                          onChange={() => {}}
                                          disabled
                                        />
                                      </Form.Item>
                                    </Col>
                                    <Col className="flex items-center">
                                      <p className="font-bold text-[16px] pr-2 md:flex hidden">
                                        RM {NumberThousandSeparator(totalValueByCompany.get(orderedItems[0].companyId).totalForOrder)}
                                      </p>
                                    </Col>
                                  </Row>
                                </Row>
                              </Form>
                            </Row>
                            {/* order detail */}

                            <div>
                              <p className="font-bold mb-2 text-[16px]">{t("Checkout.orderDetail")}</p>
                              <Row className="flex justify-between">
                                <p>{t("Checkout.subTotal")}</p>
                                <p className="">
                                  RM {/* subtotal */}
                                  {NumberThousandSeparator(
                                    totalValueByCompany.get(orderedItems[0].companyId).totalPriceOrdered
                                    // companyProducts.totalPriceOrdered
                                  )}
                                </p>
                              </Row>
                              <Row className="flex justify-between">
                                <p>{t("Checkout.totalDiscount")}</p>
                                <p className="discount-color">
                                  - RM {/* discount */}
                                  {NumberThousandSeparator(
                                    totalValueByCompany.get(orderedItems[0].companyId).totalDiscountOrdered
                                    // companyProducts.totalDiscountOrdered
                                  )}
                                </p>
                              </Row>
                              <Row className="flex justify-between">
                                <p>{t("Checkout.shippingFee")}</p>
                                <p className="">RM 0.00</p>
                              </Row>
                              <Row className="flex justify-between">
                                <p>{t("Checkout.totalTax")}</p>
                                <p className="">
                                  {" "}
                                  RM {/* total tax */}
                                  {NumberThousandSeparator(
                                    totalValueByCompany.get(orderedItems[0].companyId).totalTaxOrdered
                                    // companyProducts.totalTaxOrdered
                                  )}
                                </p>
                              </Row>
                              <div className="semiBold-horizontal-divider"> </div>
                              <Row className="flex justify-between">
                                <p className="font-bold text-[16px]">{t("Checkout.totalAmount")}</p>
                                <p className="font-bold text-[16px]">
                                  RM{" "}
                                  {NumberThousandSeparator(
                                    totalValueByCompany.get(orderedItems[0].companyId).totalForOrder
                                    // companyProducts.totalForOrder
                                  )}
                                </p>
                              </Row>
                            </div>
                          </div>
                        </div>
                      ) : null}

                      {/* pre-order item */}
                      {preOrderItems.length > 0 ? (
                        <div>
                          <div className="bold-horizontal-divider"> </div>
                          <div className="px-4">
                            <p className="mb-4 font-bold text-[20px]">{t("Checkout.pre-OrderItems")}</p>
                            <ListingTableUI dataSource={preOrderItems} columns={columns} endMessage={""} />
                            <div className="flex flex-col w-full bg-lightGrey p-3">
                              {/* remark form */}
                              <Row className="justify-between pb-5">
                                <Form className="w-full " form={form} layout="horizontal" scrollToFirstError>
                                  <Row className="w-full  ">
                                    <Row className="w-full flex md:flex-row flex-col gap-x-4 gap-y-2">
                                      <Form.Item
                                        name={"remark"}
                                        className="flex-1"
                                        /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                                          <p className="text-neutral700 text-[12px]">{t("Remark")}</p>
                                        }
                                      >
                                        <DebounceFilterTextInput
                                          placeholder={t("PlaceHolder.remark")}
                                          maxLength={100}
                                          debounceTime={300}
                                          onDebouncedChange={(val) => {
                                            let tempMap = new Map(displayMap);
                                            const companyData = displayMap.get(companyProducts[0].companyId);

                                            if (companyData) {
                                              companyData.remark = val;

                                              tempMap.set(companyProducts[0].companyId, companyData);

                                              setDisplayMap(tempMap);
                                            }
                                          }}
                                        />
                                      </Form.Item>
                                      <Col className="md:w-2/6 w-full">
                                        <Form.Item
                                          name={"returnDate"}
                                          className="flex-1 "
                                          /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/
                                        >
                                          <SingleDateInput
                                            placeholder={`Estimate delivery: Received by ${formateDate(preOrderDeliveryDate)}`}
                                            onChange={() => {}}
                                            disabled
                                          />
                                        </Form.Item>
                                      </Col>
                                      <Col className="flex items-center">
                                        <p className="font-bold text-[16px] pr-2 md:flex hidden">
                                          RM{" "}
                                          {NumberThousandSeparator(
                                            totalValueByCompany.get(preOrderItems[0].companyId).totalForPreOrder
                                          )}
                                        </p>
                                      </Col>
                                    </Row>
                                  </Row>
                                </Form>
                              </Row>
                              {/* order detail */}
                              <div>
                                <p className="font-bold mb-2 text-[16px]">{t("Checkout.orderDetail")}</p>
                                <Row className="flex justify-between">
                                  <p>{t("Checkout.subTotal")}</p>
                                  <p className="">
                                    {" "}
                                    RM{" "}
                                    {NumberThousandSeparator(
                                      totalValueByCompany.get(preOrderItems[0].companyId).totalPricePreOrder
                                      // companyProducts.totalPricePreOrder
                                    )}
                                  </p>
                                </Row>
                                <Row className="flex justify-between">
                                  <p>{t("Checkout.totalDiscount")}</p>
                                  <p className="discount-color">
                                    - RM{" "}
                                    {NumberThousandSeparator(
                                      totalValueByCompany.get(preOrderItems[0].companyId).totalDiscountPreOrder
                                      // companyProducts.totalDiscountPreOrder
                                    )}
                                  </p>
                                </Row>
                                <Row className="flex justify-between">
                                  <p>{t("Checkout.shippingFee")}</p>
                                  <p className="">RM 0.00</p>
                                </Row>
                                <Row className="flex justify-between">
                                  <p>{t("Checkout.totalTax")}</p>
                                  <p className="">
                                    {" "}
                                    RM{" "}
                                    {NumberThousandSeparator(
                                      totalValueByCompany.get(preOrderItems[0].companyId).totalTaxPreOrder
                                      // companyProducts.totalTaxPreOrder
                                    )}
                                  </p>
                                </Row>
                                <div className="semiBold-horizontal-divider"> </div>
                                <Row className="flex justify-between">
                                  <p className="font-bold text-[16px]">{t("Checkout.totalAmount")}</p>
                                  <p className="font-bold text-[16px]">
                                    {" "}
                                    RM{" "}
                                    {NumberThousandSeparator(
                                      totalValueByCompany.get(preOrderItems[0].companyId).totalForPreOrder
                                      // companyProducts.totalForPreOrder
                                    )}
                                  </p>
                                </Row>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : null}
                    </div>
                  </Panel>
                </Collapse>
                {/* </Card> */}
              </div>
            );
          })}
        </Row>
      </div>
    );
  };

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => {}} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">{showContent()}</Content>
      <FixedBottomBar
        buttonDisabled={Object.keys(selectedAddress).length > 0 || isAutoCheckout ? false : true}
        quantityItem={totalItem}
        totalPrice={totalBottomBar}
        savedPrice={discountBottomBar}
        onclick={async () => {
          const configureSettingIsOn: any = await getConfigureSetting(retailerAccess?.companyId ?? "");

          configureSettingIsOn && Object.keys(configureSettingIsOn).length
            ? // modal display where holiday
              ModalInfoUI({
                title: t("Notice"),
                content: <div dangerouslySetInnerHTML={{ __html: configureSettingIsOn.value }} />,
                okText: t("Common.ok"),
                cancelText: t("Common.cancel"),
                onOk: () => {
                  setIsCheckoutButtonLoading(false);
                },
                onCancel: () => {
                  setIsCheckoutButtonLoading(false);
                },
              })
            : // delete till here
            !isAutoCheckout
            ? submitSalesOrder()
            : ModalInfoUI({
                title: t("Notice"),
                content: (
                  <div>
                    <p>
                      We would like to kindly inform you that, you are under scheduled order placing, this order will be process on:{" "}
                      <b>{nextQualifiedDate} - 12:00pm</b>.
                    </p>
                    <br />
                    <p>Please note that all product prices and promotions will be applied based on the checkout date.</p>
                    <br />
                    <p>We sincerely appreciate your understanding and patience regarding this matter.</p>
                  </div>
                ),
                okText: t("Common.ok"),
                cancelText: t("Common.cancel"),
                onOk: () => {},
                onCancel: () => {},
              });
        }}
        buttonLoading={isCheckoutButtonLoading || isProcessingOrderLoading}
      />
      <div className="app-footer">
        <AppFooter retailerAccessValues={retailerAccess} />
      </div>
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ["common"], null, supportedLocales)),
    },
  };
}

export default Checkout;
