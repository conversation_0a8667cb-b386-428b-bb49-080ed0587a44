import PhoneInput from "react-phone-number-input";
import { E164Number } from "libphonenumber-js";
import { Input, Checkbox, Select, InputNumber, Radio, DatePicker } from "antd";
import { EditButtonUI, RemoveButtonUI, IconLabelButtonProps } from "./buttonUI";
import dayjs, { Dayjs } from "dayjs";
import { useCallback, useEffect, useState } from "react";
import { OutletShippingAddress } from "./type";
import { DeleteFilled, EditFilled } from "@ant-design/icons";
import { Moment } from "moment";

const { RangePicker } = DatePicker;

interface PhoneNumberProps {
  value?: string;
  onChange: (value: E164Number) => void;
  className?: string;
  disabled?: boolean;
}

interface PasswordProps {
  disabled?: boolean;
  maxLength: number;
  placeholder: string;
  onChange?: (value: React.ChangeEvent<HTMLInputElement>) => void;
}

interface CheckboxProps {
  label?: string;
  checked?: boolean;
  defaultChecked?: boolean;
  disabled?: boolean;
  onChange?: (value: any) => void;
  children?: React.ReactNode;
  className?: string;
  indeterminate?: boolean;
}

interface TextProps {
  defaultValue?: string;
  disabled?: boolean;
  maxLength: number;
  minLength?: number;
  placeholder: string;
  type?: string;
  value?: string;
  onChange?: (value: React.ChangeEvent<HTMLInputElement>) => void;
  prefix?: React.ReactNode;
  className?: string;
  ref?: any;
  onInput?: (value: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (value: React.ChangeEvent<HTMLInputElement>) => void;
  onPressEnter?: (value: React.ChangeEvent<HTMLInputElement>) => void;
  suffix?: any;
  id?: string;
  debounceTime?: number;
  onDebouncedChange?: ((value: string) => void) | undefined;
}

interface SelectProps {
  options: any[];
  placeholder?: string;
  onChange?: (value: any) => void;
  onSearch?: (value: any) => void;
  onClick?: (value: any) => void;
  onFocus?: (value: any) => void;
  loading?: boolean;
  disabled?: boolean;
  allowClear?: boolean;
  mode?: "multiple" | "tags";
  showArrow?: boolean;
  value?: string | number | null | boolean | undefined | string[];
  defaultValue?: string | number | string[];
  className?: string;
  autoClearSearchValue?: boolean;
  notFoundContent?: any;
}

interface NumberProps {
  defaultValue?: number;
  disabled?: boolean;
  value?: number;
  max?: number;
  min?: number;
  onChange?: (value: any) => void;
  onBlur?: (value: any) => void;
  bordered?: boolean;
  placeholder?: string;
  formatter?:
    | ((
        value: number | undefined,
        info: { userTyping: boolean; input: string }
      ) => string)
    | undefined;
  precision?: number;
  step?: number;
  ref?: any;
  className?: string;
  parser?: any;
  style?: any;
}

interface RadioProps {
  value?: number | string;
  option?: any[];
  contentOptions?: {
    label: string;
    description: string;
    value: number | string;
  }[];
  onClick?: (value: any) => void;
  onChange?: (value: any) => void;
  defaultValue?: any;
  disabled?: boolean;
  className?: string;
  icon?: React.ReactNode;
  editButton?: IconLabelButtonProps;
  removeButton?: IconLabelButtonProps;
}

interface SingleDateInputProps {
  value?: any;
  defaultValue?: any;
  disabledDate?: (currentDate: dayjs.Dayjs | null) => boolean;
  disabled?: boolean;
  className?: string;
  onChange?: (value: any) => void;
  placeholder?: string;
}

interface RangePickerProps {
  // disabledDate?: (date: Moment) => boolean;
  disabledDate?: any;
  className?: string;
  onChange?: (value: any) => void;
  onCalendarChange?: (value: any) => void;
  onBlur?: (value: any) => void;
  value?: any;
  defaultValue?: any;
  placeholder1?: string;
  placeholder2?: string;
  disabled?: boolean;
  allowClear?: boolean;
}

export const PhoneNumberInput: React.FC<PhoneNumberProps> = ({
  onChange,
  className,
  value,
  disabled,
}) => {
  const inputClassName = disabled
    ? "cursor-no-drop textInput1 max-h-9 text-[13px] w-full bg-gray-100"
    : `textInput text-[13px] w-full h-[36px] ${className}`;
  return (
    <PhoneInput
      defaultCountry="MY"
      international={true}
      value={value}
      onChange={onChange}
      className={className ? className : inputClassName}
      countryCallingCodeEditable={false}
      limitMaxLength={true}
      disabled={disabled}
    />
  );
};

export const PasswordInput: React.FC<PasswordProps> = ({
  disabled,
  maxLength,
  placeholder,
  onChange,
}) => (
  <Input.Password
    disabled={disabled}
    maxLength={maxLength}
    placeholder={placeholder}
    onChange={onChange}
    className="textInput max-h-[36px]"
  />
);

export const CheckboxInput: React.FC<CheckboxProps> = ({
  label,
  checked,
  defaultChecked,
  disabled,
  onChange,
  indeterminate,
}) => (
  <Checkbox
    checked={checked}
    defaultChecked={defaultChecked}
    disabled={disabled}
    onChange={onChange}
    className="checkBoxDesign textDescription font-semibold items-baseline flex"
    indeterminate={indeterminate}
  >
    {label}
  </Checkbox>
);

export const FormTextInput: React.FC<TextProps> = ({
  className,
  defaultValue,
  disabled,
  maxLength,
  placeholder,
  type,
  value,
  ref,
  onChange,
}) => (
  <Input
    defaultValue={defaultValue}
    ref={ref}
    disabled={disabled}
    maxLength={maxLength}
    placeholder={placeholder}
    type={type}
    value={value}
    onChange={onChange}
    className={className ? className : "formTextInput"}
  />
);

export const SelectInput: React.FC<SelectProps> = ({
  loading,
  value,
  options,
  onChange,
  placeholder,
  onClick,
  onSearch,
  mode,
  allowClear = true,
  showArrow,
  disabled,
  defaultValue,
  className,
  onFocus,
}) => {
  let inputClassName = className
    ? `singleSelectorInput customSelectDropdown ${className}`
    : "singleSelectorInput customSelectDropdown";
  disabled ? inputClassName : (inputClassName = "singleSelectorInput");

  return (
    <Select
      loading={loading}
      value={value}
      mode={mode}
      showSearch
      allowClear={allowClear}
      options={options}
      onChange={onChange}
      onFocus={onFocus}
      onClick={onClick}
      placeholder={placeholder}
      disabled={disabled}
      onSearch={onSearch}
      // showArrow={showArrow}
      defaultValue={defaultValue}
      filterOption={(input, option) =>
        (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
      }
      className={inputClassName}
    />
  );
};

export const NumberInput: React.FC<NumberProps> = ({
  defaultValue,
  value,
  disabled,
  max,
  min,
  onChange,
  bordered,
  placeholder,
  precision,
  step,
  style,
}) => (
  <InputNumber
    controls={false}
    defaultValue={defaultValue}
    disabled={disabled}
    max={max}
    min={min}
    onChange={onChange}
    bordered={bordered}
    placeholder={placeholder}
    value={value}
    step={step}
    precision={precision}
    className="numberInput w-full"
    style={style}
  />
);

export const FormNumberInput: React.FC<NumberProps> = ({
  defaultValue,
  value,
  disabled,
  max,
  min,
  onChange,
  onBlur,
  bordered,
  placeholder,
  precision,
  step,
  className,
}) => (
  <InputNumber
    defaultValue={defaultValue}
    value={value}
    disabled={disabled}
    max={max && max}
    min={min}
    onChange={onChange}
    precision={precision}
    bordered={bordered}
    placeholder={placeholder}
    step={step}
    controls={false}
    onBlur={onBlur}
    className={className + " numberInput "}
  />
);

export const RadioButtonInput: React.FC<RadioProps> = ({
  defaultValue,
  value,
  option,
  onChange,
  disabled,
  className,
}) => (
  <Radio.Group
    defaultValue={defaultValue}
    value={value}
    options={option}
    onChange={onChange}
    className={"radioDesign " + className}
    disabled={disabled}
  />
);

export const IconDescriptionRadioButtonInput: React.FC<RadioProps> = ({
  value,
  contentOptions,
  onChange,
  defaultValue,
  disabled,
  className,
  icon,
  editButton,
  removeButton,
}) => {
  return (
    <div className={`w-full h-auto ${className}`}>
      {contentOptions?.map((opt: OutletShippingAddress) => {
        return (
          <div
            key={opt.id}
            className="w-full p-2 flex items-start pt-5 hover:bg-gray-100"
          >
            <div className="pt-1 pr-2 text-gray-300">{icon}</div>
            <div className="w-[90%]">
              <p className="text-[20px]">
                {opt.shippingAddressDescription ?? ""}
              </p>
              <p className="text-gray-400 font-normal">{opt.description}</p>
              <div className="flex">
                {editButton ? (
                  <EditButtonUI
                    label={"Edit"}
                    icon={<EditFilled />}
                    onClick={() => editButton.onClick(opt)}
                  />
                ) : null}
                {removeButton ? (
                  <RemoveButtonUI
                    label={"Remaove"}
                    icon={<DeleteFilled />}
                    onClick={() => removeButton.onClick(opt)}
                  />
                ) : null}
              </div>
            </div>
            <div className="ml-auto flex items-center self-center">
              <div className="radioWrapper">
                <input
                  type="radio"
                  className="radioInput "
                  value={opt.id}
                  checked={value === opt.id}
                  onChange={onChange}
                  disabled={disabled}
                />
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export const SingleDateInput: React.FC<SingleDateInputProps> = ({
  value,
  defaultValue,
  disabledDate,
  disabled,
  className,
  placeholder,
  onChange,
}) => (
  <DatePicker
    placeholder={placeholder}
    onChange={onChange}
    value={value}
    defaultValue={defaultValue}
    disabledDate={disabledDate}
    disabled={disabled}
    className={`dateInput ${className}`}
  />
);

export const ColumnSingleDateInput: React.FC<SingleDateInputProps> = ({
  value,
  defaultValue,
  disabledDate,
  disabled,
  className,
  placeholder,
  onChange,
}) => (
  <DatePicker
    placeholder={placeholder}
    onChange={onChange}
    value={value}
    defaultValue={defaultValue}
    disabledDate={disabledDate}
    disabled={disabled}
    className={`columnSingleDateInput ${className}`}
  />
);

export const DebounceFilterTextInput: React.FC<TextProps> = ({
  value,
  defaultValue,
  disabled,
  maxLength,
  placeholder,
  debounceTime,
  onDebouncedChange,
  prefix,
  suffix,
}) => {
  const [value1, setValue1] = useState("");

  useEffect(() => {
    setValue1(value || "");
  }, [value]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;
    setValue1(inputValue);

    // Call onDebouncedChange whenever the input value changes\
    if (inputValue === "" && onDebouncedChange) {
      onDebouncedChange(inputValue);
    }

    if (onDebouncedChange) {
      debounceChange(inputValue);
    }
  };

  const debounceChange = useCallback(
    debounce((inputValue: string) => {
      if (onDebouncedChange) {
        onDebouncedChange(inputValue);
      }
    }, debounceTime || 0),
    [onDebouncedChange, debounceTime]
  );

  function debounce(func: (inputValue: string) => void, delay: number) {
    let timeoutId: NodeJS.Timeout;
    return function (inputValue: string) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(inputValue), delay);
    };
  }

  return (
    <Input
      prefix={prefix}
      suffix={suffix}
      defaultValue={defaultValue}
      disabled={disabled}
      type="text"
      placeholder={placeholder}
      maxLength={maxLength || 20} // Defaulting to 20 if maxLength is not provided
      onChange={handleInputChange}
      value={value1}
      className="filterTextInput sm:h-full h-6"
      allowClear
    />
  );
};

export const RangePickerInput: React.FC<RangePickerProps> = ({
  allowClear,
  disabled,
  disabledDate,
  onChange,
  onCalendarChange,
  onBlur,
  value,
  defaultValue,
  placeholder1,
  placeholder2,
  className,
}) => (
  <RangePicker
    disabled={disabled}
    allowClear={allowClear}
    disabledDate={disabledDate}
    className={className ? className : "dateInput"}
    onChange={onChange}
    onBlur={onBlur}
    onCalendarChange={onCalendarChange}
    defaultValue={defaultValue}
    value={value}
    placeholder={[placeholder1 ?? "Start Date", placeholder2 ?? "End Date"]}
    format={"DD/MM/YYYY"}
  />
);
