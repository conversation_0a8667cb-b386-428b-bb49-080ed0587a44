import { message } from "antd";
import axios from "axios";
import { API_URL_V1, API_URL_V2 } from "../../components/config";
import useRetailerStore from "../../stores/store";

const defaultHeaders = {
  "Content-Type": "application/json",
};

let isAPIFailed = true;
let errorCode = 0;
let isFirstApiVersionSet = false;

const fetching = (url: string, method: string, body?: Object | JSON, customHeaders?: Object, apiVersion?: string, ) => {
  // if(!url) {
  //   return new Promise((resolve, reject) => {
  //     window.location.reload()
  //     resolve(true)
  //   })
  // }
  
  let authHeaders = {};
  let token = localStorage.getItem("accessToken");
  if (!url.includes("login") && !url.includes("resetOTP") && !url.includes("validateOTP") && !url.includes("registeredRetailer") && !url.includes("signUp") && !url.includes("uploadFile")&& !url.includes("retailer/verifyOTP")&& !url.includes("retailer/troubleshootingAccess") && !url.includes("payment/fpxResult")) {
    if (token !== undefined && token !== null) {
      authHeaders = {
        Authorization: "Bearer " + token,
      };
    }
    else {
      redirectToLogin("Session Expired. Please login again");
    }
  }

  // const API_URL = apiVersion === "v1" ? API_URL_V1 : API_URL_V2;
  const API_URL = apiVersion ==="CUSTOM" ? "" : apiVersion === "v1" ? API_URL_V1 : API_URL_V2;
  const headers = Object.assign({}, defaultHeaders, authHeaders, customHeaders);
  let config = {
    method: method,
    url: API_URL + url,
    headers: headers,
    data: body,
  };

  return Promise.race([
    fetchAPI(config),
    new Promise((_resolve, reject) => {
      setTimeout(() => {
        if (isAPIFailed && errorCode >= 400) {
          redirectToHomePage(errorCode);
        }
        reject("timeout");
      }, 90000);
    }),
  ]);
};

const promptError = (errorMessage: string) => {
  message.error({
    content: errorMessage,
    style: {
      fontSize: "16px",
    },
    duration: 8,
  });
};

const redirectToHomePage = (errorCode: number) => {
  promptError("The server encountered a temporary error. Please try again in 30 seconds.");
  window.location.replace("/404/" + errorCode);
};

const redirectToLogin = (errorMessage: string) => {
  promptError(errorMessage);
  window.location.replace("/login");
  return;
};

const fetchAPI = (config: any) => {
  // The request was made but no response was received
  return new Promise((resolve, reject) => {
    axios(config)
      .then((res) => {
        isAPIFailed = false;
        
        // if (config.method === "POST" && config.url.includes("/paymentListing") && res.status === 200) {
        //   console.log('me here')
        //   window.location.reload()
        // }
        resolve(res.data);
      })
      .catch((err) => {
        if (!useRetailerStore.getState().error) {
          useRetailerStore.setState({ error: true, err: err });
          if (useRetailerStore.getState().isCheckingAdmin) {
            useRetailerStore.getState().displayError();
          }
          setTimeout(() => {
            useRetailerStore.getState().reset();
          }, 3000);
        }

        // // Check if redirection is needed based on the error status
        // if (config.method === "POST" && err.response && err.response.status === 502 && config.url === "https://yltc.neuroforce.zappit.com.my/retailer/payment/paymentListing") {
        //   window.location.reload()
        // }

        reject(err);
      });
  });
};

const GET = (uri: string, body?: Object | JSON, customHeaders?: Object, apiVersion?: string ) => fetching(uri, "GET", body, customHeaders, apiVersion || "v1");
const POST = (uri: string, body: Object | JSON, customHeaders?: Object, apiVersion?: string) => fetching(uri, "POST", body, customHeaders, apiVersion || "v1");
const PATCH = (uri: string, body: Object | JSON, customHeaders?: Object, apiVersion?: string) => fetching(uri, "PATCH", body, customHeaders, apiVersion || "v1");
const PUT = (uri: string, body: Object | JSON, customHeaders?: Object, apiVersion?: string) => fetching(uri, "PUT", body, customHeaders, apiVersion || "v1");
const DELETE = (uri: string, body: Object | JSON, customHeaders?: Object, apiVersion?: string) => fetching(uri, "DELETE", body, customHeaders, apiVersion || "v1");

export default {
  GET,
  POST,
  PATCH,
  PUT,
  DELETE,
};
