import React, { useState, useEffect, useRef, ChangeEvent } from "react";
import { Content } from "antd/lib/layout/layout";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { Col, Form, Row } from "antd";
import { VictoryPie } from "victory";
import User from "../../assets/logo/user.svg";
import Company from "../../assets/logo/company.svg";
import Help from "../../assets/logo/help.svg";
import Logout from "../../assets/logo/logout.svg";
import Header from "../../components/header";
import { supportedLocales } from "../../components/header";
import { ModalUI } from "@/components/modalUI";
import {
  FormTextInput,
  NumberInput,
  PasswordInput,
  PhoneNumberInput,
  SelectInput,
  RadioButtonInput,
} from "@/components/input";
import { isValidPhoneNumber } from "react-phone-number-input";
import { isSpecial<PERSON>haracter, isValidEmail } from "../api/checkHelper";
import { UploadPicture } from "@/components/upload";
import { isValidPasswordPattern } from "@/stores/utilize";
import { useRouter } from "next/router";
import { LargeClickableCard } from "@/components/card";
import useRetailerStore from "@/stores/store";
import {
  Retailer,
  Address,
  SelectOption,
  googleMap,
  Outlet,
} from "@/components/type";
import {
  DataSource,
  encodeParams,
  compareDifferent2,
  PicSignedUrl,
} from "@/stores/utilize";
import { PrimaryButtonUI, SecondaryButtonUI } from "@/components/buttonUI";
import {
  eInoviceStateSelector,
  eInvoiceCountryCodeSelector,
  eInvoiceGeneralTIN,
  trueFalseOption,
} from "@/components/config";
import GoogleMap from "@/components/googleMap";
import Upload, { RcFile, UploadChangeParam, UploadFile } from "antd/lib/upload";
import { MessageErrorUI, MessageSuccessUI } from "../../components/ui";
import { PlusOutlined } from "@ant-design/icons";
import apiHelper from "../api/apiHelper";
import _ from "lodash";
import AppFooter from "@/components/footer";
import hashPassword from "@/stores/hashPassword";
import TaxIncomeNumberChecker from "@/components/tinNumberCheckerComponent";

function Profile() {
  const { t } = useTranslation("common");
  const router = useRouter();

  // Form
  const [profileForm] = Form.useForm();
  const [outletForm] = Form.useForm();
  const [helpForm] = Form.useForm();

  // Modal
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [isOutletModalOpen, setIsOutletModalOpen] = useState(false);
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);

  // File Upload
  const [uploadedFile, setUploadedFile] = useState(null);

  // Phone Number
  const [phoneNumber, setPhoneNumber] = useState({});

  // Image
  const [previewImage, setPreviewImage] = useState<UploadFile[]>([]);
  const [picture, setPicture] = useState<any[]>([]);
  const [outletPreview, setOutletPreview] = useState<UploadFile[]>([]);

  // Tax Examption
  const [isTaxable, setIsTaxable] = useState("");

  // Retailer Access
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});

  // Set Data
  const [retailerData, setRetailerData] = useState<Retailer>({});
  const [outletData, setOutletData] = useState<Outlet>({});

  const [dataStatus, setDataStatus] = useState(false);

  // Outlet Address
  const [pinpointedCoordinates, setPinpointedCoordinates] = useState<googleMap>(
    {}
  );
  const [lat, setLat] = useState(0); // Initialize with a default value
  const [lng, setLng] = useState(0); // Initialize with a default value
  const [pointCord, setPointCord] = useState({ lat: 0, lng: 0 });
  const [isMalaysia, setIsMalaysia] = useState(false);

  const [businessRegistrationNumber, setBusinessRegistrationNumber] =
    useState("");
  const [requiredEInvoice, setrequiredEInvoice] = useState("FALSE");

  // Check Password Not Empty
  const [isPasswordNotEmpty, setIsPasswordNotEmpty] = useState(false);

  const [isSmallScreen, setIsSmallScreen] = useState(false);

  // Header Path
  const headerItems = [
    {
      label: t("Setting.setting"),
      route: "/profile/setting",
      className: "labelTextStyle",
    },
  ];

  // Retailer Access - User Effect
  useEffect(() => {
    if (router.isReady) {
      getRetailer();
    }
  }, [router.isReady]);

  useEffect(() => {
    const passwordInput = document.getElementById(
      "password-input"
    ) as HTMLInputElement;
    if (isPasswordNotEmpty && passwordInput) {
      passwordInput.focus();
    }
  }, [isPasswordNotEmpty]);

  useEffect(() => {
    // Function to check screen size and update state
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 950); // Define your breakpoint for small screens
    };

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Call handleResize initially to set initial screen size
    handleResize();

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // API CALL //

  //get retailer latest data
  const getRetailer = async (id: string[] = []) => {
    try {
      const dataSource = new DataSource("retailer", "", false, "v2");
      const res: any = await dataSource.load();

      if (res !== null) {
        setRetailerData(res);
        setRetailerFormValues(res);
        let currentOutletId = localStorage.getItem("currentOutletId") || "";
        getOutlets(currentOutletId);
      }
    } catch (error) {
      console.error("Error fetching retailer:", error);
    }
  };

  //get outlet latest data
  const getOutlets = async (id: string | string[] = []) => {
    try {
      const dataSource = new DataSource(
        "outlets",
        encodeParams({ id: id }),
        false
      );

      const res: any = await dataSource.load();

      if (res.items !== null && res.items.length > 0) {
        const data = res.items[0];
        setOutletData(data);
        setOutletFormValues(data);

        setrequiredEInvoice(data?.requiredEInvoice || "FALSE");
        setBusinessRegistrationNumber(data.businessRegistrationNumber);

        if (data.country) {
          const localcase = data.country.toLowerCase();
          if (localcase === "malaysia") {
            setIsMalaysia(true);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching outlets:", error);
    }
  };

  // Update Retailer data.
  const updateRetailerDetails = async (res: any) => {
    // Assuming the form values are updated in the profileForm object
    const updatedData: any = res;
    let afterCompare = compareDifferent2(retailerData, updatedData);

    if (updatedData.password) {
      let data = {
        password: await hashPassword.hashPassword(updatedData.password),
      };

      apiHelper
        .POST("retailer/setPassword", data, "", "v2")
        ?.then(() => {
          MessageSuccessUI(t("Login.updatePasswordSuccess"));
          if (Object.keys(afterCompare).length > 0) {
            apiHelper
              .PUT(`retailer?id=${retailerData.id}`, afterCompare, {}, "v2")
              .then(() => {
                MessageSuccessUI(t("Common.Approval.Submit.Success"));
                setIsProfileModalOpen(false);
                router.push("/profile/setting");
              })
              ?.catch(() => {
                MessageErrorUI(t("Common.Approval.Submit.Failed"));
              });
          }
        })
        .catch(() => {
          MessageErrorUI(t("Login.updatePasswordFail"));
        });
    } else if (Object.keys(afterCompare).length > 0) {
      apiHelper
        .PUT(`retailer?id=${retailerData.id}`, afterCompare, {}, "v2")
        .then(() => {
          MessageSuccessUI(t("Common.Approval.Submit.Success"));
          setIsProfileModalOpen(false);
          router.push("/profile/setting");
        })
        ?.catch(() => {
          MessageErrorUI(t("Common.Approval.Submit.Failed"));
        });
    } else {
      MessageSuccessUI(t("NoDataChanges"));
      setIsProfileModalOpen(false);
      router.push("/profile/setting");
    }
  };

  // Update outlet data
  const updateOutletDetails = async () => {
    const outletId = localStorage.getItem("currentOutletId") || "";
    const allFiles = picture;

    // Compare outlet image
    if (previewImage !== outletPreview) {
      outletForm.setFieldValue("image", allFiles[0]?.name);
    }

    // Assuming the form values are updated in the outletForm object
    const updatedData = outletForm.getFieldsValue();

    let dataSubmit = {
      name: updatedData.name,
      image: updatedData.image,
      // image: allFiles[0].name,
      companyRegistrationNo: updatedData.companyRegistrationNo,
      isTaxable: updatedData.isTaxable,
      taxExemptionNo: updatedData.taxExemptionNo,
      gstNo: updatedData.gstNo,
      sstNo: updatedData.sstNo,
      longitude: updatedData.longitude,
      latitude: updatedData.latitude,
      unitNo: updatedData.unitNo,
      address1: updatedData.address1,
      address2: updatedData.address2,
      country: updatedData.country,
      city: updatedData.city,
      state: updatedData.state,
      postalCode: updatedData.postalCode,
      billContactPerson: updatedData.billContactPerson,
      billMobilePhone: updatedData.billMobilePhone,
      billOfficePhone: updatedData.billOfficePhone,
      tradeName: updatedData.tradeName,
      billEmail: updatedData.billEmail,
      bankName: updatedData?.bankName ?? "",
      accountNo: updatedData?.accountNo ?? "",
      swiftCode: updatedData?.swiftCode ?? "",
    };

    // Use your PUT function here
    let afterCompare = compareDifferent2(outletData, dataSubmit);

    if (dataSubmit.state) {
      const state = eInoviceStateSelector.find(
        (item) => item.label === dataSubmit.state
      );
      afterCompare.stateCode = state?.code;
    }

    if (dataSubmit.country) {
      const country = eInvoiceCountryCodeSelector.find(
        (item) =>
          item.label.toLocaleLowerCase() ===
          dataSubmit.country.toLocaleLowerCase()
      );
      afterCompare.countryCode = country?.value;
    }

    if (Object.keys(afterCompare).length > 0) {
      if (allFiles.length > 0) {
        // Create a FormData object and append outlet image files to it
        const formData = new FormData();
        formData.append("file", allFiles[0]);

        // Use apiHelper to upload the file
        apiHelper
          .POST("uploadFile", formData, {
            "Content-Type": "multipart/form-data",
          })
          ?.then((res: any) => {
            afterCompare.image = res.item[allFiles[0].name];
            afterCompare.creditLimit = outletData.creditLimit;
            afterCompare.creditTerm = outletData.creditTerm;
            // Use your PUT function here
            apiHelper
              .PUT(`outlet?id=` + outletId, afterCompare, {
                "Content-Type": "multipart/form-data",
              })
              // .PUT(`outlet?id=${retailerAccess.id}`, afterCompare, {})
              .then(() => {
                MessageSuccessUI(t("Common.Approval.Submit.Success"));
                setIsOutletModalOpen(false);
                router.push("/profile/setting");
              })
              .catch(() => {
                MessageErrorUI(t("Common.Approval.Submit.Failed"));
              });
          })
          .catch(() => {
            MessageErrorUI(t("Common.Approval.Submit.Failed"));
          });
      } else {
        afterCompare.creditLimit = outletData.creditLimit;
        afterCompare.creditTerm = outletData.creditTerm;
        // Use your PUT function here
        apiHelper
          .PUT(`outlet?id=` + outletId, afterCompare, {
            "Content-Type": "multipart/form-data",
          })
          // .PUT(`outlet?id=${retailerAccess.id}`, afterCompare, {})
          .then(() => {
            MessageSuccessUI(t("Common.Approval.Submit.Success"));
            setIsOutletModalOpen(false);
            router.push("/profile/setting");
          })
          .catch(() => {
            MessageErrorUI(t("Common.Approval.Submit.Failed"));
          });
      }
    } else {
      MessageSuccessUI(t("NoDataChanges"));
      setIsOutletModalOpen(false);
      router.push("/profile/setting");
    }
  };

  //** Set form field value */

  // Set Retailer form field value.
  const setRetailerFormValues = (data: Retailer) => {
    profileForm.setFieldsValue({
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      contact: data.contact,
    });
  };

  // Set outlet form field value.
  const setOutletFormValues = (data: Outlet) => {
    outletForm.setFieldsValue({
      name: data.name,
      image: data.image || "",
      companyRegistrationNo: data.companyRegistrationNo,
      isTaxable: data.isTaxable,
      taxExemptionNo: data.taxExemptionNo,
      gstNo: data.gstNo,
      sstNo: data.sstNo,
      longitude: data.longitude,
      latitude: data.latitude,
      unitNo: data.unitNo,
      address1: data.address1,
      address2: data.address2,
      country: data.country,
      city: data.city,
      state: data.state,
      postalCode: data.postalCode,
      billContactPerson: data.billContactPerson,
      billMobilePhone: data.billMobilePhone,
      billOfficePhone: data.billOfficePhone,
      tradeName: data.tradeName,
      billEmail: data.billEmail,
      bankName: data.bankName,
      accountNo: data.accountNo,
      swiftCode: data.swiftCode,
      businessRegistrationNumber: data.businessRegistrationNumber,
      taxIdentificationNumber: data.taxIdentificationNumber,
    });

    if (data.image !== "" && data.image !== null && data.image !== undefined) {
      PicSignedUrl(data.image).then((res: any) => {
        let preview: UploadFile = {
          uid: data.image ?? "",
          name: data.image ?? "",
          thumbUrl: res,
        };
        setOutletPreview(_.cloneDeep([preview]));
        setPreviewImage([preview]);
      });
    }

    // setIsTaxable(data.isTaxable ? "true" : "none");
    setIsTaxable(data.isTaxable === "TRUE" ? "TRUE" : "FALSE");
    setPointCord({
      lat: parseFloat(data.latitude ?? "0"),
      lng: parseFloat(data.longitude ?? "0"),
    });
  };

  // Button Action
  const handleProfileCancel = () => {
    setIsProfileModalOpen(false);

    // Reset form fields
    profileForm.resetFields();

    // Set form values from retailerData after form reset
    setTimeout(() => {
      setRetailerFormValues(retailerData);
    }, 0);
  };

  const handleProfileOk = () => {
    // confirmGenerate(selectedChildRowData);
  };

  const handleOutletOk = () => {
    // confirmGenerate(selectedChildRowData);
  };

  const handleOutletCancel = () => {
    setIsOutletModalOpen(false);

    // Reset form fields
    outletForm.resetFields();

    // Set form values from retailerData after form reset
    setTimeout(() => {
      setOutletFormValues(outletData);
    }, 0);
  };

  const handleHelpOk = () => {
    // confirmGenerate(selectedChildRowData);
  };

  const handleHelpCancel = () => {
    setIsHelpModalOpen(false);
  };

  // Trigger password input
  const handlePasswordChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    if (value === "") {
      setIsPasswordNotEmpty(false);
    } else {
      setIsPasswordNotEmpty(true);
    }
    // Your other password validation logic here
  };

  // ** Image handler ** //

  const getBase64 = (file: RcFile): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });

  // Handle the image preview
  const handleImagePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }
  };

  // Handle the image change
  const handleImageChange = (info: UploadChangeParam<UploadFile<any>>) => {
    if (info.fileList.length > 0) {
      if (info.fileList.at(-1) !== undefined) {
        let pic: any[] = picture !== null ? picture : [];
        let preview = previewImage !== null ? previewImage : [];
        let file = info.fileList.at(-1);
        if (file !== undefined) {
          let uid = file.uid;
          let checkExist = pic.filter((item: any) => item.uid === uid);
          if (checkExist.length === 0) {
            file.status = "done";
            pic.push(file.originFileObj);
            preview.push(file);
            setPreviewImage([...preview]);
            setPicture([...pic]);
          }
        }
      }
    }
  };

  // To remove image
  const handleImageRemove = (info: UploadFile<any>) => {
    let pic = picture.filter((item: any) => item.uid !== info.uid);
    let preview = previewImage.filter((item: any) => item.uid !== info.uid);
    setPreviewImage(preview);
    setPicture(pic);
  };

  // Handle the image before upload
  const handleImageBeforeUpload = (file: RcFile) => {
    if (
      file.type !== "image/png" &&
      file.type !== "image/jpg" &&
      file.type !== "image/jpeg"
    ) {
      MessageErrorUI(
        `${file.name} is an invalid file format. Please change the file extension to either .png, .jpg, .jpeg.`
      );
      return Upload.LIST_IGNORE;
    } else if (file.size > 5242880) {
      MessageErrorUI(
        `${file.name} is too large. Please upload another document that is smaller than 5MB.`
      );
      return Upload.LIST_IGNORE;
    } else {
      return false;
    }
  };

  // Outlet Pin Address
  const getAddressDetails = (
    lat: number,
    lng: number
  ): Promise<Address | null> => {
    return new Promise((resolve) => {
      const latLng = new google.maps.LatLng(lat, lng) as google.maps.LatLng;

      const geocoder = new google.maps.Geocoder();
      geocoder.geocode({ location: latLng }, (results: any, status) => {
        if (status === google.maps.GeocoderStatus.OK && results.length > 0) {
          const addressComponents = results[0].address_components;

          const addressDetails: Address = {
            unitNo: "-",
            address1: "-",
            address2: "-",
            city: "-",
            state: "-",
            country: "-",
            postalCode: "00000",
          };

          addressComponents.forEach((component: any) => {
            const types = component.types;
            if (types.includes("street_number")) {
              addressDetails.unitNo = component.long_name;
            } else if (types.includes("route")) {
              addressDetails.address1 = `${component.long_name}`;
              // address.address1 = `${address.address1} ${component.long_name}`;
            } else if (types.includes("sublocality")) {
              addressDetails.address2 = component.long_name;
            } else if (types.includes("locality")) {
              addressDetails.city = component.long_name;
            } else if (types.includes("administrative_area_level_1")) {
              addressDetails.state = component.short_name;
            } else if (types.includes("country")) {
              addressDetails.country = component.long_name;
            } else if (types.includes("postal_code")) {
              addressDetails.postalCode = component.long_name;
            }
          });

          resolve(addressDetails);
        } else {
          resolve(null); // Pass null to indicate an error or no results
        }
      });
    });
  };

  // handle outlet latitude change
  const handleLatChange = (value: number) => {
    setLat(value);
  };

  // handle outlet longitude change
  const handleLngChange = (value: number) => {
    setLng(value);
  };

  const handleLocationPinpointed = async (
    lat: number,
    lng: number,
    address: string,
    type: string
  ) => {
    if (type === "general") {
      // Update the state with the pinpointed coordinates
      const pinpointedLocationObject = {
        lat,
        lng,
        address,
        text: "Pinpointed Location",
      };
      setPinpointedCoordinates(pinpointedLocationObject);
      outletForm.setFieldValue(
        "longitude",
        pinpointedLocationObject.lng?.toFixed(4)
      );
      outletForm.setFieldValue(
        "latitude",
        pinpointedLocationObject.lat?.toFixed(4)
      );

      const addressComponents = address.split(", ");
      if (addressComponents.length >= 2) {
        // Destructure the address components
        const [
          shippingAddress1,
          shippingAddress2,
          fullShippingCity,
          shippingState,
        ] = addressComponents;
        // Split the city into postal code and city (if necessary)
        const shippingCityComponents = fullShippingCity?.split(" ");
        let shippingPostalCode = "";
        let shippingCity = "";

        if (shippingCityComponents?.length >= 2) {
          shippingPostalCode = shippingCityComponents[0];
          shippingCity = shippingCityComponents.slice(1).join(" ");
        } else {
          // If there's no space-separated postal code and city, assume the whole string is the city
          shippingCity = fullShippingCity;
        }

        // Update the state with the pinpointed coordinates
        const pinpointedLocationObject = {
          lat,
          lng,
          address,
          text: "Pinpointed Location",
        };
        setPinpointedCoordinates(pinpointedLocationObject);

        // Call the getAddressDetails function to get the full address
        let addressByGeoCode: Address | null = null;

        try {
          addressByGeoCode = await getAddressDetails(lat, lng);
        } catch (error) { }

        // Set the values in the form fields
        outletForm.setFieldValue(
          "address1",
          addressByGeoCode?.address1 === "-"
            ? shippingAddress1
            : (addressByGeoCode?.unitNo ?? "") +
            " " +
            addressByGeoCode?.address1
        );
        outletForm.setFieldValue(
          "address2",
          addressByGeoCode?.address2 === "-"
            ? shippingAddress2
            : addressByGeoCode?.address2
        );

        // Extract the country (assuming it's the last component of the address)
        const shippingCountry = addressComponents[addressComponents.length - 1];

        // Set the country in the form field
        outletForm.setFieldValue(
          "country",
          addressByGeoCode?.country === "-"
            ? shippingCountry
            : addressByGeoCode?.country
        );

        outletForm.setFieldValue(
          "state",
          addressByGeoCode?.state === "-"
            ? shippingState
            : addressByGeoCode?.state
        );
        outletForm.setFieldValue(
          "city",
          addressByGeoCode?.city === "-" ? shippingCity : addressByGeoCode?.city
        );
        outletForm.setFieldValue(
          "postalCode",
          addressByGeoCode?.postalCode === "-"
            ? shippingPostalCode
            : addressByGeoCode?.postalCode
        );
      }
    }
  };
  // ** Modal UI ** //

  const showProfileModal = () => {
    return (
      <div className="w-full">
        <Form
          className="w-full pt-3.5"
          form={profileForm}
          onFinish={updateRetailerDetails}
          layout="vertical"
          scrollToFirstError
        >
          <Col className="flex flex-col bg-white w-full rounded-[12px] gap-y-2 sm:gap-0 ">
            <Row className="flex md:flex-row flex-col gap-x-4 gap-y-2 sm:gap-0 ">
              {/* First Name */}
              <Form.Item
                name="firstName"
                className="flex-1"
                rules={[
                  {
                    required: true,
                    message:
                      t("First Name") + " " + t("Validation.requiredField"),
                  },
                  {
                    validator(_, value) {
                      if (/[0-9]/.test(value)) {
                        return Promise.reject(
                          new Error(t("Validation.numberCharacter"))
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Setting.firstName")}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={t("PlaceHolder.enterFirstName")}
                  maxLength={50}
                />
              </Form.Item>

              {/* Last Name */}
              <Form.Item
                name="lastName"
                className="flex-1"
                rules={[
                  {
                    required: true,
                    message:
                      t("Setting.lastName") +
                      " " +
                      t("Validation.requiredField"),
                  },
                  {
                    validator(_, value) {
                      if (/[0-9]/.test(value)) {
                        return Promise.reject(
                          new Error(t("Validation.numberCharacter"))
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Last Name")}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={t("PlaceHolder.enterLastName")}
                  maxLength={50}
                />
              </Form.Item>
            </Row>

            <Row className="flex md:flex-row flex-col gap-x-4 gap-y-2 sm:gap-0 ">
              {/* Email */}
              <Form.Item
                name="email"
                className="flex-1"
                rules={[
                  {
                    required: true,
                    message:
                      t("Common.email") + " " + t("Validation.requiredField"),
                  },
                  {
                    validator(_, value) {
                      if (value && isValidEmail(value)) {
                        return Promise.reject(
                          new Error(t("Validation.invalidEmail"))
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.email")}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={t("PlaceHolder.sampleEmail")}
                  maxLength={50}
                />
              </Form.Item>

              {/* Contact */}
              <Form.Item
                name="contact"
                className="flex-1"
                rules={
                  [
                    // {
                    //   required: true,
                    //   message: t("Login.signUp.notEmptyPhoneNumber"),
                    // },
                    // {
                    //   validator(_, value) {
                    //     if (value && !isValidPhoneNumber(value)) {
                    //       return Promise.reject(
                    //         new Error(t("Validation.phoneFormat"))
                    //       );
                    //     }
                    //     return Promise.resolve();
                    //   },
                    // },
                  ]
                }
                label={
                  <p className="text-neutral700 text-[12px]">{t("Contact")}</p>
                }
              >
                <FormTextInput
                  placeholder={t("Common.eg") + " " + t("Placeholder.contact")}
                  disabled={true}
                  maxLength={14}
                />
                {/* <PhoneNumberInput onChange={() => {}} value={retailerAccess?.contact} /> */}
              </Form.Item>
            </Row>

            {/* <Row className="flex flex-row gap-x-4">
            <Form.Item
              className="flex-1"
              name="ic"
              rules={[
                {
                  required: true,
                  message:
                    t("Setting.icNumber") +
                    " " +
                    t("Validation.requiredField"),
                },
                {
                  validator(_, value) {
                    if (value && isSpecialCharacter(value)) {
                      return Promise.reject(
                        new Error(t("Validation.specialCharacter"))
                      );
                    } else if (value && /[a-z]/.test(value)) {
                      return Promise.reject(
                        new Error(t("Validation.alphabetCharacter"))
                      );
                    } else if (value && value.length < 12) {
                      return Promise.reject(
                        new Error(t("Validation.icLength"))
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
              label={
                <p className="text-neutral700 text-[12px]">{t("Setting.icNumber")}</p>
              }
            >
              <FormTextInput
                placeholder={retailerAccess?.ic || t("Common.eg") + " " + t("Login.signUp.icNumber")}
                maxLength={12}
                onChange={() => {}}
                value={retailerAccess?.ic}
              />
            </Form.Item>
            <Form.Item
              className="flex-1"
              label={
                <p className="text-neutral700 text-[12px]">
                  {t("Setting.icNumber")}
                </p>
              }
            >
              <UploadPicture
                value={retailerAccess?.icFrontPicture ? retailerAccess.icFrontPicture[0] : ""}
                onChange={handleFileChange}
                onRemove={handleFileRemove}
              />
            </Form.Item>
          </Row> */}

            <Row className="flex md:flex-row flex-col gap-x-4 gap-y-2 sm:gap-0 ">
              {/* Password */}
              <Form.Item
                name="password"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Login.signUp.password")}
                  </p>
                }
                rules={[
                  // {
                  //   required: true,
                  //   message: t("Login.signUp.enterPassword"),
                  // },
                  {
                    validator(_, value) {
                      if (value && value.length < 6) {
                        return Promise.reject(
                          new Error(t("Login.signUp.inputValidPassword"))
                        );
                      }
                      if (value && isValidPasswordPattern(value) == false) {
                        return Promise.reject(
                          new Error(t("Login.signUp.passwordRequirement"))
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <PasswordInput
                  maxLength={20}
                  // value={retailerAccess?.password}
                  onChange={handlePasswordChange}
                  placeholder={t("Login.signUp.password")}
                ></PasswordInput>
              </Form.Item>

              {/* Confirm Password */}
              <Form.Item
                name="confirmPassword"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Login.signUp.confirmPassword")}
                  </p>
                }
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      const passwordFieldValue = getFieldValue("password");

                      // Skip validation if password field is empty
                      if (!passwordFieldValue) {
                        return Promise.resolve();
                      }

                      if (value && value.length < 6) {
                        return Promise.reject(
                          new Error(t("Login.signUp.inputValidPassword"))
                        );
                      } else if (value !== passwordFieldValue) {
                        return Promise.reject(
                          new Error(t("Login.signUp.passwordNotMatch"))
                        );
                      } else if (value && !isValidPasswordPattern(value)) {
                        return Promise.reject(
                          new Error(t("Login.signUp.passwordRequirement"))
                        );
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <PasswordInput
                  maxLength={20}
                  disabled={!isPasswordNotEmpty}
                  // value={retailerAccess?.confirmPassword}
                  placeholder={t("Login.signUp.confirmPassword")}
                ></PasswordInput>
              </Form.Item>
            </Row>
          </Col>
          <Row className="flex justify-end space-x-3 pt-8">
            <SecondaryButtonUI
              label={t("Common.cancel")}
              htmlType="reset"
              onClick={() => {
                handleProfileCancel();
              }}
            />
            <PrimaryButtonUI
              label={t("Common.submit")}
              htmlType="submit"
              onClick={() => { }}
            />
          </Row>
        </Form>
      </div>
    );
  };

  const showOutletModal = () => {
    return (
      <div className="w-[100%] flex flex-col">
        <div className="w-full mt-4">
          <Form
            layout="vertical"
            scrollToFirstError
            form={outletForm}
            onFinish={updateOutletDetails}
          >
            <Row className=" border-gray-600">
              <h1 className="font-bold text-xl pb-3 pt-4">
                {t("Setting.outletDetails")}
              </h1>

              <Col className="flex flex-col bg-white w-full rounded-[12px] gap-y-2 sm:gap-0 ">
                {/* <Col className="flex flex-col bg-white w-full rounded-[12px] space-y-2 border-l-4 border-b-4 border-blue p-[20px]"> */}
                <Row className="flex flex-row gap-x-4 gap-y-2 sm:gap-0 ">
                  {/* Outlet Name */}
                  <Form.Item
                    name="name"
                    className="flex-1"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Setting.outletName") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Setting.outletName") +
                          " (" +
                          t("Setting.ssmRegistrationName") +
                          ")"}
                      </p>
                    }
                  >
                    <FormTextInput
                      disabled={dataStatus}
                      placeholder={t("PlaceHolder.outletName")}
                      maxLength={200}
                      onInput={(e: any) =>
                        (e.target.value = e.target.value.toUpperCase())
                      }
                    />
                  </Form.Item>
                </Row>

                <Row className="flex flex-row gap-x-4 gap-y-2 sm:gap-0 ">
                  {/* Outlet Image */}
                  <Form.Item
                    name="image"
                    className="flex-1"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Setting.outletImage") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Setting.outletImage")}
                      </p>
                    }
                  >
                    <Upload
                      name="file"
                      multiple={true}
                      maxCount={1}
                      listType="picture-card"
                      onPreview={handleImagePreview}
                      showUploadList={{ showPreviewIcon: false }}
                      beforeUpload={handleImageBeforeUpload}
                      onChange={(info) => handleImageChange(info)}
                      onRemove={(info) => handleImageRemove(info)}
                      fileList={previewImage}
                      className="custom-upload"
                    >
                      {previewImage.length === 0 ? (
                        <div>
                          <PlusOutlined />
                          <div style={{ marginTop: 8 }}>Upload</div>
                        </div>
                      ) : null}
                    </Upload>
                  </Form.Item>

                  <Col className="w-1/2">
                    {/* SSM Number */}
                    <Form.Item
                      label={
                        <p className="text-neutral700 text-[12px]">
                          {t("Setting.ssmNo")}
                        </p>
                      }
                      name="companyRegistrationNo"
                      className="flex-1"
                      rules={[
                        {
                          required: true,
                          message:
                            t("Setting.ssmNo") +
                            " " +
                            t("Validation.requiredField"),
                        },
                      ]}
                    >
                      <FormTextInput
                        maxLength={50}
                        placeholder={t("Login.signUp.enterSsmNo")}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row className="flex flex-grow gap-x-4 gap-y-2 sm:gap-0 ">
                  <Form.Item
                    name="businessRegistrationNumber"
                    className="flex-1"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Setting.businessRegistrationNumber") +
                          " " +
                          t("Validation.requiredField"),
                      },
                      {
                        validator(_, value) {
                          if (value && isSpecialCharacter(value)) {
                            return Promise.reject(
                              new Error(t("Validation.specialCharacter"))
                            );
                          } else return Promise.resolve();
                        },
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Setting.businessRegistrationNumber")}
                      </p>
                    }
                  >
                    <FormTextInput
                      placeholder={t("Setting.businessRegistrationNumber")}
                      maxLength={50}
                      onChange={(value: {
                        target: { value: React.SetStateAction<string> };
                      }) => setBusinessRegistrationNumber(value.target.value)}
                    />
                  </Form.Item>
                  <TaxIncomeNumberChecker
                    isRequired={true}
                    disabled={!businessRegistrationNumber}
                    value={outletForm.getFieldValue("taxIdentificationNumber")}
                    checkTINValue={{
                      registeredType: "BRN",
                      registerSchemeID: businessRegistrationNumber,
                    }}
                  />
                </Row>

                <Row className="flex flex-row gap-x-4 gap-y-2 sm:gap-0 ">
                  <Form.Item
                    name="generalTIN"
                    className="flex-1"
                    rules={[
                      {
                        required: true,
                        message:
                          t("setting.eInvoiceGeneralTin") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("setting.eInvoiceGeneralTin")}
                      </p>
                    }
                  >
                    <SelectInput
                      placeholder={t("Eg") + " " + ""}
                      options={eInvoiceGeneralTIN}
                      onChange={() => { }}
                    />
                  </Form.Item>
                </Row>

                <Form.Item
                  name="requiredEInvoice"
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("setting.requiredEInvoice")}
                    </p>
                  }
                >
                  <RadioButtonInput
                    defaultValue={requiredEInvoice}
                    onChange={(value: {
                      target: { value: React.SetStateAction<string> };
                    }) => {
                      setrequiredEInvoice(value.target.value);
                    }}
                    option={trueFalseOption}
                  />
                </Form.Item>

                <Row className="flex md:flex-row flex-col gap-x-4 gap-y-2 sm:gap-0 ">
                  {/* Tax Examption */}
                  <Form.Item
                    name="isTaxable"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Setting.taxable") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Setting.taxable")}
                      </p>
                    }
                  >
                    <RadioButtonInput
                      disabled={dataStatus}
                      defaultValue={isTaxable}
                      onChange={(value: {
                        target: { value: React.SetStateAction<string> };
                      }) => {
                        // Set isTaxable value
                        setIsTaxable(value.target.value);

                        // Reset specific fields based on the new value of isTaxable
                        if (value.target.value === "FALSE") {
                          outletForm.resetFields(["taxExemptionNo"]);
                        } else if (value.target.value === "TRUE") {
                          outletForm.resetFields(["gstNo"]);
                        }
                      }}
                      option={trueFalseOption}
                    />
                  </Form.Item>
                  {isTaxable === "FALSE" ? (
                    <Row className="flex md:flex-row flex-col gap-x-4 gap-y-2 sm:gap-0 w-full">
                      <Form.Item
                        name="taxExemptionNo"
                        className="flex-1 w-full"
                        rules={[
                          // { required: isTaxable === "FALSE" ? true : false, message: t("TaxExemptionNo") + " " + t("Validation.requiredField") },
                          {
                            validator(_, value) {
                              if (/[a-z]/.test(value) && value !== undefined) {
                                return Promise.reject(
                                  new Error(t("Validation.alphabetCharacter"))
                                );
                              } else if (value && isSpecialCharacter(value)) {
                                return Promise.reject(
                                  new Error(t("Validation.specialCharacter"))
                                );
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                        label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Setting.taxExamptionNo")}
                          </p>
                        }
                      >
                        <FormTextInput
                          disabled={dataStatus}
                          placeholder={
                            t("Common.eg") + " " + t("Setting.taxExamptionNo")
                          }
                          maxLength={50}
                        />
                      </Form.Item>
                      <Form.Item
                        name="sstNo"
                        className="flex-1 w-1/2"
                        rules={[
                          {
                            required: true,
                            message:
                              t("SSTNumber") +
                              " " +
                              t("Validation.requiredField"),
                          },
                          {
                            validator(_, value) {
                              // if (/[a-z]/.test(value) && value !== undefined) {
                              //   return Promise.reject(new Error(t("Alphabet.Character.Error")));
                              // }
                              if (value && /\s/.test(value)) {
                                return Promise.reject(
                                  new Error(t("Validation.spaceNotPermitted"))
                                );
                              } else if (value && isSpecialCharacter(value)) {
                                return Promise.reject(
                                  new Error(t("Validation.specialCharacter"))
                                );
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                        label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Setting.sstNo")}
                          </p>
                        }
                      >
                        <FormTextInput
                          disabled={dataStatus}
                          placeholder={
                            t("Common.eg") + " " + t("PlaceHolder.sstNo")
                          }
                          maxLength={50}
                        />
                      </Form.Item>
                    </Row>
                  ) : isTaxable === "TRUE" ? (
                    <Row className="flex md:flex-row flex-col gap-x-4 gap-y-2 sm:gap-0 w-full">
                      {/* GST No */}
                      <Form.Item
                        name="gstNo"
                        className="flex-1 w-1/2"
                        rules={[
                          // { required: isTaxable === "TRUE" ? true : false, message: t("GSTNumber") + " " + t("Validation.requiredField") },
                          {
                            validator(_, value) {
                              // if (/[a-z]/.test(value) && value !== undefined) {
                              //   return Promise.reject(new Error(t("Alphabet.Character.Error")));
                              // }
                              if (value && /\s/.test(value)) {
                                return Promise.reject(
                                  new Error(t("Validation.spaceNotPermitted"))
                                );
                              } else if (value && isSpecialCharacter(value)) {
                                return Promise.reject(
                                  new Error(t("Validation.specialCharacter"))
                                );
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                        label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Setting.gstNo")}
                          </p>
                        }
                      >
                        <FormTextInput
                          disabled={dataStatus}
                          placeholder={
                            t("Common.eg") + " " + t("PlaceHolder.gstNo")
                          }
                          maxLength={50}
                        />
                      </Form.Item>

                      {/* SST No */}
                      <Form.Item
                        name="sstNo"
                        className="flex-1 w-1/2"
                        rules={[
                          {
                            required: true,
                            message:
                              t("SSTNumber") +
                              " " +
                              t("Validation.requiredField"),
                          },
                          {
                            validator(_, value) {
                              // if (/[a-z]/.test(value) && value !== undefined) {
                              //   return Promise.reject(new Error(t("Alphabet.Character.Error")));
                              // }
                              if (value && /\s/.test(value)) {
                                return Promise.reject(
                                  new Error(t("Validation.spaceNotPermitted"))
                                );
                              } else if (value && isSpecialCharacter(value)) {
                                return Promise.reject(
                                  new Error(t("Validation.specialCharacter"))
                                );
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                        label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Setting.sstNo")}
                          </p>
                        }
                      >
                        <FormTextInput
                          disabled={dataStatus}
                          placeholder={
                            t("Common.eg") + " " + t("PlaceHolder.sstNo")
                          }
                          maxLength={50}
                        />
                      </Form.Item>
                    </Row>
                  ) : null}
                </Row>
              </Col>
            </Row>

            <h1 className="font-bold text-xl pb-3 pt-4">
              {t("Login.signUp.outletAddress")}
            </h1>

            <Col className="flex flex-col bg-white w-full rounded-[12px] space-y-2 p-[20px]">
              {/* <Col className="flex flex-col bg-white w-full rounded-[12px] space-y-2 border-l-4 border-b-4 border-blue p-[20px]"> */}
              <Row className="flex flex-row gap-x-4 gap-y-2 sm:gap-0 pt-2">
                {/* GPS Location (Google Map) */}
                <Form.Item
                  name="gpsLocation"
                  className="flex-1"
                  rules={
                    [
                      // {
                      //   required: true,
                      //   message:
                      //     t("GPSLocation") + " " + t("Validation.requiredField"),
                      // },
                    ]
                  }
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.outletLocation")}
                    </p>
                  }
                >
                  <GoogleMap
                    newLat={lat}
                    newLng={lng}
                    onLocationPinpointed={(lat, lng, address) =>
                      handleLocationPinpointed(lat, lng, address, "general")
                    }
                    pointLat={0}
                    pointLng={0}
                  />
                </Form.Item>
              </Row>

              <Row className="flex md:flex-row flex-col gap-x-4 gap-y-2 sm:gap-0 ">
                {/* Latitude */}
                <Form.Item
                  name="latitude"
                  className="flex-1"
                  rules={[
                    {
                      required: true,
                      message:
                        t("Setting.latitude") +
                        " " +
                        t("Validation.requiredField"),
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.latitude")}
                    </p>
                  }
                  initialValue={pinpointedCoordinates.lat}
                >
                  <NumberInput
                    placeholder={
                      t("Common.eg") + " " + t("PlaceHolder.latitude")
                    }
                    value={lat}
                    precision={4}
                    onChange={handleLatChange}
                    disabled
                  />
                </Form.Item>

                {/* Longtitude */}
                <Form.Item
                  name="longitude"
                  className="flex-1"
                  rules={[
                    {
                      required: true,
                      message:
                        t("Setting.longitude") +
                        " " +
                        t("Validation.requiredField"),
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.longitude")}
                    </p>
                  }
                  initialValue={pinpointedCoordinates.lng}
                >
                  <NumberInput
                    placeholder={
                      t("Common.eg") + " " + t("PlaceHolder.longitude")
                    }
                    value={lng}
                    precision={4}
                    onChange={handleLngChange}
                    disabled
                  />
                </Form.Item>
              </Row>

              <Row className="flex flex-row gap-x-4 gap-y-2 sm:gap-0 ">
                {/* Unit No */}
                <Form.Item
                  name="unitNo"
                  className="flex-1"
                  rules={[
                    {
                      required: true,
                      message:
                        t("Setting.unitNo") +
                        " " +
                        t("Validation.requiredField"),
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.unitNo")}
                    </p>
                  }
                >
                  <FormTextInput
                    placeholder={t("Common.eg") + " " + t("PlaceHolder.unitNo")}
                    maxLength={100}
                  />
                </Form.Item>
              </Row>

              <Row className="flex md:flex-row flex-col gap-x-4 gap-y-2 sm:gap-0 ">
                {/* Address 1 */}
                <Form.Item
                  name="address1"
                  className="flex-1"
                  rules={[
                    {
                      required: true,
                      message:
                        t("Setting.address1") +
                        " " +
                        t("Validation.requiredField"),
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.address1")}
                    </p>
                  }
                >
                  <FormTextInput
                    placeholder={
                      t("Common.eg") + " " + t("PlaceHolder.address1")
                    }
                    maxLength={100}
                  />
                </Form.Item>

                {/* Address 2 */}
                <Form.Item
                  name="address2"
                  className="flex-1"
                  /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.address2")}
                    </p>
                  }
                >
                  <FormTextInput
                    placeholder={
                      t("Common.eg") + " " + t("PlaceHolder.address2")
                    }
                    maxLength={100}
                  />
                </Form.Item>
              </Row>

              <Row className="flex md:flex-row flex-col gap-x-4 gap-y-2 sm:gap-0 ">
                {/* Country */}
                <Form.Item
                  name="country"
                  className="flex-1"
                  rules={[
                    {
                      required: true,
                      message:
                        t("Setting.country") +
                        " " +
                        t("Validation.requiredField"),
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.country")}
                    </p>
                  }
                >
                  <SelectInput
                    options={eInvoiceCountryCodeSelector}
                    placeholder={
                      t("Common.eg") + " " + t("PlaceHolder.country")
                    }
                    onChange={(val) => {
                      val === "MYR"
                        ? setIsMalaysia(true)
                        : setIsMalaysia(false);
                      outletForm.resetFields(["billState"]);
                    }}
                  />
                </Form.Item>

                {/* State */}
                <Form.Item
                  name="state"
                  className="flex-1"
                  rules={[
                    {
                      required: true,
                      message:
                        t("Setting.state") +
                        " " +
                        t("Validation.requiredField"),
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.state")}
                    </p>
                  }
                >
                  {isMalaysia ? (
                    <SelectInput
                      options={eInoviceStateSelector}
                      placeholder={
                        t("Common.eg") + " " + t("PlaceHolder.state")
                      }
                    />
                  ) : (
                    <FormTextInput
                      defaultValue={""}
                      placeholder={
                        t("Common.eg") + " " + t("PlaceHolder.state")
                      }
                      maxLength={50}
                    />
                  )}
                  {/* disabled={stateList.length !== 0 ? false : true} /> */}
                </Form.Item>
              </Row>

              <Row className="flex md:flex-row flex-col gap-x-4 gap-y-2 sm:gap-0 ">
                {/* City */}
                <Form.Item
                  name="city"
                  className="flex-1"
                  rules={[
                    {
                      required: true,
                      message:
                        t("Setting.city") + " " + t("Validation.requiredField"),
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.city")}
                    </p>
                  }
                >
                  <FormTextInput
                    defaultValue={""}
                    placeholder={t("Common.eg") + " " + t("PlaceHolder.city")}
                    maxLength={100}
                  />
                  {/* disabled={cityList.length !== 0 ? false : true} /> */}
                </Form.Item>

                {/* Postal Code */}
                <Form.Item
                  name="postalCode"
                  className="flex-1"
                  rules={[
                    {
                      required: true,
                      message:
                        t("Setting.postalCode") +
                        " " +
                        t("Validation.requiredField"),
                    },
                    {
                      validator(_, value) {
                        if (value && isSpecialCharacter(value)) {
                          return Promise.reject(
                            new Error(t("Validation.specialCharacter"))
                          );
                        }
                        if (value && /[a-z]/.test(value)) {
                          return Promise.reject(
                            new Error(t("Validation.alphabetCharacter"))
                          );
                        } else return Promise.resolve();
                      },
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.postalCode")}
                    </p>
                  }
                >
                  <FormTextInput
                    placeholder={
                      t("Common.eg") + " " + t("PlaceHolder.postalCode")
                    }
                    maxLength={5}
                  // onChange={(value: any) => setPostalCode(value)}
                  />
                </Form.Item>
              </Row>

              {/* Contact Person */}
              <Form.Item
                name="billContactPerson"
                rules={[
                  {
                    required: true,
                    message:
                      t("Setting.billContactPerson") +
                      " " +
                      t("Validation.requiredField"),
                  },
                  {
                    validator(_, value) {
                      if (value && isSpecialCharacter(value)) {
                        return Promise.reject(
                          new Error(t("Validation.specialCharacter"))
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Setting.billContactPerson")}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={
                    t("Common.eg") + " " + t("PlaceHolder.billContactPerson")
                  }
                  maxLength={100}
                />
              </Form.Item>

              <Row className="flex md:flex-row flex-col gap-x-4 gap-y-2 sm:gap-0 ">
                {/* Mobile Phone */}
                <Form.Item
                  name="billMobilePhone"
                  className="flex-1"
                  rules={[
                    {
                      required: true,
                      message:
                        t("Setting.postalCode") +
                        " " +
                        t("Validation.requiredField"),
                    },
                    {
                      validator(_, value) {
                        if (value && !isValidPhoneNumber(value)) {
                          return Promise.reject(
                            new Error(t("Validation.phoneFormat"))
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.billMobilePhone")}
                    </p>
                  }
                >
                  <PhoneNumberInput onChange={() => { }} />
                </Form.Item>

                {/* Office Phone */}
                <Form.Item
                  name="billOfficePhone"
                  className="flex-1"
                  rules={[
                    {
                      validator(_, value) {
                        if (value && !isValidPhoneNumber(value)) {
                          return Promise.reject(
                            new Error(t("Validation.phoneFormat"))
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.billOfficePhone")}
                    </p>
                  }
                >
                  <PhoneNumberInput onChange={() => { }} />
                </Form.Item>
              </Row>

              <Row className="flex md:flex-row flex-col gap-x-4 gap-y-2 sm:gap-0 ">
                {/* Trade Name */}
                <Form.Item
                  name="tradeName"
                  className="flex-1"
                  // rules={[
                  //   {
                  //     required: true,
                  //     message:
                  //       t("Setting.tradeName") +
                  //       " " +
                  //       t("Validation.requiredField"),
                  //   },
                  // ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.tradeName")}
                    </p>
                  }
                >
                  <FormTextInput
                    placeholder={
                      t("Common.eg") + " " + t("PlaceHolder.tradeName")
                    }
                    maxLength={200}
                  />
                </Form.Item>

                {/* Email */}
                <Form.Item
                  name="billEmail"
                  className="flex-1"
                  rules={[
                    {
                      validator(_, value) {
                        if (value && isValidEmail(value)) {
                          return Promise.reject(
                            new Error(t("Validation.invalidEmail"))
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.billEmail")}
                    </p>
                  }
                >
                  <FormTextInput
                    placeholder={
                      t("Common.eg") + " " + t("PlaceHolder.billEmail")
                    }
                    maxLength={100}
                  />
                </Form.Item>
              </Row>
            </Col>

            <h1 className="font-bold text-xl pb-3 pt-4">
              {t("Setting.virtualAccountDetails")}
            </h1>

            <Col className="flex flex-col bg-white w-full rounded-[12px] space-y-2 p-[20px]">
              {/* <Col className="flex flex-col bg-white w-full rounded-[12px] space-y-2 border-l-4 border-b-4 border-blue p-[20px]"> */}
              <Row className="flex md:flex-row flex-col gap-x-4 gap-y-2 sm:gap-0 ">
                {/* Back Name */}
                <Form.Item
                  name="bankName"
                  className="flex-1"
                  // rules={[
                  //   {
                  //     required: true,
                  //     message:
                  //       t("Setting.bankName") +
                  //       " " +
                  //       t("Validation.requiredField"),
                  //   },
                  // ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.bankName")}{" "}
                    </p>
                  }
                >
                  <FormTextInput
                    placeholder={
                      t("Common.eg") + " " + t("PlaceHolder.bankName")
                    }
                    maxLength={100}
                    onChange={() => { }}
                  />
                </Form.Item>

                {/* Account Number */}
                <Form.Item
                  name="accountNo"
                  className="flex-1"
                  rules={[
                    {
                      validator(_, value) {
                        if (value && isSpecialCharacter(value)) {
                          return Promise.reject(
                            new Error(t("Validation.specialCharacter"))
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Setting.accountNo")}
                    </p>
                  }
                >
                  <FormTextInput
                    placeholder={
                      t("Common.eg") + " " + t("PlaceHolder.accountNo")
                    }
                    maxLength={100}
                    onChange={() => { }}
                  />
                </Form.Item>
              </Row>

              {/* Swift Code */}
              <Form.Item
                name="swiftCode"
                rules={[
                  {
                    validator(_, value) {
                      if (value && isSpecialCharacter(value)) {
                        return Promise.reject(
                          new Error(t("Validation.specialCharacter"))
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Setting.swiftCode")}{" "}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={
                    t("Common.eg") + " " + t("PlaceHolder.swiftCode")
                  }
                  maxLength={100}
                  onChange={() => { }}
                />
              </Form.Item>
            </Col>

            <Row className="mt-[30px] flex justify-end">
              <SecondaryButtonUI
                label={t("Common.cancel")}
                htmlType="reset"
                onClick={() => {
                  handleOutletCancel();
                }}
              />
              <PrimaryButtonUI
                label={t("Common.submit")}
                htmlType="submit"
                onClick={() => { }}
              />
            </Row>
          </Form>
        </div>
      </div>
    );
  };

  // Help Modal
  const showHelpModal = () => {
    return (
      <div>
        <Form
          className="w-full pt-3.5"
          form={helpForm}
          layout="vertical"
          scrollToFirstError
        >
          {/* <Form.Item
            className="flex-1"
            label={
              <p className="text-neutral700 text-[12px]">{t("helpfile")}</p>
            }
          >
            <UploadPicture
              value="helpfile"
              onChange={handleFileChange}
              onRemove={handleFileRemove}
            />
          </Form.Item> */}
        </Form>
      </div>
    );
  };

  // Account Page
  const accountTab = () => {
    return (
      <Col>
        <LargeClickableCard
          title={`${retailerData?.firstName ?? ""} ${retailerData?.lastName ?? ""
            }`}
          imageComponent={
            <User width="40" height="40" viewBox="0 0 70 80" src={User.src} />
          }
          onClick={() => setIsProfileModalOpen(true)}
          height="150px"
          className="mb-4"
        />
        <LargeClickableCard
          // title={outletForm.getFieldValue("name")}
          title={outletData?.name ?? ""}
          imageComponent={
            <Company
              width="40"
              height="40"
              viewBox="0 0 90 80"
              src={Company.src}
            />
          }
          onClick={() => setIsOutletModalOpen(true)}
          height="150px"
          className="mb-4"
        />
        <LargeClickableCard
          title={t("Setting.helpFile")}
          imageComponent={
            <Help width="40" height="40" viewBox="0 0 85 85" src={Help.src} />
          }
          onClick={() => setIsHelpModalOpen(true)}
          height="150px"
          className="mb-4"
        />
        <LargeClickableCard
          title={t("Setting.logout")}
          imageComponent={
            <Logout
              width="40"
              height="40"
              viewBox="0 0 90 80"
              src={Logout.src}
            />
          }
          onClick={() => {
            useRetailerStore.setState({ retailer: null });
            localStorage.clear();
            router.push("/login");
          }}
          height="150px"
          className="mb-4"
        />
      </Col>
    );
  };

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => { }} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        <h1>{t("Setting.setting")}</h1>

        {accountTab()}

        <ModalUI
          title={"My Profile"}
          width={isSmallScreen ? "100%" : "80%"}
          visible={isProfileModalOpen}
          onOk={handleProfileOk}
          onCancel={handleProfileCancel}
          content={showProfileModal()}
        ></ModalUI>
        <ModalUI
          title={"My Outlet Profile"}
          width={isSmallScreen ? "100%" : "80%"}
          visible={isOutletModalOpen}
          onOk={handleOutletOk}
          onCancel={handleOutletCancel}
          content={showOutletModal()}
        ></ModalUI>
        <ModalUI
          title={"Help File"}
          width={isSmallScreen ? "100%" : "80%"}
          visible={isHelpModalOpen}
          onOk={handleHelpOk}
          onCancel={handleHelpCancel}
          content={showHelpModal()}
        ></ModalUI>
      </Content>
      <AppFooter retailerAccessValues={retailerData} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default Profile;
