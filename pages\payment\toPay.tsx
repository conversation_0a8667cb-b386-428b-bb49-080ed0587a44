import React, { useEffect, useRef, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import Header, { supportedLocales } from "../../components/header";
import {
  BackButtonUI,
  PrimaryButtonUI,
  SecondaryButtonUI,
} from "../../components/buttonUI";
import {
  Col,
  DatePicker,
  Divider,
  Form,
  MenuProps,
  Modal,
  Row,
  Upload,
  UploadFile,
  UploadProps,
} from "antd";
import {
  ListingTableUI,
  MessageErrorUI,
  MessageInfoUI,
  MessageSuccessUI,
  getStatusStyles,
} from "../../components/ui";
import {
  CreditNote,
  CreditNoteDocument,
  DebitNoteDocument,
  Invoice,
  InvoiceDocument,
  PaymentDetail,
  ProductOrdered,
  Retailer,
  SelectOption,
  StatementList,
} from "@/components/type";
import {
  DataSource,
  NumberThousandSeparator,
  capitalize,
  cloneDeep,
  encodeParams,
  formateDate,
} from "@/stores/utilize";
import {
  FormNumberInput,
  FormTextInput,
  NumberInput,
  SelectInput,
} from "@/components/input";
import { debounce } from "lodash";
import FpxIcon from "../../assets/icon/FPX.svg";
import PaymentIcon from "../../assets/logo/payment.svg";
import CreditNoteIcon from "../../assets/logo/creditNote.svg";
import ChequeIcon from "../../assets/logo/cheque.svg";
import { ClickableCard } from "@/components/card";
import { ModalUI } from "@/components/modalUI";
import FilterFormComponent from "@/components/filter";
import {
  creditTypeOption,
  paymentMethodOption,
  statusFilterOption1,
} from "@/components/config";
import { PlusOutlined } from "@ant-design/icons";
import { RcFile, UploadChangeParam } from "antd/es/upload";
import apiHelper from "../api/apiHelper";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import moment from "moment";
import Decimal from "decimal.js";
import _ from "lodash";
import AppFooter from "@/components/footer";

function ToPay() {
  const router = useRouter();
  const { t } = useTranslation("common");
  const [generalInvoiceForm] = Form.useForm();
  const [generalDebitNoteForm] = Form.useForm();
  const [generalCreditNoteForm] = Form.useForm();
  const [filterDNForm] = Form.useForm();
  const [filterDNModalForm] = Form.useForm();
  const [filterCNForm] = Form.useForm();
  const [filterCNModalForm] = Form.useForm();
  const [cashPaymentForm] = Form.useForm();
  const [chequePaymentForm] = Form.useForm();
  const [onlineTransferForm] = Form.useForm();
  const [fpxModalForm] = Form.useForm();
  const [cursor, setCursor] = useState("");
  const [currentOutletId, setCurrentOutletId] = useState("");

  const [selectedRowInvKeys, setSelectedRowInvKeys] = useState<string[]>([]);
  const [selectedRowInvData, setSelectedRowInvData] = useState<any[]>([]);
  const [selectedRowDnKeys, setSelectedRowDnKeys] = useState<string[]>([]);
  const [selectedRowDnData, setSelectedRowDnData] = useState<any[]>([]);
  const [selectedRowCnKeys, setSelectedRowCnKeys] = useState<string[]>([]);
  const [selectedRowCnData, setSelectedRowCnData] = useState<any[]>([]);

  const [data, setData] = useState<any>({});
  const [fullData, setFullData] = useState<StatementList[]>([]);
  // const [companyMap, setCompanyMap] = useState(new Map());
  // const [outletMap, setOutletMap] = useState(new Map());
  const [invoiceMap, setInvoiceMap] = useState(new Map());
  const [invoiceTotal, setInvoiceTotal] = useState(0);
  const [dnTotal, setDnTotal] = useState(0);
  const [cnTotal, setCnTotal] = useState(0);
  const [totalPayment, setTotalPayment] = useState(0);
  const [creditNote, setCreditNote] = useState<any>({});
  // const [paymentData, setPaymentData] = useState<PaymentDetail>({});
  const [paymentData, setPaymentData] = useState<{
    [key: string]: PaymentDetail;
  }>({});
  const [selectedRowCnModalKeys, setSelectedRowCnModalKeys] = useState<
    string[]
  >([]);
  const [selectedRowCnModalData, setSelectedRowCnModalData] = useState<any[]>(
    []
  );
  const [isCnModalOpen, setIsCnModalOpen] = useState(false);
  const [debitNote, setDebitNote] = useState<any>({});
  const [selectedRowDnModalKeys, setSelectedRowDnModalKeys] = useState<
    string[]
  >([]);
  const [selectedRowDnModalData, setSelectedRowDnModalData] = useState<any[]>(
    []
  );
  const [isDnModalOpen, setIsDnModalOpen] = useState(false);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);

  const [bankListOption, setBankListOption] = useState<SelectOption[]>([]);
  const [paymentType, setPaymentType] = useState<string>("");
  // ======================================================================
  // Picture - state()
  // ======================================================================
  const [allFiles, setAllFiles] = useState<any[]>([]);
  const [previewImage, setPreviewImage] = useState<UploadFile[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewTitle, setPreviewTitle] = useState("");
  const [previewImageDisplay, setPreviewImageDisplay] = useState("");

  // ======================================================================
  // filter function - state()
  // ======================================================================
  const [showClearFilter, setShowClearFilter] = useState(false);
  const [fuzzySearchFilter, setFuzzySearchFilter] = useState("");
  const [modalFilter, setModalFilter] = useState<any>({});
  const [cnFilterSetting, setCnFilterSetting] = useState("");
  const [statusValue, setStatusValue] = useState("All");
  const [statusKey, setStatusKey] = useState("ALL");
  const [isFilterCNModalOpen, setIsFilterCNModalOpen] = useState(false);
  const [fieldName, setFieldName] = useState("");

  const [dnFuzzySearchFilter, setDnFuzzySearchFilter] = useState("");
  const [dnModalFilter, setDnModalFilter] = useState<any>({});
  const [dnFilterSetting, setDnFilterSetting] = useState("");
  const [isFilterDNModalOpen, setIsFilterDNModalOpen] = useState(false);
  const [dnFieldName, setDnFieldName] = useState("");
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});

  const [isSubmitButtonLoading, setIsSubmitButtonLoading] = useState(false);

  // ======================================================================
  // FPX - state()
  // ======================================================================
  const [fpxInvNo, setFpxInvNo] = useState<string>("");
  const [isFPXClicked, setIsFPXClicked] = useState(false);
  const [finalAmountToPaid, setFinalAmountToPaid] = useState<Decimal>(
    new Decimal(0)
  );
  const [isFPXModalStatusOn, setIsFPXModalStatusOn] = useState(false);
  const [fpxStatus, setFPXStatus] = useState("PENDING");
  const [fpxPaymentData, setFPXPaymentData] = useState<any>({});
  const [paymentId, setPaymentId] = useState<string>("");

  const headerItems = [
    {
      label: t("Header.home"),
      route: "/landing",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label:
        router.query.paymentMade === "invoiceSelected"
          ? t("Header.invoice")
          : t("Header.statement"),
      route:
        router.query.paymentMade === "invoiceSelected"
          ? "/invoice/invoiceListing"
          : "/statement/statementListing",
      className: "labelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Header.toPay"),
      className: "labelTextStyle",
    },
  ];

  useEffect(() => {
    if (router.isReady && Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }
      let outletId = localStorage.getItem("currentOutletId");
      setCurrentOutletId(outletId ?? "");
    }
  }, [Object.keys(useRetailerStore.getState()).length, router.isReady]);

  useEffect(() => {
    if (Object.keys(retailerAccess).length > 0) {
      if (router.query.paymentMade === "paymentSelected") {
        getStatement();
      } else if (router.query.paymentMade === "invoiceSelected") {
        getInvoiceData();
      }
    }
    getBankListOption();
  }, [retailerAccess]);

  useEffect(() => {
    if (fullData.length > 0 && Object.keys(retailerAccess).length > 0) {
      remapStatement();
    }
  }, [fullData, retailerAccess]);

  useEffect(() => {
    if (data && Object.keys(retailerAccess).length > 0) {
      getCreditNote();
      getDebitNote();
      getInvoice();
    }
  }, [data, retailerAccess]);

  useEffect(() => {
    if (
      Object.keys(retailerAccess).length > 0 &&
      selectedRowInvKeys.length > 0
    ) {
      let invoiceSubtotal = 0;

      let updatedData = _.cloneDeep(data);

      // Loop the selected key and update the amount.
      for (let i = 0; i < selectedRowInvKeys.length; i++) {
        let selectedInvoicedData = updatedData?.reMapInvoice.find(
          (val: any) => selectedRowInvKeys[i] === val.id
        );

        // Selected invoice from the data source.
        if (selectedInvoicedData) {
          let formNumber = 0;
          let amountToPaid = generalInvoiceForm.getFieldValue(
            `amount${selectedInvoicedData.id}`
          );

          // Get the amount from form value
          // Else condition which is next page which cannot retrieve the data when select all , then use the remaining amount/
          if (amountToPaid) {
            formNumber =
              parseFloat(
                generalInvoiceForm
                  .getFieldValue(`amount${selectedInvoicedData.id}`)
                  .toString()
                  .replace(/,/g, "")
              ) ?? 0;
            invoiceSubtotal += formNumber;
          } else {
            formNumber = parseFloat(selectedInvoicedData?.remainingAmount) || 0;
            invoiceSubtotal += formNumber;
          }
        } else {
          console.error("documentNotFound");
        }
      }

      setInvoiceTotal(invoiceSubtotal);
    } else {
      setInvoiceTotal(0);
    }
  }, [selectedRowInvKeys, data, retailerAccess]);

  useEffect(() => {
    if (
      Object.keys(retailerAccess).length > 0 &&
      selectedRowDnKeys.length > 0
    ) {
      let dnSubtotal = 0;
      selectedRowDnKeys.forEach((item) => {
        let checked = data?.reMapDebitNote?.find((val: any) => item === val.id);
        let amount = 0;
        if (typeof checked?.netAmount === "string") {
          amount = parseFloat(checked.netAmount.toString().replace(/,/g, ""));
        }
        let formNumber = 0;
        if (generalDebitNoteForm.getFieldValue(`amount${checked.id}`)) {
          formNumber =
            parseFloat(
              generalDebitNoteForm
                .getFieldValue(`amount${checked.id}`)
                .toString()
                .replace(/,/g, "")
            ) ?? 0;
        }

        dnSubtotal += formNumber ?? amount;
      });
      setDnTotal(dnSubtotal);
    } else {
      setDnTotal(0);
    }
  }, [selectedRowDnKeys, data, retailerAccess]);

  useEffect(() => {
    if (
      Object.keys(retailerAccess).length > 0 &&
      selectedRowCnKeys.length > 0
    ) {
      let cnSubtotal = 0;
      selectedRowCnKeys.forEach((item) => {
        let checked = data?.reMapCreditNote?.find(
          (val: any) => item === val.id
        );
        let amount = 0;
        if (typeof checked?.netAmount === "string") {
          amount = parseFloat(checked.netAmount.toString().replace(/,/g, ""));
        }
        let formNumber = 0;
        if (generalCreditNoteForm.getFieldValue(`amount${checked.id}`)) {
          formNumber =
            parseFloat(
              generalCreditNoteForm
                .getFieldValue(`amount${checked.id}`)
                .toString()
                .replace(/,/g, "")
            ) ?? 0;
        }
        cnSubtotal += formNumber ?? amount;
      });
      setCnTotal(cnSubtotal);
    } else {
      setCnTotal(0);
    }
  }, [selectedRowCnKeys, data, retailerAccess]);

  useEffect(() => {
    if (Object.keys(retailerAccess).length > 0) {
      let total = invoiceTotal - cnTotal + dnTotal;
      setTotalPayment(total);
    }
  }, [invoiceTotal, cnTotal, dnTotal, retailerAccess]);

  useEffect(() => {
    if (isPaymentModalOpen === false) {
      setAllFiles([]);
      setPreviewImage([]);
    }
  }, [isPaymentModalOpen]);

  useEffect(() => {
    if (isFPXClicked && finalAmountToPaid.equals(0)) {
      MessageInfoUI(
        "The payment amount has already been fulfilled; therefore, FPX cannot be applied."
      );
    }
  }, [isFPXClicked, finalAmountToPaid]);

  useEffect(() => {
    if (Object.keys(retailerAccess).length > 0) {
      const data: any = {
        fuzzySearch: fuzzySearchFilter || "",
        creditNoteNo: modalFilter.creditNoteNo || "",
        invoiceNo: modalFilter.invoiceNo || "",
        // outletCode: modalFilter.outletCode || "",
        // outletName: modalFilter.outletName || "",
        productSku: modalFilter.productSku || "",
        productName: modalFilter.productName || "",
        type: modalFilter.type || "",
        status: statusKey === "ALL" ? "" : statusKey,
      };

      const clonedFilterKey = { ...data };
      delete clonedFilterKey.fuzzySearch;

      Object.keys(clonedFilterKey).forEach((key) => {
        const capitalizedKey = key
          .split(/(?=[A-Z])|\s+/)
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        clonedFilterKey[capitalizedKey] = clonedFilterKey[key];
        delete clonedFilterKey[key];
      });

      const keysAsString: string = Object.keys(clonedFilterKey).join(", ");
      setFieldName(keysAsString);

      const allPropertiesEmpty = Object.values(data).every(
        (value) => value === ""
      );

      if (!allPropertiesEmpty) {
        searchCN(data);
      } else {
        setCnFilterSetting("");
      }
    }
  }, [fuzzySearchFilter, statusKey, modalFilter, retailerAccess]);

  useEffect(() => {
    if (Object.keys(retailerAccess).length > 0) {
      if (cnFilterSetting) {
        getCreditNote();
        setShowClearFilter(true);
      } else {
        getCreditNote();
        setShowClearFilter(false);
      }
    }
  }, [cnFilterSetting, retailerAccess]);

  useEffect(() => {
    if (Object.keys(retailerAccess).length > 0) {
      const data: any = {
        fuzzySearch: dnFuzzySearchFilter || "",
        debitNoteNo: dnModalFilter.debitNoteNo || "",
        type: dnModalFilter.type || "",
        status: statusKey === "ALL" ? "" : statusKey,
      };
      const clonedFilterKey = { ...data };
      delete clonedFilterKey.fuzzySearch;

      Object.keys(clonedFilterKey).forEach((key) => {
        const capitalizedKey = key
          .split(/(?=[A-Z])|\s+/)
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        clonedFilterKey[capitalizedKey] = clonedFilterKey[key];
        delete clonedFilterKey[key];
      });

      const keysAsString: string = Object.keys(clonedFilterKey).join(", ");
      setDnFieldName(keysAsString);

      const allPropertiesEmpty = Object.values(data).every(
        (value) => value === ""
      );

      if (!allPropertiesEmpty) {
        searchDN(data);
      } else {
        setDnFilterSetting("");
      }
    }
  }, [dnFuzzySearchFilter, statusKey, dnModalFilter, retailerAccess]);

  useEffect(() => {
    if (Object.keys(retailerAccess).length > 0) {
      if (dnFilterSetting) {
        getDebitNote();
        setShowClearFilter(true);
      } else {
        getDebitNote();
        setShowClearFilter(false);
      }
    }
  }, [dnFilterSetting, retailerAccess]);

  // const paymentApiEndpoint =
  //   "https://uat.zappit.com.my/paymentapi/api/Payment/FPX"; // Replace with your ASP.NET endpoint

  // const handleFPXPayment = async () => {
  //   try {
  //     const paymentData = {
  //       PaymentID: "800",
  //       BoxID: "ARS",
  //       UserID: retailerAccess?.id,
  //       CashierID: retailerAccess?.id,
  //       InvNo: fpxInvNo,
  //       CurrencyID: "MYR",
  //       TotalAmt: totalPayment,
  //       DiscAmt: 0,
  //       Remark: "Test Order",
  //       BuyerBankId: "",
  //       BuyerEmail: "",
  //       invDetail: [
  //         {
  //           SKU: "9001001",
  //           Qty: 1,
  //         },
  //         // Add more items if needed
  //       ],
  //     };

  //     const response = await fetch(paymentApiEndpoint, {
  //       method: "POST",
  //       headers: {
  //         "Content-Type": "application/json",
  //         // Add any other necessary headers
  //       },
  //       body: JSON.stringify(paymentData),
  //     });
  //     console.log("response", response);
  //     if (response.ok) {
  //       const responseData = await response.json();
  //       console.log("Payment response:", responseData);

  //       if (responseData.Status === "1" && responseData.Data) {
  //         // Redirect the user to the FPX payment processing URL for successful payment
  //         window.location.href = responseData.Data;
  //       } else {
  //         console.error("Payment failed:", responseData.Message);
  //         // Handle other status conditions or failure cases
  //       }
  //     } else {
  //       console.error("Payment failed:", response.statusText);
  //       // Handle error response
  //     }
  //   } catch (error) {
  //     console.error("Payment failed:", error);
  //     // Handle fetch error
  //   }
  // };

  const handleFPXPayment = async () => {
    const paymentId = await submitPayment(true);
    try {
      const paymentData = {
        companyId: retailerAccess?.companyId,
        companyBranchId: retailerAccess?.companyBranchId,
        retailerId: retailerAccess?.id,
        outletId: currentOutletId,
        PaymentID: "800",
        BoxID: "YLTCPay", // ARS | YLTCPay
        UserID: retailerAccess?.id,
        CashierID: retailerAccess?.id,
        // InvNo: fpxInvNo,
        InvNo: paymentId,
        CurrencyID: "MYR",
        TotalAmt: finalAmountToPaid.toNumber(),
        DiscAmt: 0,
        Remark: "Test Order",
        BuyerBankId: "",
        BuyerEmail: "",
        invDetail: [
          {
            SKU: "9001001",
            Qty: 1,
          },
          // Add more items if needed
        ],
      };
      apiHelper
        .POST("payment/fpx", paymentData, "", "v2")
        ?.then((res: any) => {
          if (res && res.item && res.item.Status === "0") {
            // set fpx payment session id
            sessionStorage.setItem("fpxId", res.item.PaymentId || "");

            // setTimeout(() => {
            //   router.push(res.item.Data);
            // }, 500);
            setTimeout(() => {
              const newPage = window.open(res.item.Data, "_blank");
              if (newPage) {
                newPage.focus();
                setIsSubmitButtonLoading(false);
                setIsFPXModalStatusOn(true);
                // fpxStatusCheckingModal(paymentData, res.item.PaymentId)
                setFPXPaymentData(_.cloneDeep(paymentData));
                setPaymentId(res.item.PaymentId);
              } else {
                console.error(
                  "Unable to open a new window/tab. Please check your browser settings."
                );
              }
            }, 500);
          }
          MessageSuccessUI(
            t("Payment.collection") + " " + t("Common.createSuccess")
          );
          // router.push("/payment/paymentListing");
        })
        .catch(() => {
          //* This Part need re-edit*//
          MessageErrorUI(
            t("Payment.collection") + " " + t("Common.createUnsuccess")
          );
        });
    } catch (error) {
      console.error("Payment failed:", error);
      // Handle fetch error
    }
  };

  const callFPXstatus = (id: string) => {
    if (!id) return;
    apiHelper.GET(`payment/fpxes?id=${id}`, "", "", "v2")?.then((res: any) => {
      if (res && res.items) {
        setFPXStatus(res.items[0].status);
      }
    });
  };

  const getStatement = (
    isClearFilter = false,
    otherViewBranchAccess = false
  ) => {
    // const accessBranches = otherViewBranchAccess === false ? retailerAccess.companyBranchIds : otherCompanyBranchesIds;
    const getLocalSelectedInvoice = localStorage.getItem("paymentSelected");
    const charArray = getLocalSelectedInvoice
      ? getLocalSelectedInvoice.split("/")
      : "";

    // const ids: string = router.query.ids as string;
    // let array: string[] = [];
    // if (ids && ids.includes(",")) {
    //   array = ids.split(",");
    // }
    setTimeout(() => {
      let params: any = {
        sort: "createdAt",
        sortOrder: "-1",
        // id: array.length > 0 ? array : ids,
        id: charArray,
        isCurrentStatement: "TRUE",
      };

      // if (!isAdmin) {
      //   params.companyBranchIds = accessBranches;
      // }
      // const checkAdminRights = isAdmin ? params : params + "&companyId=" + retailerAccess.companyId;
      // const checkFilterRights = cnFilterSetting && !isClearFilter ? cnFilterSetting : encodeParams(params);
      const dataSource = new DataSource(
        "statements",
        encodeParams(params),
        true
      );

      // !isAdmin && !otherViewBranchAccess ? filterCNForm.setFieldValue("companyId", retailerAccess.companyId) : null;

      // if ((accessBranches && accessBranches.length > 0) || isAdmin) {
      dataSource
        .load()
        .then((res: any) => {
          if (res !== null) {
            let data = res;

            // use to display in fpx
            setFpxInvNo(data[0]?.statementNo ?? "");

            // const objectMap = data.reduce(
            //   (accumulator: any, current: StatementList) => {
            //     // accumulator["outletId"] = accumulator["outletId"] || [];
            //     // if (
            //     //   current.outletId &&
            //     //   !outletMap.has(current.outletId) &&
            //     //   !accumulator["outletId"].includes(current.outletId)
            //     // ) {
            //     //   accumulator["outletId"].push(current.outletId ?? "");
            //     // }

            //     // current.invoices?.reduce((acc: any, product: Invoice) => {
            //     //   accumulator["companyId"] = accumulator["companyId"] || [];
            //     //   if (
            //     //     product.companyId &&
            //     //     !companyMap.has(product.companyId) &&
            //     //     !accumulator["companyId"].includes(product.companyId)
            //     //   ) {
            //     //     accumulator["companyId"].push(product.companyId ?? "");
            //     //   }
            //     //   return acc;
            //     // }, {});

            //     return accumulator;
            //   },
            //   {}
            // );
            // getOutlets(objectMap["outletId"]);
            // getCompany(objectMap["companyId"]);

            // getCompanyBranch(objectMap["companyBranchId"]);
            setFullData(data);
          }
        })
        .catch(() => {
          //* This Part need re-edit*//
        });
      // } else {
      //   setGoodsReturnListingInfoData([]);
      // }
    }, 500);
  };

  const remapStatement = () => {
    let tempData = fullData.map((item: any) => {
      // item.outletCode = outletMap.get(item.outletId)?.outletCode;
      // item.outletName = outletMap.get(item.outletId)?.name;
      let invoiceListData: Invoice[] = [];
      let creditNoteListData: any[] = [];
      let debitNoteListData: any[] = [];
      let statementAmount = 0;
      let paidAmount = 0;
      if (item.date !== undefined) {
        item.statementDate = formateDate(item.date);
      }
      // Get invoices
      item.invoices.forEach((invoice: Invoice) => {
        // let totalAmountInProduct = 0;
        // let productCount = 0;
        // invoice.invoiceProducts!.map((product: ProductOrdered) => {
        //   if (
        //     product.discount !== undefined &&
        //     product.quantity !== undefined &&
        //     product.price !== undefined &&
        //     product.taxRate !== undefined
        //   ) {
        //     let unitDiscount = product.discount / product.quantity;
        //     let subTotalPrice =
        //       (product.price - unitDiscount) * product.quantity;
        //     let taxPrice = subTotalPrice * (product.taxRate / 100);
        //     let total = subTotalPrice + taxPrice;
        //     totalAmountInProduct += total;
        //     productCount += 1;
        //   }
        // });

        // let invoiceDate = invoicesData.find((invoice: Invoice) => invoice.id)?.createdAt;
        // if (invoiceDate !== undefined) {
        //   let date = new Date(invoiceDate);
        //   formatInvoiceDate = ("0" + date.getDate()).slice(-2) + "/" + ("0" + (date.getMonth() + 1)).slice(-2) + "/" + date.getFullYear();
        // }
        //  let invoiceDueDate = moment(invoice.createdAt).add(outletData?.creditTerm, "day").format("DD/MM/YYYY");
        let invoice1: any = {
          id: invoice.id,
          statementId: item.id,
          statementNo: item.statementNo,
          invoiceNo: invoice.invoiceNo,
          invoiceDate: formateDate(invoice.invoiceDate ?? ""),
          companyId: item.companyId,
          amount: NumberThousandSeparator(
            (invoice.netAmount ?? 0) +
            (invoice.totalTax ?? 0) -
            (invoice.paidAmount ?? 0) -
            (invoice.processingAmount ?? 0)
          ),
          invoiceAmount: NumberThousandSeparator(
            // totalAmountInProduct + (invoice.shippingFee ?? 0)
            (invoice.netAmount ?? 0) + (invoice.totalTax ?? 0)
          ),
          remainingAmount: NumberThousandSeparator(
            // totalAmountInProduct +
            //   (invoice.shippingFee ?? 0) -
            (invoice.netAmount ?? 0) +
            (invoice.totalTax ?? 0) -
            (invoice.paidAmount ?? 0) -
            (invoice.processingAmount ?? 0)
          ),
          paidAmount: NumberThousandSeparator(invoice.paidAmount ?? 0),
          totalProduct: invoice.invoiceProducts?.length,
          processingAmount: item.processingAmount ?? 0,
        };
        statementAmount += invoice1.amount;
        paidAmount += invoice1.paidAmount;
        // outstandingAmount += totalAmountInProduct + (invoice.shippingFee ?? 0) - (invoice.paidAmount ?? 0);
        invoiceListData.push(invoice1);
        generalInvoiceForm.setFieldValue(
          `amount${invoice.id}`,
          invoice1.remainingAmount
        );
      });

      // Get credit note
      // let formatCreditNoteDate: string = "";
      // let creditNoteDate = creditNoteData.find((creditNote) => creditNote.id)?.createdAt;
      // if (creditNoteDate !== undefined) {
      //   let date = new Date(creditNoteDate);
      //   formatCreditNoteDate = ("0" + date.getDate()).slice(-2) + "/" + ("0" + (date.getMonth() + 1)).slice(-2) + "/" + date.getFullYear();
      // }

      if (item.creditNotes) {
        item.creditNotes.forEach((creditNote: any) => {
          let creditNote1 = {
            id: creditNote.id,
            creditNoteNo: creditNote.creditNoteNo,
            creditNoteDate: formateDate(creditNote.creditNoteDate ?? ""),
            remainingAmount: NumberThousandSeparator(
              (creditNote.grossAmount ?? 0) +
              (creditNote.taxAmount ?? 0) -
              (creditNote.usedAmount ?? 0) -
              (creditNote.processingAmount ?? 0)
            ),
            grossAmount: NumberThousandSeparator(creditNote.grossAmount ?? 0),
            taxAmount: NumberThousandSeparator(creditNote.taxAmount ?? 0),
            netAmount: NumberThousandSeparator(
              (creditNote.grossAmount ?? 0) +
              (creditNote.taxAmount ?? 0) -
              (creditNote.usedAmount ?? 0) -
              (creditNote.processingAmount ?? 0)
            ),
            invoiceId: creditNote.invoiceId,
            type: capitalize(creditNote.type),
            //   outstandingAmount: parseFloat(((creditNote.grossAmount ?? 0) + (creditNote.taxAmount ?? 0) - (creditNote.usedAmount ?? 0)).toFixed(2)),
          };
          statementAmount -= creditNote.grossAmount + creditNote.taxAmount;
          paidAmount += creditNote.amount;
          // outstandingAmount -= (creditNote.grossAmount ?? 0) + (creditNote.taxAmount ?? 0) - (creditNote.usedAmount ?? 0);
          creditNoteListData.push(creditNote1);
        });
      }

      // Get debit note
      // let formatDebitNoteDate: string = "";
      // let debitNoteDate = debitNoteData?.find((debitNote) => debitNote.id)?.createdAt;
      // if (debitNoteDate !== undefined) {
      //   let date = new Date(debitNoteDate);
      //   formatDebitNoteDate = ("0" + date.getDate()).slice(-2) + "/" + ("0" + (date.getMonth() + 1)).slice(-2) + "/" + date.getFullYear();
      // }

      if (item.debitNotes) {
        item.debitNotes?.forEach((debitNote: any) => {
          let debitNote1 = {
            id: debitNote.id,
            debitNoteNo: debitNote.debitNoteNo,
            debitNoteDate: formateDate(debitNote.debitNoteDate ?? ""),
            remainingAmount: NumberThousandSeparator(
              (debitNote.grossAmount ?? 0) +
              (debitNote.taxAmount ?? 0) -
              (debitNote.usedAmount ?? 0) -
              (debitNote.processingAmount ?? 0)
            ),
            grossAmount: NumberThousandSeparator(debitNote.grossAmount ?? 0),
            taxAmount: NumberThousandSeparator(debitNote.taxAmount ?? 0),
            netAmount: NumberThousandSeparator(
              (debitNote.grossAmount ?? 0) +
              (debitNote.taxAmount ?? 0) -
              (debitNote.usedAmount ?? 0) -
              (debitNote.processingAmount ?? 0)
            ),
            type: capitalize(debitNote.type),
          };
          statementAmount += debitNote.grossAmount + debitNote.taxAmount;
          paidAmount += debitNote.amount;
          // outstandingAmount += (debitNote.grossAmount ?? 0) + (debitNote.taxAmount ?? 0) - (debitNote.paidAmount ?? 0);
          debitNoteListData.push(debitNote1);
        });
      }

      item.statementAmount = statementAmount;
      item.paidAmount = paidAmount;
      item.statementStatus = item.status;
      item.reMapInvoice = invoiceListData;
      item.reMapCreditNote = creditNoteListData;
      item.reMapDebitNote = debitNoteListData;

      return item;
    });
    setData(tempData[0]);
  };

  // const getOutlets = async (id: string[] = []) => {
  //   let tempProductMap = new Map(outletMap);
  //   if (!id?.length) return tempProductMap;

  //   const params: any = {
  //     // status: "ACTIVE",
  //     id: [],
  //   };
  //   try {
  //     while (id?.length) {
  //       params.id = id?.splice(0, 50);
  //       const dataSource = new DataSource(
  //         "outlets",
  //         encodeParams(params),
  //         false
  //       );
  //       const res: any = await dataSource.load().catch(() => {
  //         id = [];
  //         //* This Part need re-edit*//
  //       });
  //       if (res !== null && res.items.length > 0) {
  //         setOutletMap((prevDataMap) => {
  //           const newDataMap = new Map(prevDataMap);
  //           res.items.forEach((item: Outlet) => {
  //             if (!newDataMap.has(item.id)) {
  //               newDataMap.set(item.id, item);
  //             }
  //           });
  //           return newDataMap;
  //         });
  //         res.items?.map((item: Outlet) => {
  //           tempProductMap.set(item.id, item);
  //         });
  //       }
  //     }
  //   } catch (err) {
  //     return tempProductMap;
  //   }
  //   return tempProductMap;
  // };

  // const getCompany = async (id: string[] = []) => {
  //   let tempProductMap = new Map(outletMap);
  //   if (!id?.length) return tempProductMap;

  //   const params: any = {
  //     // status: "ACTIVE",
  //     id: [],
  //   };
  //   try {
  //     while (id?.length) {
  //       params.id = id?.splice(0, 50);
  //       const dataSource = new DataSource(
  //         "companies",
  //         encodeParams(params),
  //         false
  //       );
  //       const res: any = await dataSource.load().catch(() => {
  //         id = [];
  //         //* This Part need re-edit*//
  //       });
  //       if (res !== null && res.items.length > 0) {
  //         setCompanyMap((prevDataMap) => {
  //           const newDataMap = new Map(prevDataMap);
  //           res.items.forEach((item: CompanyGeneralInfo) => {
  //             if (!newDataMap.has(item.id)) {
  //               newDataMap.set(item.id, item);
  //             }
  //           });
  //           return newDataMap;
  //         });
  //         res.items?.map((item: CompanyGeneralInfo) => {
  //           tempProductMap.set(item.id, item);
  //         });
  //       }
  //     }
  //   } catch (err) {
  //     return tempProductMap;
  //   }
  //   return tempProductMap;
  // };

  const getBankListOption = () => {
    apiHelper.GET("kit/bankList").then((res: any) => {
      let bankList: SelectOption[] = [];
      if (res && res.length) {
        res.forEach((bankName: string) => {
          bankList.push({
            value: bankName,
            label: bankName,
          });
        });
        setBankListOption(bankList);
      }
    });
  };

  const getInvoice = () => {
    const getLocalSelectedInvoice = localStorage.getItem("invoiceSelected");
    if (!getLocalSelectedInvoice) return;
    const charArray = getLocalSelectedInvoice
      ? getLocalSelectedInvoice.split("/")
      : "";
    // let params = {
    //   companyId: retailerAccess.companyId,
    //   companyBranchId: retailerAccess.companyBranchId,
    //   outletId: retailerAccess.outletIds,
    // };
    const dataSource = new DataSource(
      "invoices",
      encodeParams({ id: charArray }),
      true
    );
    dataSource
      .load()
      ?.then((res: any) => {
        if (res !== null) {
          let tempProductMap = new Map(invoiceMap);
          setInvoiceMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.forEach((item: Invoice) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res?.map((item: Invoice) => {
            tempProductMap.set(item.id, item);
          });
        }
      })
      .catch(() => { });
  };

  const getInvoiceData = () => {
    const getLocalSelectedInvoice = localStorage.getItem("invoiceSelected");
    const charArray = getLocalSelectedInvoice
      ? getLocalSelectedInvoice.split("/")
      : "";

    const dataSource = new DataSource(
      "invoices",
      encodeParams({ id: charArray }),
      true
    );
    dataSource
      .load()
      ?.then((res: any) => {
        if (res !== null) {
          const invoices = res;

          // use to display in fpx
          setFpxInvNo(invoices?.invoiceNo ?? "");

          let statementAmount = 0;
          let paidAmount = 0;
          let invoiceListData: any[] = [];

          invoices.forEach((invoice: Invoice) => {
            let totalAmountInProduct = 0;
            let productCount = 0;
            // invoice.invoiceProducts!.map((product: ProductOrdered) => {
            //   if (
            //     product.discount !== undefined &&
            //     product.quantity !== undefined &&
            //     product.price !== undefined &&
            //     product.taxRate !== undefined
            //   ) {
            //     let unitDiscount = product.discount / product.quantity;
            //     let subTotalPrice =
            //       (product.price - unitDiscount) * product.quantity;
            //     let taxPrice = subTotalPrice * (product.taxRate / 100);
            //     let total = subTotalPrice + taxPrice;
            //     totalAmountInProduct += total;
            //     productCount += 1;
            //   }
            // });
            let invoice1: any = {
              id: invoice.id,
              statementNo: "",
              invoiceNo: invoice.invoiceNo,
              invoiceDate: formateDate(invoice.invoiceDate ?? ""),
              companyId: invoice.companyId,
              amount: NumberThousandSeparator(
                (invoice.netAmount ?? 0) +
                (invoice.totalTax ?? 0) -
                (invoice.paidAmount ?? 0) -
                (invoice.processingAmount ?? 0)
              ),
              invoiceAmount: NumberThousandSeparator(
                // totalAmountInProduct + (invoice.shippingFee ?? 0)
                (invoice.netAmount ?? 0) + (invoice.totalTax ?? 0)
              ),
              remainingAmount: NumberThousandSeparator(
                // totalAmountInProduct +
                //   (invoice.shippingFee ?? 0) -
                (invoice.netAmount ?? 0) +
                (invoice.totalTax ?? 0) -
                (invoice.paidAmount ?? 0) -
                (invoice.processingAmount ?? 0)
              ),
              paidAmount: NumberThousandSeparator(invoice.paidAmount ?? 0),
              totalProduct: productCount,
              processingAmount: invoice.processingAmount ?? 0,
            };
            statementAmount += invoice1.amount;
            paidAmount += invoice1.paidAmount;
            // outstandingAmount += totalAmountInProduct + (invoice.shippingFee ?? 0) - (invoice.paidAmount ?? 0);
            invoiceListData.push(invoice1);
          });
          setData({
            reMapInvoice: invoiceListData,
            reMapCreditNote: [],
            reMapDebitNote: [],
          });
        }
      })
      .catch(() => { });
  };

  const getCreditNote = () => {
    let params: any = {
      companyId: retailerAccess.companyId,
      // companyBranchId: retailerAccess.companyBranchId,
      outletId: currentOutletId,
      status: "ACTIVE",
      balanceGT: "0",
    };

    const checkFilterRights = cnFilterSetting
      ? cnFilterSetting
      : encodeParams(params);

    const dataSource = new DataSource("creditNotes", checkFilterRights, false);
    dataSource
      .load()
      ?.then(async (res: any) => {
        if (res.items !== null) {
          let creditNoteListData: any[] = [];
          let data = res.items;
          const objectMap = data.reduce(
            (accumulator: any, current: CreditNote) => {
              accumulator["invoiceId"] = accumulator["invoiceId"] || [];
              if (
                current.invoiceId &&
                !invoiceMap.has(current.invoiceId) &&
                !accumulator["invoiceId"].includes(current.invoiceId)
              ) {
                accumulator["invoiceId"].push(current.invoiceId ?? "");
              }

              return accumulator;
            },
            {}
          );
          await getInvoices(objectMap["invoiceId"]);

          res.items?.forEach((creditNote: any) => {
            const alreadyHas = data?.reMapCreditNote?.find(
              (val: any) => val.id === creditNote.id
            );

            if (!alreadyHas) {
              let creditNote1 = {
                id: creditNote.id,
                creditNoteNo: creditNote.creditNoteNo,
                creditNoteDate: formateDate(creditNote.creditNoteDate ?? ""),
                usedAmount: NumberThousandSeparator(creditNote.usedAmount ?? 0),
                grossAmount: NumberThousandSeparator(
                  creditNote.grossAmount ?? 0
                ),
                taxAmount: NumberThousandSeparator(creditNote.taxAmount ?? 0),
                netAmount: NumberThousandSeparator(
                  (creditNote.grossAmount ?? 0) + (creditNote.taxAmount ?? 0)
                ),
                invoiceNo: invoiceMap.get(creditNote.invoiceId)?.invoiceNo,
                type: capitalize(creditNote.type),
                remainingAmount: NumberThousandSeparator(
                  (creditNote.grossAmount ?? 0) +
                  (creditNote.taxAmount ?? 0) -
                  (creditNote.usedAmount ?? 0) -
                  (creditNote.processingAmount ?? 0)
                ),
              };

              // outstandingAmount += (debitNote.grossAmount ?? 0) + (debitNote.taxAmount ?? 0) - (debitNote.paidAmount ?? 0);
              creditNoteListData.push(creditNote1);
            }
          });
          setCreditNote(creditNoteListData);
        }
      })
      .catch(() => { });
  };

  const getInvoices = async (id: string[] = []) => {
    let tempProductMap = new Map(invoiceMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      // status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "invoices",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setInvoiceMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Invoice) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Invoice) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getDebitNote = () => {
    let params: any = {
      companyId: retailerAccess.companyId,
      // companyBranchId: retailerAccess.companyBranchId,
      outletId: currentOutletId,
      status: "ACTIVE",
      balanceGT: "0",
    };

    const checkFilterRights = dnFilterSetting
      ? dnFilterSetting
      : encodeParams(params);
    const dataSource = new DataSource("debitNotes", checkFilterRights, false);
    dataSource
      .load()
      ?.then((res: any) => {
        if (res.items !== null) {
          let debitNoteListData: any[] = [];
          res.items?.forEach((debitNote: any) => {
            const alreadyHas = data?.reMapDebitNote?.find(
              (val: any) => val.id === debitNote.id
            );
            if (!alreadyHas) {
              let debitNote1 = {
                id: debitNote.id,
                debitNoteNo: debitNote.debitNoteNo,
                debitNoteDate: formateDate(debitNote.debitNoteDate ?? ""),
                usedAmount: NumberThousandSeparator(debitNote.usedAmount ?? 0),
                grossAmount: NumberThousandSeparator(
                  debitNote.grossAmount ?? 0
                ),
                taxAmount: NumberThousandSeparator(debitNote.taxAmount ?? 0),
                netAmount: NumberThousandSeparator(
                  (debitNote.grossAmount ?? 0) + (debitNote.taxAmount ?? 0)
                ),
                type: capitalize(debitNote.type),
                remainingAmount: NumberThousandSeparator(
                  (debitNote.grossAmount ?? 0) +
                  (debitNote.taxAmount ?? 0) -
                  (debitNote.usedAmount ?? 0) -
                  (debitNote.processingAmount ?? 0)
                ),
              };

              // outstandingAmount += (debitNote.grossAmount ?? 0) + (debitNote.taxAmount ?? 0) - (debitNote.paidAmount ?? 0);
              debitNoteListData.push(debitNote1);
            }
          });
          setDebitNote(debitNoteListData);
        }
      })
      .catch(() => { });
  };

  // *************************************************************************************
  // *** Images Props and function ***
  // *************************************************************************************

  //images upload
  const getBase64 = (file: RcFile): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }

    setPreviewImageDisplay(file.url || (file.preview as string));
    setPreviewOpen(true);
    setPreviewTitle(
      file.name || file.url!.substring(file.url!.lastIndexOf("/") + 1)
    );
  };

  const handleChange = (info: UploadChangeParam<UploadFile<any>>) => {
    if (info.fileList.length > 0) {
      if (info.fileList.at(-1) !== undefined) {
        let pic: any[] = allFiles !== null ? allFiles : [];
        let preview: any[] = previewImage !== null ? previewImage : [];
        let file = info.fileList.at(-1);
        if (file !== undefined) {
          let uid = file.uid;
          let checkExist = pic.filter((item: any) => item.uid === uid);
          if (checkExist.length === 0) {
            file.status = "done";
            // pic.push(file.originFileObj);
            preview.push(file);
            setPreviewImage([...preview]);
            setAllFiles([...preview]);
          }
        }
      }
    }
  };

  const handleRemove = (info: UploadFile<any>) => {
    let pic = allFiles.filter((item: any) => item.uid !== info.uid);
    let preview = previewImage.filter((item: any) => item.uid !== info.uid);
    setPreviewImage(preview);
    setAllFiles(pic);
  };

  const handleBeforeUpload = (file: {
    type: string;
    name: any;
    size: number;
  }) => {
    if (
      file.type !== "image/png" &&
      file.type !== "image/jpg" &&
      file.type !== "image/jpeg" &&
      file.type !== "application/pdf"
    ) {
      MessageErrorUI(
        `${file.name} is an invalid file format. Please change the file extension to either .pdf, .png, .jpg, .jpeg.`
      );
      return Upload.LIST_IGNORE;
    } else if (file.size > 5242880) {
      MessageErrorUI(
        `${file.name} is too large. Please upload another document that is smaller than 5MB.`
      );
      return Upload.LIST_IGNORE;
    } else {
      return false;
    }
  };

  //Upload props
  const props: UploadProps = {
    name: "file",
    multiple: true,
    maxCount: 8,
    listType: "picture-card",
    fileList: previewImage,
    onPreview: handlePreview,
    accept: "image/png, image/jpeg, image/jpg",
    showUploadList: {
      showPreviewIcon: true,
    },
    beforeUpload: handleBeforeUpload,
    // onChange: handleChange,
    onRemove: handleRemove,
  };

  const invColumn = [
    router.query.paymentMade === "invoiceSelected"
      ? null
      : {
        title: t("Payment.statement") + " " + t("Common.no"),
        dataIndex: "statementNo",
        key: "statementNo",
        render: (_: any, record: any) => {
          return <p className="tableRowNameDesign">{record.statementNo}</p>;
        },
      },
    {
      title: t("Payment.invoice") + " " + t("Common.no"),
      dataIndex: "invoiceNo",
      key: "invoiceNo",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.invoiceNo}</p>;
      },
    },
    {
      title: t("Payment.invoiceDate"),
      dataIndex: "invoiceDate",
      key: "invoiceDate",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.invoiceDate}</p>;
      },
    },
    // {
    //   title: t("Company"),
    //   dataIndex: "companyId",
    //   key: "companyId",
    //   render: (_: any, record: any) => {
    //     const name = companyMap.get(record.companyId)?.name;
    //     return <p className="">{name}</p>;
    //   },
    // },
    {
      title: t("Payment.totalProduct"),
      dataIndex: "totalProduct",
      key: "totalProduct",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.totalProduct}</p>;
      },
    },
    {
      title: t("Payment.invoiceAmount"),
      dataIndex: "invoiceAmount",
      key: "invoiceAmount",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.invoiceAmount}</p>;
      },
    },
    {
      title: t("Payment.remainingAmount"),
      dataIndex: "remainingAmount",
      key: "remainingAmount",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.remainingAmount}</p>;
      },
    },
    {
      title: t("Payment.processingAmount"),
      dataIndex: "processingAmount",
      key: "processingAmount",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.processingAmount}</p>;
      },
    },
    {
      title: t("Payment.amountToPaid"),
      dataIndex: "toPaidAmount",
      key: "toPaidAmount",
      render: (_: any, record: any) => {
        // generalForm.setFieldValue(
        //   `amount${record.id}`,
        //   generalForm.getFieldValue(`amount${record.id}`) ??
        //     record?.remainingAmount
        // );

        const handleOnBlurWhenNumberChange = async (
          item: any,
          remainingAmount: string
        ) => {
          // Remove thousand separtor , for remaing amount
          const cleanedRemainingAmount = remainingAmount.replace(/,/g, "");

          const moneyPattern = /^\d+(\.\d{1,2})?$/;

          // if enter value is not number pattern then just used remaining amount.

          let value = moneyPattern.test(item) ? item : cleanedRemainingAmount;

          // if remaing amount is 0 , then the value just 0

          if (parseFloat(cleanedRemainingAmount) <= 0) {
            value = "0";
          }

          // if enter value is more than remaing amount then just use remaing amount
          else if (parseFloat(value) > parseFloat(cleanedRemainingAmount)) {
            value = cleanedRemainingAmount;
          }

          const hasInv = selectedRowInvKeys.includes(record.id);
          if (!hasInv) {
            setSelectedRowInvKeys([...selectedRowInvKeys, record.id]);
            setSelectedRowInvData([...selectedRowInvData, record]);
          }

          const tempData = cloneDeep(data);
          const selectedInvIndex = tempData.reMapInvoice.findIndex(
            (val: any) => val.id === record.id
          );
          value = await NumberThousandSeparator(parseFloat(value));
          if (selectedInvIndex !== -1) {
            tempData.reMapInvoice[selectedInvIndex].amount = value;
          }

          generalInvoiceForm.setFieldValue(`amount${record.id}`, value);

          setData(tempData);
        };

        return (
          <Form.Item
            name={"amount" + record.id}
            rules={[
              {
                required: true,
                message: "Please Input Amount!",
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const cleanedRemainingAmount =
                    record?.remainingAmount.replace(/,/g, "");
                  if (
                    value &&
                    parseFloat(value) > parseFloat(cleanedRemainingAmount)
                  ) {
                    return Promise.reject(
                      "Amount must not exceed the remaining amount"
                    );
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <FormNumberInput
              precision={2}
              defaultValue={record?.remainingAmount}
              className="flex w-full"
              placeholder="Amount"
              onBlur={(event) => {
                handleOnBlurWhenNumberChange(
                  event.target.value,
                  record?.remainingAmount
                );
              }}
            />
          </Form.Item>
        );
      },
    },
  ].filter(Boolean);

  const dnColumn = [
    {
      title: t("Payment.debitNote") + " " + t("Common.no"),
      dataIndex: "debitNoteNo",
      key: "debitNoteNo",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.debitNoteNo}</p>;
      },
    },
    {
      title: t("Payment.debitNoteDate"),
      dataIndex: "debitNoteDate",
      key: "debitNoteDate",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.debitNoteDate}</p>;
      },
    },
    {
      title: t("Payment.type"),
      dataIndex: "type",
      key: "type",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.type}</p>;
      },
    },
    {
      title: t("Payment.netAmount"),
      dataIndex: "netAmount",
      key: "netAmount",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.netAmount}</p>;
      },
    },
    {
      title: t("Payment.remainingAmount"),
      dataIndex: "remainingAmount",
      key: "remainingAmount",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.remainingAmount}</p>;
      },
    },
    !isDnModalOpen && {
      title: t("Payment.amountToPaid"),
      dataIndex: "paidAmount",
      key: "paidAmount",
      render: (_: any, record: any) => {
        const handleChange = async (item: any) => {
          let value = item;
          const hasDn = selectedRowDnKeys.includes(record.id);
          if (!hasDn) {
            setSelectedRowDnKeys([...selectedRowDnKeys, record.id]);
            setSelectedRowDnData([...selectedRowDnData, record]);
          }

          if (value) {
            const tempData = cloneDeep(data);
            const selectedDnIndex = tempData.reMapDebitNote.findIndex(
              (val: any) => val.id === record.id
            );
            const selectedDn = tempData.reMapDebitNote.filter(
              (val: any) => val.id === record.id
            );
            value = await NumberThousandSeparator(parseFloat(value));
            if (selectedDnIndex !== -1) {
              selectedDn.map((dn: any) => {
                dn.amount = value;
              });
            }
            tempData.reMapDebitNote[selectedDnIndex] = selectedDn[0];
            setData(tempData);
          }
        };

        const cleanedRemainingAmount = record?.remainingAmount.replace(
          /,/g,
          ""
        );

        return (
          <Form.Item
            name={"amount" + record.id}
            rules={[
              {
                required: true,
                message: `Please Input Amount!`,
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (
                    value &&
                    parseFloat(value) > parseFloat(cleanedRemainingAmount)
                  ) {
                    return Promise.reject(
                      "Amount must not exceed the remaining amount"
                    );
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <FormNumberInput
              precision={2}
              className="flex w-full"
              placeholder="Amount"
              max={cleanedRemainingAmount}
              onChange={handleChange}
            />
          </Form.Item>
        );
      },
    },
  ].filter(Boolean);

  const cnColumn = [
    {
      title: t("Payment.creditNote") + " " + t("Common.no"),
      dataIndex: "creditNoteNo",
      key: "creditNoteNo",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.creditNoteNo}</p>;
      },
    },
    {
      title: t("Payment.creditNoteDate"),
      dataIndex: "creditNoteDate",
      key: "creditNoteDate",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.creditNoteDate}</p>;
      },
    },
    {
      title: t("Payment.type"),
      dataIndex: "type",
      key: "type",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.type}</p>;
      },
    },
    {
      title: t("Payment.invoice") + " " + t("Common.no"),
      dataIndex: "invoiceNo",
      key: "invoiceNo",
      render: (_: any, record: any) => {
        // const name = invoiceMap.get(record.invoiceId)?.invoiceNo;
        return (
          <p className="tableRowNameDesign">
            {record.invoiceNo ? record.invoiceNo : "-"}
          </p>
        );
      },
    },
    {
      title: t("Payment.netAmount"),
      dataIndex: "netAmount",
      key: "netAmount",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.netAmount}</p>;
      },
    },
    {
      title: t("Payment.remainingAmount"),
      dataIndex: "remainingAmount",
      key: "remainingAmount",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.remainingAmount}</p>;
      },
    },
    !isCnModalOpen && {
      title: t("Payment.amountToPaid"),
      dataIndex: "paidAmount",
      key: "paidAmount",
      render: (_: any, record: any) => {
        const handleChange = async (item: any) => {
          let value = item;
          const hasCn = selectedRowCnKeys.includes(record.id);
          if (!hasCn) {
            setSelectedRowCnKeys([...selectedRowCnKeys, record.id]);
            setSelectedRowCnData([...selectedRowCnData, record]);
          }

          if (value) {
            const tempData = cloneDeep(data);
            const selectedCnIndex = tempData.reMapCreditNote.findIndex(
              (val: any) => val.id === record.id
            );
            const selectedCn = tempData.reMapCreditNote.filter(
              (val: any) => val.id === record.id
            );
            value = await NumberThousandSeparator(parseFloat(value));
            if (selectedCnIndex !== -1) {
              selectedCn.map((cn: any) => {
                cn.amount = value;
              });
            }
            tempData.reMapCreditNote[selectedCnIndex] = selectedCn[0];
            setData(tempData);
          }
        };

        const cleanedRemainingAmount = record?.remainingAmount.replace(
          /,/g,
          ""
        );

        return (
          <Form.Item
            name={"amount" + record.id}
            rules={[
              {
                required: true,
                message: `Please Input Amount!`,
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (
                    value &&
                    parseFloat(value) > parseFloat(cleanedRemainingAmount)
                  ) {
                    return Promise.reject(
                      "Amount must not exceed the remaining amount"
                    );
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <FormNumberInput
              precision={2}
              className="flex w-full"
              placeholder="Amount"
              max={cleanedRemainingAmount}
              onChange={handleChange}
            />
          </Form.Item>
        );
      },
    },
  ].filter(Boolean);

  const invRowSelection = {
    onChange: (
      selectedRowInvKeys: string[],
      selectedInvRows: [],
      info: any
    ) => {
      if (info.type !== "all") {
        if (selectedRowInvKeys.length > 0) {
          selectedRowInvKeys.forEach((item) => {
            generalInvoiceForm.validateFields([`amount${item}`]).then(() => {
              setSelectedRowInvData(selectedInvRows);
              setSelectedRowInvKeys(selectedRowInvKeys);
            });
          });
        } else {
          setSelectedRowInvData([]);
          setSelectedRowInvKeys([]);
        }
      }
    },
    // getCheckboxProps: (record: { disabled: any; status: any }) => {},
    selectedRowKeys: selectedRowInvKeys,
    onSelectAll: (isSelectedAll: boolean) => {
      if (isSelectedAll) {
        const invoiceIds = data.reMapInvoice.map(
          (item: { id: string }) => item.id
        );
        setSelectedRowInvKeys(invoiceIds);
        setSelectedRowInvData(data.reMapInvoice);
      } else {
        setSelectedRowInvKeys([]);
        setSelectedRowInvData([]);
      }
    },
  };

  const dnRowSelection = {
    onChange: (selectedRowDnKeys: string[], selectedDnRows: [], info: any) => {
      if (info.type !== "all") {
        if (selectedRowDnKeys.length > 0) {
          selectedRowDnKeys.forEach((item) => {
            generalDebitNoteForm.validateFields([`amount${item}`]).then(() => {
              setSelectedRowDnData(selectedDnRows);
              setSelectedRowDnKeys(selectedRowDnKeys);
            });
          });
        } else {
          setSelectedRowDnData([]);
          setSelectedRowDnKeys([]);
        }
      }
    },

    // getCheckboxProps: (record: { disabled: any; status: any }) => {},
    selectedRowKeys: selectedRowDnKeys,

    onSelectAll: (isSelectedAll: boolean) => {
      if (isSelectedAll) {
        const debitNoteIds = data.reMapDebitNote.map(
          (item: { id: string }) => item.id
        );
        setSelectedRowDnKeys(debitNoteIds);
        setSelectedRowDnData(data.reMapDebitNote);
      } else {
        setSelectedRowDnKeys([]);
        setSelectedRowDnData([]);
      }
    },
  };

  const cnRowSelection = {
    onChange: (selectedRowCnKeys: string[], selectedCnRows: [], info: any) => {
      if (info.type !== "all") {
        if (selectedRowCnKeys.length > 0) {
          selectedRowCnKeys.forEach((item) => {
            generalCreditNoteForm.validateFields([`amount${item}`]).then(() => {
              setSelectedRowCnData(selectedCnRows);
              setSelectedRowCnKeys(selectedRowCnKeys);
            });
          });
        } else {
          setSelectedRowCnData([]);
          setSelectedRowCnKeys([]);
        }
      }
    },

    // getCheckboxProps: (record: { disabled: any; status: any }) => {},
    selectedRowKeys: selectedRowCnKeys,

    onSelectAll: (isSelectedAll: boolean) => {
      if (isSelectedAll) {
        const CreditNoteIds = data.reMapCreditNote.map(
          (item: { id: string }) => item.id
        );
        setSelectedRowCnKeys(CreditNoteIds);
        setSelectedRowCnData(data.reMapCreditNote);
      } else {
        setSelectedRowCnKeys([]);
        setSelectedRowCnData([]);
      }
    },
  };

  const dnModelRowSelection = {
    onChange: (
      selectedRowDnModalKeys: string[],
      selectedRowDnModalData: []
    ) => {
      setSelectedRowDnModalData(selectedRowDnModalData);
      setSelectedRowDnModalKeys(selectedRowDnModalKeys);
    },
    getCheckboxProps: (record: { id: string; disabled: any; status: any }) => {
      const debitNoteIds = data?.reMapDebitNote.map((item: any) => item.id);
      if (debitNoteIds.includes(record.id)) {
        return { disabled: true };
      }
    },
    selectedRowKeys: selectedRowDnModalKeys,
  };

  const submitPayment = async (isFpxPaymentSelected: boolean = false) => {
    let totalAmount = 0;
    let paidAmount = 0;
    let invoiceDocument: InvoiceDocument[] = [];
    data.reMapInvoice.map((item: any) => {
      const amount = item.amount.toString().replace(/,/g, "");

      if (selectedRowInvKeys.includes(item.id)) {
        invoiceDocument.push({
          statementId: item.statementId !== "-" ? item.statementId : undefined,
          invoiceId: item.id,
          amount: parseFloat(amount),
        });
        totalAmount += parseFloat(amount);
      }
    });

    let debitNoteDocument: DebitNoteDocument[] = [];
    if (selectedRowDnKeys.length > 0) {
      data?.reMapDebitNote?.map((item: any) => {
        if (selectedRowDnKeys.includes(item.id)) {
          const amount = item.amount.toString().replace(/,/g, "");
          debitNoteDocument.push({
            debitNoteId: item.id,
            amount: parseFloat(amount),
          });
          totalAmount += parseFloat(amount);
        }
      });
    }

    let creditNoteDocument: CreditNoteDocument[] = [];
    if (selectedRowCnKeys.length > 0) {
      data?.reMapCreditNote?.map((item: any) => {
        if (selectedRowCnKeys.includes(item.id)) {
          const amount = item.amount.toString().replace(/,/g, "");
          creditNoteDocument.push({
            creditNoteId: item.id,
            amount: parseFloat(amount),
            paymentStatus: "PENDING",
          });
          totalAmount -= parseFloat(amount);
        }
      });
    }

    let paymentDetails: PaymentDetail[] = [];
    let form = new FormData();
    // Object.values(paymentData).forEach((item: any) => {
    //   paymentDetails.push({
    //     referenceNo: item.referenceNo,
    //     referenceDate: item.referenceDate ? moment(item.referenceDate).format("YYYY-MM-DDT00:00:00") + "Z" : moment(new Date()).format("YYYY-MM-DDT00:00:00") + "Z",
    //     type: item.method,
    //     amount: item.amount,
    //     paymentStatus: "PENDING",
    //     receipt: item.receiptImage || "",
    //   });
    //   if (item.receipt !== null) {
    //     // form.append("type", item.method);
    //     form.append("file", item.receiptImage);
    //   }
    // });
    // const picUploadDoc: any = (await apiHelper.POST("uploadFile", form, { "Content-Type": "multipart/form-data" })) || [];
    Object.values(paymentData).forEach((item: any) => {
      paymentDetails.push({
        referenceNo: item.referenceNo,
        referenceDate: item.referenceDate
          ? moment(item.referenceDate).format("YYYY-MM-DDT00:00:00") + "Z"
          : moment(new Date()).format("YYYY-MM-DDT00:00:00") + "Z",
        type: item.type,
        amount: item.amount,
        paymentStatus: "PENDING",
        receipt: [],
      });
      paidAmount += item.amount;

      if (item.receiptImage && item.receiptImage.length > 0) {
        item.receiptImage.forEach((image: any) => {
          // Append each image to the form
          form.append("file", image.originFileObj);
          // Push information about each image to the paymentDetails array
          paymentDetails[paymentDetails.length - 1]?.receipt?.push(image.name);
        });
      }
    });

    if (
      parseFloat(totalAmount.toFixed(2)) !==
      parseFloat(paidAmount.toFixed(2)) &&
      !isFpxPaymentSelected
    ) {
      MessageErrorUI("Please tally amount");
      setIsSubmitButtonLoading(false);
      return;
    }

    if (parseFloat(totalAmount.toFixed(2)) < 0) {
      MessageErrorUI("Total Amount cannot be less than 0");
      setIsSubmitButtonLoading(false);
      return;
    }

    const picUploadDoc: any =
      (await apiHelper.POST("uploadFile", form, {
        "Content-Type": "multipart/form-data",
      })) || [];

    paymentDetails.forEach((payment) => {
      payment.receipt = payment?.receipt?.map(
        (filename) => picUploadDoc.item[filename]
      );
    });

    if (isFpxPaymentSelected) {
      paymentDetails.push({
        referenceNo: "",
        referenceDate: moment(new Date()).format("YYYY-MM-DDT00:00:00") + "Z",
        type: "FPX",
        amount: finalAmountToPaid.toNumber(),
        paymentStatus: "PENDING",
        receipt: [],
      });
    }

    let dataSubmit = {
      companyId: retailerAccess?.companyId,
      companyBranchId: retailerAccess?.companyBranchId,
      outletId: currentOutletId,
      retailerId: retailerAccess?.id,
      date: new Date(),
      invoiceDocuments: invoiceDocument,
      creditNoteDocuments: creditNoteDocument,
      debitNoteDocuments: debitNoteDocument,
      paymentDetails: paymentDetails,
    };

    // if (!isFpxPayment) {
    // apiHelper
    //   .POST("payment", dataSubmit)
    //   ?.then((res: any) => {
    //     if (!isFpxPayment) {
    //       MessageSuccessUI(t("Payment.collection") + " " + t("Common.createSuccess"));
    //       router.push("/payment/paymentListing");
    //     } else {
    //       return res.item
    //     }
    //   })
    //   .catch((err) => {
    //     if (!isFpxPayment) {
    //       //* This Part need re-edit*//
    //       MessageErrorUI(t("Payment.collection") + " " + t("Common.createUnsuccess"));
    //     }
    //   });
    // }

    const apiCall: any = await apiHelper.POST("payment", dataSubmit);
    if (apiCall) {
      if (!isFpxPaymentSelected) {
        MessageSuccessUI(
          t("Payment.collection") + " " + t("Common.createSuccess")
        );
        setIsSubmitButtonLoading(false);
        router.push("/payment/paymentListing");
      } else {
        return apiCall.item;
      }
    } else {
      MessageErrorUI(
        t("Payment.collection") + " " + t("Common.createUnsuccess")
      );
      setIsSubmitButtonLoading(false);
    }
  };

  const handlePaymentDetailSubmit = (formSelected: any) => {
    const paymentFormData = formSelected.getFieldsValue();
    if (allFiles.length === 0) {
      MessageErrorUI("Please insert at least one document.");
      return;
    }
    const data: PaymentDetail = {
      referenceNo: paymentFormData.referenceNo ?? "",
      referenceDate:
        moment(paymentFormData.referenceDate).format("YYYY-MM-DDT00:00:00") +
        "Z",
      type: paymentFormData.type,
      amount: paymentFormData.amount ?? 0,
      receiptImage: previewImage,
    };
    setPaymentData({
      ...paymentData,
      [paymentType]: data,
    });
    // setPaymentData(paymentFormData);
    setIsPaymentModalOpen(false);
    setPreviewImage([]);
    setAllFiles([]);
  };

  const handleImageDisplay = (type: string) => {
    if (
      paymentData[type] &&
      "receiptImage" in paymentData[type] &&
      paymentData[type].receiptImage
    ) {
      setAllFiles(paymentData[type]?.receiptImage ?? []);
      setPreviewImage(paymentData[type]?.receiptImage ?? []);
    }
  };

  const handleModalClose = () => {
    setIsPaymentModalOpen(false);
  };

  const handleCancel = () => setPreviewOpen(false);

  const paymentModal = () => {
    const formSelected =
      paymentType === "CASH"
        ? cashPaymentForm
        : paymentType === "CHEQUE"
          ? chequePaymentForm
          : onlineTransferForm;
    return (
      <ModalUI
        title={
          paymentMethodOption.find((item) => item.value === paymentType)
            ?.label + " Modal"
        }
        width="80%"
        visible={isPaymentModalOpen}
        onOk={handleModalClose}
        onCancel={handleModalClose}
        destroyOnClose={true}
        content={
          <Form
            onFinish={() => handlePaymentDetailSubmit(formSelected)}
            className="w-full "
            form={formSelected}
            layout="vertical"
            scrollToFirstError
          >
            <div className="flex flex-col bg-white w-full rounded-[12px] p-2 space-y-6">
              <Col className="w-full space-y-3">
                <Row className="flex md:flex-row flex-col gap-x-4">
                  <Form.Item
                    name="type"
                    className="flex-1"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Payment.type") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Payment.type")}
                      </p>
                    }
                  >
                    <SelectInput
                      options={paymentMethodOption}
                      disabled={true}
                    // defaultValue={paymentType}
                    // onChange={(value: string) => {
                    //   setPaymentType(value);
                    //   if (value === "CASH")
                    //     cashPaymentForm.resetFields(["bankName"]);
                    // }}
                    />
                  </Form.Item>
                  {paymentType !== "CASH" ? (
                    <Form.Item
                      className="mb-1 flex-1"
                      name="bankName"
                      rules={[
                        {
                          required: true,
                          message:
                            t("Payment.bankName") +
                            " " +
                            t("Validation.requiredField"),
                        },
                      ]}
                      label={
                        <p className="text-neutral700 text-[12px]">
                          {t("Payment.bankName")}
                        </p>
                      }
                    >
                      <SelectInput options={bankListOption} />
                    </Form.Item>
                  ) : null}
                </Row>
                <Row className="flex md:flex-row flex-col gap-x-4">
                  <Form.Item
                    className="mb-1 flex-1"
                    name="amount"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Payment.paymentAmount") +
                          " " +
                          t("Validation.requiredField"),
                      },
                      {
                        validator(_, value) {
                          if (value === 0) {
                            return Promise.reject(
                              new Error(t("Payment.errorAmount0"))
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Payment.paymentAmount")}
                      </p>
                    }
                  >
                    <NumberInput
                      min={0}
                      max={100000000}
                      value={0.0}
                      defaultValue={0.0}
                      precision={5}
                    />
                  </Form.Item>
                  <Form.Item
                    className="mb-1 flex-1"
                    name="referenceNo"
                    // rules={[
                    //   {
                    //     required: true,
                    //     message:
                    //       t("Payment.referenceNo") + " " + t("Validation.requiredField"),
                    //   },
                    // ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Payment.referenceNo")}
                      </p>
                    }
                  >
                    <FormTextInput
                      placeholder={t("Payment.referenceNo")}
                      maxLength={50}
                      onInput={(e: any) =>
                        (e.target.value = e.target.value.toUpperCase())
                      }
                    />
                  </Form.Item>
                </Row>
                <Row className="flex md:flex-row flex-col gap-x-4">
                  <Form.Item
                    className="mb-1 flex-1"
                    name="referenceDate"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Payment.referenceDate") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Payment.referenceDate")}
                      </p>
                    }
                  >
                    <DatePicker
                      // disabledDate={disabledDate}
                      className="rounded-lg min-w-[120px] w-full"
                      onChange={() => {
                        // handleExpiryDate(item, index);
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    className="mb-1 flex-1"
                    name="receiptImage"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Payment.receipt") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Payment.receipt")}
                      </p>
                    }
                  >
                    <Upload
                      {...props}
                      onChange={(fileList: any) => {
                        formSelected.setFieldsValue({ receiptImage: fileList });
                        handleChange(fileList);
                        // setAllFiles(newFileList);
                      }}
                      className="custom-upload"
                    >
                      <div>
                        <PlusOutlined />
                        <p>{t("Payment.upload")}</p>
                      </div>
                    </Upload>
                    <Modal
                      open={previewOpen}
                      title={previewTitle}
                      footer={null}
                      onCancel={handleCancel}
                    >
                      <img
                        alt="example"
                        style={{ width: "100%" }}
                        src={previewImageDisplay}
                      />
                    </Modal>
                  </Form.Item>
                </Row>
              </Col>
              <Row className="flex flex-row gap-x-3 pt-8">
                <PrimaryButtonUI
                  label={
                    paymentData[paymentType] &&
                      Object.keys(paymentData[paymentType]).length > 0
                      ? t("Common.update")
                      : t("Common.ok")
                  }
                  htmlType="submit"
                />
                <SecondaryButtonUI
                  label={
                    paymentData[paymentType] &&
                      Object.keys(paymentData[paymentType]).length > 0
                      ? t("Common.remove")
                      : t("Common.cancel")
                  }
                  // htmlType="reset"
                  onClick={() => {
                    if (!paymentData[paymentType]) {
                      formSelected.resetFields();
                    } else {
                      // Set Back the value to its formField
                      // formSelected.setFieldsValue({
                      //   type: paymentData[paymentType].type,
                      //   amount: paymentData[paymentType].amount,
                      //   referenceNo: paymentData[paymentType].referenceNo,
                      //   referenceDate: moment(paymentData[paymentType].referenceDate),
                      // });

                      delete paymentData[paymentType];
                      formSelected.resetFields();
                    }
                    setIsPaymentModalOpen(false);
                    setAllFiles([]);
                    setPreviewImage([]);
                  }}
                />
              </Row>
            </div>
          </Form>
        }
      ></ModalUI>
    );
  };

  const handleStatusMenuClick: MenuProps["onClick"] = ({ key }) => {
    const items = statusFilterOption1;
    setStatusKey(key);
    // Access the label property of the selected item
    const selectedLabel = items.find(
      (menuItem: any) => menuItem.key === key
    )?.label;
    if (selectedLabel) {
      setStatusValue(selectedLabel);
    }
  };

  const dnFilterFormOnfinish = (values: any) => {
    setDnModalFilter(values);
  };

  const searchDN = (values: any) => {
    //convert to empty string when no value is entered as default value is undefined.
    for (const key in values) {
      if (values[key] === undefined) {
        values[key] = "";
      }
    }

    // return true or false
    let isAnyKeyFilled = Object.keys(values).some(
      (key) => values[key] !== "" && values[key] !== undefined
    );

    const params =
      encodeParams({
        fuzzySearch: values.fuzzySearch,
        type: values.type || "",
        debitNoteNo: values.debitNoteNo || "",
        status: values.status,
      }) + "&status=ACTIVE&balanceGT=0&outletId=65053189279a8e1102a71348";

    if (isAnyKeyFilled) {
      setCursor("0");
      setDnFilterSetting(params);
    }
  };

  const filterDNModal = () => {
    return (
      <div className="w-full">
        <Form
          onFinish={dnFilterFormOnfinish}
          form={filterCNModalForm}
          className=""
          layout="vertical"
        >
          <h1 className="font-bold text-base pb-4">{t("Filter")}</h1>
          {/* First Row of Filter Input */}
          <Row className="filterBlockForm flex-col space-y-3">
            <Row className="flex flex-row gap-x-4">
              <Form.Item
                name="debitNoteNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("DebitNote.debitNote") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={
                    t("Common.eg") +
                    " " +
                    t("DebitNote.debitNote") +
                    " " +
                    t("Common.no")
                  }
                  maxLength={30}
                />
              </Form.Item>
              <Form.Item
                name="type"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("CreditNote.DebitNote") +
                      "?"}
                  </p>
                }
              >
                <SelectInput
                  placeholder={t("Common.eg") + " " + t("DebitNote.debitType")}
                  options={creditTypeOption}
                />
              </Form.Item>
            </Row>
          </Row>
          <Row className="flex flex-row space-x-3 pt-8 justify-between">
            <PrimaryButtonUI
              label={t("Common.resetAll")}
              onClick={() => {
                filterDNModalForm.resetFields();
                // setParamsFromLocalStorage(router.pathname, `status=${statusKey}`, "productFilter");
              }}
            />
            <Row>
              <SecondaryButtonUI
                label={t("Common.cancel")}
                htmlType="reset"
                onClick={() => {
                  setDnModalFilter({});
                  setIsFilterDNModalOpen(false);
                }}
              />
              <PrimaryButtonUI
                label={t("Common.applyFilter")}
                htmlType="submit"
                onClick={() => {
                  setIsFilterDNModalOpen(false);
                }}
              />
            </Row>
          </Row>
        </Form>
      </div>
    );
  };

  const showDnModal = () => {
    return (
      <div className="w-full">
        <Row className="mb-4 w-full">
          <FilterFormComponent
            filterForm={filterDNForm}
            onDebouncedChange={(value) => {
              filterDNModalForm.resetFields();
              setDnModalFilter({});
              setDnFuzzySearchFilter(value);
            }}
            fieldName={dnFieldName}
            clearButtonOnChange={() => {
              filterDNForm.resetFields();
              setStatusKey("ALL");
              setStatusValue("All");
              setDnFuzzySearchFilter("");
              setDnModalFilter({});
              filterDNModalForm.resetFields();
              // setData([...fullData]);
              setShowClearFilter(false);
              // setCursor(tempCursor);
              setDnFilterSetting("");
              localStorage.removeItem("dnFilter");
            }}
            filterModalButtonOnClick={() => {
              setIsFilterDNModalOpen(true);
              filterDNForm.resetFields();
              setDnFuzzySearchFilter("");
            }}
            modalFilterValue={dnModalFilter}
            option={statusFilterOption1}
            handleStatusMenuClick={handleStatusMenuClick}
            clearFilterDisable={showClearFilter === true ? false : true}
            statusValue={statusValue}
            debounceValue={dnFuzzySearchFilter}
          ></FilterFormComponent>
        </Row>
        <Col>
          <ListingTableUI
            // EditableCell={EditableCell}
            bordered
            dataSource={debitNote}
            columns={dnColumn}
            // rowClassName="editable-row"
            rowKey="id"
            rowSelection={dnModelRowSelection}
            cursor={false}
            // loader={showButtonLoader}
            pagination={false}
            endMessage={""}
          />
        </Col>
        <Row className="flex gap-x-2 justify-end pt-3">
          <PrimaryButtonUI
            htmlType="submit"
            label={t("Submit")}
            onClick={() => {
              const tempData = cloneDeep(data);
              let filterData = cloneDeep(debitNote);
              if (selectedRowDnModalData) {
                selectedRowDnModalData.forEach((item) => {
                  tempData.reMapDebitNote.unshift(item);
                  filterData = filterData.filter(
                    (val: any) => item.id !== val.id
                  );
                });
              }
              setDebitNote(filterData);
              setData(tempData);
              setIsDnModalOpen(false);
              setSelectedRowDnModalKeys([]);
              setSelectedRowDnModalData([]);
            }}
          />

          <SecondaryButtonUI
            label={t("Cancel")}
            className=" buttonStyle secondaryButtonBg"
            onClick={() => {
              setIsDnModalOpen(false);
              setSelectedRowDnModalKeys([]);
              setSelectedRowDnModalData([]);
            }}
          />
        </Row>
      </div>
    );
  };

  // const handleFPXmodal = () => {
  //   setIsFPXModalStatusOn(false)
  // }

  // const fpxStatusCheckingModal = (paymentData: any, paymentId: string) => {
  //   <ModalUI
  //     title="FPX Payment Status"
  //     width="60%"
  //     // visible={isFPXModalStatusOn}
  //     visible={true}
  //     onCancel={() => setIsFPXModalStatusOn(false)}
  //     destroyOnClose={true}
  //     content={
  //       <Form
  //         className="w-full "
  //         form={fpxModalForm}
  //         layout="vertical"
  //         scrollToFirstError
  //       >
  //         <div className="flex flex-col bg-white w-full rounded-[12px] p-2 space-y-6">
  //           <Col className="w-full">
  //             <h1> {paymentData.CurrencyID + " " + paymentData.TotalAmt + " is pending to Proceed"}</h1>
  //             <p> Current status: {getStatusStyles(fpxStatus)} </p>
  //           </Col>
  //           <Row className="flex flex-row space-x-3 pt-8">
  //             <PrimaryButtonUI
  //               label={
  //                 fpxStatus === "PENDING" ? "Check Payment Status" : "I've done approved/rejected the transaction."
  //               }
  //               onClick={callFPXstatus(paymentId)}
  //             />
  //             <SecondaryButtonUI
  //               label={
  //                 t("Modal.close")
  //               }
  //               // htmlType="reset"
  //               onClick={() => {
  //                 setIsFPXModalStatusOn(false)
  //                 router.push("/payment/paymentListing");
  //               }}
  //             />
  //           </Row>
  //         </div>
  //       </Form>
  //     }
  //   ></ModalUI>
  // };

  // *************************************************************************************
  // *** Filter Modal ***
  // *************************************************************************************

  const filterFormOnfinish = (values: any) => {
    setModalFilter(values);
  };

  const searchCN = (values: any) => {
    //convert to empty string when no value is entered as default value is undefined.
    for (const key in values) {
      if (values[key] === undefined) {
        values[key] = "";
      }
    }

    // return true or false
    let isAnyKeyFilled = Object.keys(values).some(
      (key) => values[key] !== "" && values[key] !== undefined
    );

    const params =
      encodeParams({
        fuzzySearch: values.fuzzySearch,
        creditNoteNo: values.creditNoteNo || "",
        type: values.type || "",
        invoiceNo: values.invoiceNo || "",
        status: values.status,
      }) + "&status=ACTIVE&balanceGT=0&outletId=65053189279a8e1102a71348";

    if (isAnyKeyFilled) {
      setCursor("0");
      setCnFilterSetting(params);
    }
  };

  const filterCNModal = () => {
    return (
      <div className="w-full">
        <Form
          onFinish={filterFormOnfinish}
          form={filterCNModalForm}
          className=""
          layout="vertical"
        >
          <h1 className="font-bold text-base pb-4">{t("Filter")}</h1>
          {/* First Row of Filter Input */}
          <Row className="filterBlockForm flex-col space-y-3">
            <Row className="flex flex-row gap-x-4">
              <Form.Item
                name="creditNoteNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("CreditNote.creditNote") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={
                    t("Common.eg") +
                    " " +
                    t("CreditNote.creditNote") +
                    " " +
                    t("Common.no")
                  }
                  maxLength={30}
                />
              </Form.Item>
              <Form.Item
                name="type"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("CreditNote.creditType") +
                      "?"}
                  </p>
                }
              >
                <SelectInput
                  placeholder={
                    t("Common.eg") + " " + t("CreditNote.creditType")
                  }
                  options={creditTypeOption}
                />
              </Form.Item>
            </Row>

            <Row className="flex flex-row gap-x-4">
              <Form.Item
                name="invoiceNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("CreditNote.invoice") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={
                    t("Common.eg") + " " + t("PlaceHolder.invoiceNo")
                  }
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item name="" className="mb-0 flex-1">
                {/* <RangePickerInput /> */}
              </Form.Item>
            </Row>
          </Row>
          <Row className="flex flex-row space-x-3 pt-8 justify-between">
            <PrimaryButtonUI
              label={t("Common.resetAll")}
              onClick={() => {
                filterCNModalForm.resetFields();
                // setParamsFromLocalStorage(router.pathname, `status=${statusKey}`, "productFilter");
              }}
            />
            <Row>
              <SecondaryButtonUI
                label={t("Common.cancel")}
                htmlType="reset"
                onClick={() => {
                  setModalFilter({});
                  setIsFilterCNModalOpen(false);
                }}
              />
              <PrimaryButtonUI
                label={t("Common.applyFilter")}
                htmlType="submit"
                onClick={() => {
                  setIsFilterCNModalOpen(false);
                }}
              />
            </Row>
          </Row>
        </Form>
      </div>
    );
  };

  const cnModelRowSelection = {
    onChange: (
      selectedRowCnModalKeys: string[],
      selectedRowCnModalData: []
    ) => {
      setSelectedRowCnModalData(selectedRowCnModalData);
      setSelectedRowCnModalKeys(selectedRowCnModalKeys);
    },
    getCheckboxProps: (record: { id: string; disabled: any; status: any }) => {
      const creditNoteIds = data?.reMapCreditNote.map((item: any) => item.id);
      if (creditNoteIds.includes(record.id)) {
        return { disabled: true };
      }
    },
    selectedRowKeys: selectedRowCnModalKeys,
  };

  const showCnModal = () => {
    return (
      <div className="w-full">
        <Row className="mb-4 w-full">
          <FilterFormComponent
            filterForm={filterCNForm}
            onDebouncedChange={(value) => {
              filterCNModalForm.resetFields();
              setModalFilter({});
              setFuzzySearchFilter(value);
            }}
            fieldName={fieldName}
            clearButtonOnChange={() => {
              filterCNForm.resetFields();
              setStatusKey("ALL");
              setStatusValue("All");
              setFuzzySearchFilter("");
              setModalFilter({});
              filterCNModalForm.resetFields();
              // setData([...fullData]);
              setShowClearFilter(false);
              // setCursor(tempCursor);
              setCnFilterSetting("");
              localStorage.removeItem("cnFilter");
            }}
            filterModalButtonOnClick={() => {
              setIsFilterCNModalOpen(true);
              filterCNForm.resetFields();
              setFuzzySearchFilter("");
            }}
            modalFilterValue={modalFilter}
            option={statusFilterOption1}
            handleStatusMenuClick={handleStatusMenuClick}
            clearFilterDisable={showClearFilter === true ? false : true}
            statusValue={statusValue}
            debounceValue={fuzzySearchFilter}
          ></FilterFormComponent>
        </Row>
        <Col>
          <ListingTableUI
            // EditableCell={EditableCell}
            bordered
            dataSource={creditNote}
            columns={cnColumn}
            // rowClassName="editable-row"
            rowKey="id"
            rowSelection={cnModelRowSelection}
            cursor={false}
            // loader={showButtonLoader}
            pagination={false}
            endMessage={""}
          />
        </Col>
        <Row className="flex gap-x-2 justify-end pt-3">
          <PrimaryButtonUI
            htmlType="submit"
            label={t("Submit")}
            onClick={() => {
              const tempData = cloneDeep(data);
              let filterData = cloneDeep(creditNote);
              const newKeys = cloneDeep(selectedRowCnKeys);
              const newRowData = cloneDeep(selectedRowCnData);
              if (selectedRowCnModalData) {
                selectedRowCnModalData.forEach((item) => {
                  tempData.reMapCreditNote.unshift(item);
                  filterData = filterData.filter(
                    (val: any) => item.id !== val.id
                  );
                  newKeys.push(item.id);
                  newRowData.push(item);
                });
              }
              setCreditNote(filterData);
              setData(tempData);
              // setSelectedRowCnKeys(newKeys);
              // setSelectedRowCnData(newRowData);
              setIsCnModalOpen(false);
              setSelectedRowCnModalKeys([]);
              setSelectedRowCnModalData([]);
            }}
          />
          <SecondaryButtonUI
            label={t("Cancel")}
            className=" buttonStyle secondaryButtonBg"
            onClick={() => {
              setIsCnModalOpen(false);
              setSelectedRowCnModalKeys([]);
              setSelectedRowCnModalData([]);
            }}
          />
        </Row>
      </div>
    );
  };

  const showContent = () => {
    const keys = Object.keys(paymentData);
    const formattedKeys = keys
      .map(
        (key) => paymentMethodOption.find((item) => item.value === key)?.label
      )
      .join(", ");

    let totalPaymentMethodAmount = 0;
    Object.keys(paymentData).forEach((key) => {
      totalPaymentMethodAmount += paymentData[key]?.amount ?? 0;
    });

    let paid: Decimal = new Decimal(0);
    paid = new Decimal(totalPayment).minus(
      new Decimal(totalPaymentMethodAmount)
    );
    if (!finalAmountToPaid.equals(paid)) {
      setFinalAmountToPaid(paid);
    }
    const totalPaymentAmountFixedTwo = parseFloat(totalPayment.toFixed(2));
    const isPaidAll =
      NumberThousandSeparator(
        totalPaymentAmountFixedTwo - totalPaymentMethodAmount
      ) === "0.00";
    return (
      <div>
        <Row className="w-full flex items-center pb-5">
          <BackButtonUI title={t("Payment.toPay")} buttons={[]}></BackButtonUI>
        </Row>
        <Row className="pb-8">
          <div>
            <p className="text-xl font-bold mb-3"> Invoice</p>
          </div>
          <Form form={generalInvoiceForm} className="w-full">
            <ListingTableUI
              dataSource={data.reMapInvoice}
              columns={invColumn}
              rowSelection={invRowSelection}
              rowKey="id"
              subtotal={
                invoiceTotal ? NumberThousandSeparator(invoiceTotal) : "0.00"
              }
              endMessage={""}
            />
          </Form>
        </Row>
        <Row className="pb-8">
          <Row className="flex flex-row justify-between w-full">
            <p className="text-xl font-bold mb-3">Debit Note</p>
            <PrimaryButtonUI
              label={t("Payment.addDebitNote")}
              onClick={() => setIsDnModalOpen(true)}
            />
          </Row>
          <Form form={generalDebitNoteForm} className="w-full">
            <ListingTableUI
              dataSource={data?.reMapDebitNote}
              columns={dnColumn}
              rowSelection={dnRowSelection}
              rowKey="id"
              subtotal={dnTotal ? NumberThousandSeparator(dnTotal) : "0.00"}
              endMessage={""}
            />
          </Form>
        </Row>
        <Row className="pb-8">
          <Row className="flex flex-row justify-between w-full">
            <p className="text-xl font-bold mb-3">Credit Note</p>
            <PrimaryButtonUI
              label={t("Payment.addCreditNote")}
              onClick={() => setIsCnModalOpen(true)}
            />
          </Row>
          <Form form={generalCreditNoteForm} className="w-full">
            <ListingTableUI
              dataSource={data?.reMapCreditNote}
              columns={cnColumn}
              rowSelection={cnRowSelection}
              rowKey="id"
              subtotal={cnTotal ? NumberThousandSeparator(cnTotal) : "0.00"}
              endMessage={""}
            />
          </Form>
        </Row>
        <Row className="flex bg-white pb-8">
          <div className="flex-1">
            <div className="text-xl font-bold p-3">Payment Summary</div>
            <div className="flex flex-row justify-between p-5">
              <Row>Subtotal amount to pay</Row>
              <Row>RM {NumberThousandSeparator(invoiceTotal)}</Row>
            </div>
            <div className="flex flex-row justify-between p-5">
              <Row>Total Debit Note</Row>
              <Row>RM {NumberThousandSeparator(dnTotal)}</Row>
            </div>
            <div className="flex flex-row justify-between p-5">
              <Row>Total Credit Note</Row>
              <Row className="text-red-600">
                - RM {NumberThousandSeparator(cnTotal)}
              </Row>
            </div>
            <div className="flex flex-row justify-between p-5">
              <Row>
                Total Payment {formattedKeys ? `(${formattedKeys})` : null}{" "}
              </Row>
              <Row className="text-red-600">
                - RM {NumberThousandSeparator(totalPaymentMethodAmount)}
              </Row>
            </div>

            <Divider type="horizontal" className="mb-4" />
            <div className="font-bold p-3 pb-8 flex flex-row justify-between">
              <Row className="text-xl">Total Amount</Row>
              <Row className="text-xl">
                RM{" "}
                {NumberThousandSeparator(
                  totalPaymentAmountFixedTwo - totalPaymentMethodAmount
                )}
              </Row>
            </div>
          </div>
        </Row>
        <Row className="flex flex-row items-center pt-8 justify-between">
          <div>
            <div className="text-xl font-bold flex flex-row pr-5">
              Payment Method
            </div>
            <div className="gap-4 flex flex-wrap pt-2">
              <ClickableCard
                isClicked={isFPXClicked}
                title={t("Payment.FPXPayment")}
                imageComponent={
                  <FpxIcon
                    width="40"
                    height="50"
                    viewBox="0 0 90 80"
                    src={FpxIcon.src}
                  />
                }
                // onClick={handleFPXPayment}
                onClick={() => setIsFPXClicked(!isFPXClicked)}
              />
              <ClickableCard
                isClicked={paymentData["CASH"] ? true : false}
                className="justify-center"
                title={t("Payment.cash")}
                imageComponent={
                  <CreditNoteIcon
                    width="40"
                    height="50"
                    viewBox="0 0 90 80"
                    src={CreditNoteIcon.src}
                  />
                }
                onClick={() => {
                  handleImageDisplay("CASH");
                  setIsPaymentModalOpen(true);
                  setPaymentType("CASH");
                  cashPaymentForm.setFieldValue("type", "CASH");
                }}
              />
              <ClickableCard
                isClicked={paymentData["CHEQUE"] ? true : false}
                title={t("Payment.cheque")}
                imageComponent={
                  <ChequeIcon
                    width="40"
                    height="50"
                    viewBox="0 0 45 12"
                    src={ChequeIcon.src}
                  />
                }
                onClick={() => {
                  handleImageDisplay("CHEQUE");
                  setIsPaymentModalOpen(true);
                  setPaymentType("CHEQUE");
                  chequePaymentForm.setFieldValue("type", "CHEQUE");
                }}
              />
              <ClickableCard
                isClicked={paymentData["ONLINETRANSFER"] ? true : false}
                title={t("Payment.onlineTransfer")}
                imageComponent={
                  <PaymentIcon
                    width="40"
                    height="50"
                    viewBox="0 0 90 80"
                    src={PaymentIcon.src}
                  />
                }
                onClick={() => {
                  handleImageDisplay("ONLINETRANSFER");
                  setIsPaymentModalOpen(true);
                  setPaymentType("ONLINETRANSFER");
                  onlineTransferForm.setFieldValue("type", "ONLINETRANSFER");
                }}
              />
            </div>
          </div>
          <div className="mt-4 w-full md:w-[130px]">
            <PrimaryButtonUI
              className="w-full"
              label={isFPXClicked ? "Submit along FPX" : "Submit"}
              onClick={() => {
                setIsSubmitButtonLoading(true);
                isFPXClicked ? handleFPXPayment() : submitPayment();
              }}
              disabled={
                selectedRowInvKeys.length === 0
                  ? true
                  : isSubmitButtonLoading
                    ? true
                    : isFPXClicked
                      ? finalAmountToPaid.equals(0) || invoiceTotal === 0
                      : !isPaidAll || invoiceTotal === 0
              }
              loading={isSubmitButtonLoading}
            />
          </div>
        </Row>
      </div>
    );
  };

  const [statusColor, statusBackgroundColor, statusBorderColor] =
    getStatusStyles(fpxStatus);
  return (
    <div className="min-h-screen bg-bgOrange min-w-full">
      <Header items={headerItems} hasSearch={false} values={() => { }} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
      </Content>
      <ModalUI
        title={t("Payment.addDebitNote")}
        width="80%"
        visible={isDnModalOpen}
        // onOk={handleOk}
        onCancel={() => setIsDnModalOpen(false)}
        content={showDnModal()}
      ></ModalUI>
      <ModalUI
        title={t("Payment.addCreditNote")}
        width="80%"
        visible={isCnModalOpen}
        // onOk={handleOk}
        onCancel={() => setIsCnModalOpen(false)}
        content={showCnModal()}
      ></ModalUI>
      <ModalUI
        // title={"More Filter"}
        width="70%"
        className={"modalFilterBody"}
        visible={isFilterCNModalOpen}
        onOk={() => setIsFilterCNModalOpen(false)}
        onCancel={() => setIsFilterCNModalOpen(false)}
        content={filterCNModal()}
        title={""}
      ></ModalUI>
      <ModalUI
        // title={"More Filter"}
        width="70%"
        className={"modalFilterBody"}
        visible={isFilterDNModalOpen}
        onOk={() => setIsFilterDNModalOpen(false)}
        onCancel={() => setIsFilterDNModalOpen(false)}
        content={filterDNModal()}
        title={""}
      ></ModalUI>
      {paymentModal()}
      <ModalUI
        maskClosable={false}
        closable={false}
        title="FPX Payment Status"
        width="50%"
        visible={isFPXModalStatusOn}
        onCancel={() => setIsFPXModalStatusOn(false)}
        destroyOnClose={true}
        content={
          <>
            <Form
              className="w-full "
              form={fpxModalForm}
              layout="vertical"
              scrollToFirstError
            >
              <div className="flex flex-col bg-white w-full rounded-[12px] p-2 space-y-6">
                <Col className="w-full">
                  <h1>
                    {" "}
                    {fpxPaymentData.CurrencyID +
                      " " +
                      fpxPaymentData.TotalAmt +
                      " is pending to proceed."}
                  </h1>
                  <br />
                  <Col className="w-full flex items-center">
                    <span className="mr-2">Current status:</span>
                    <p
                      className={`tableRowNameDesign statusTag ${statusColor} ${statusBackgroundColor} ${statusBorderColor} text-lg`}
                    >
                      {fpxStatus}
                    </p>
                  </Col>
                </Col>
                <Row className="flex flex-row space-x-3 pt-8 justify-end">
                  <PrimaryButtonUI
                    label={
                      fpxStatus === "PENDING"
                        ? "Check Payment Status"
                        : "I've done approved/rejected the transaction."
                    }
                    onClick={() => {
                      callFPXstatus(paymentId);
                    }}
                  />
                  <SecondaryButtonUI
                    label={t("Modal.close")}
                    // htmlType="reset"
                    onClick={() => {
                      setIsFPXModalStatusOn(false);
                      router.push("/payment/paymentListing");
                    }}
                  />
                </Row>
              </div>
            </Form>
          </>
        }
      ></ModalUI>
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default ToPay;
