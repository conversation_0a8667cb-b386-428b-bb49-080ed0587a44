import React, { useState } from "react";
import { UploadOutlined } from "@ant-design/icons";
import { Upload, message as MessageErro<PERSON>, <PERSON><PERSON> } from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import {
  RcFile,
  UploadChangeParam,
  UploadFile,
  UploadProps,
} from "antd/lib/upload/interface";
import { useTranslation } from "next-i18next";

interface UploadPictureProps {
  value: string;
  onChange: (value: string, files: RcFile[]) => void;
  onRemove?: () => void;
}

interface UploadFileProps {
  value: string;
  onChange: (value: string, files: RcFile[]) => void;
  onRemove?: () => void;
}

export const UploadPicture: React.FC<UploadPictureProps> = ({
  value,
  onChange,
  onRemove,
}) => {
  const [files, setFiles] = useState<UploadFile<any>[]>([]);
  const { t } = useTranslation("common");

  const getBase64 = (file: RcFile): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }
  };

  const handleChange = (info: UploadChangeParam<UploadFile<any>>) => {
    let fileList = info.fileList;

    // Limit to 2 files
    fileList = fileList.slice(-2);

    setFiles(fileList);

    // Notify parent about the changes
    onChange(
      value,
      fileList.map((file) => file.originFileObj as RcFile)
    );
  };

  const handleBeforeUpload = (file: RcFile) => {
    if (
      !["image/png", "image/jpg", "image/jpeg", "application/pdf"].includes(
        file.type
      )
    ) {
      MessageError.error(
        `${file.name} is an invalid file format. Please change the file extension to either .pdf, .png, .jpg, .jpeg.`
      );
      return Upload.LIST_IGNORE;
    } else if (file.size > 5242880) {
      MessageError.error(
        `${file.name} is too large. Please upload another document that is smaller than 5MB.`
      );
      return Upload.LIST_IGNORE;
    } else {
      return false;
    }
  };

  const props: UploadProps = {
    name: "file",
    fileList: files,
    listType: "picture-card",
    onPreview: handlePreview,
    showUploadList: {
      showPreviewIcon: false,
    },
    beforeUpload: handleBeforeUpload,
    onChange: handleChange,
    onRemove: onRemove,
  };

  return (
    <Upload {...props}>
      {files.length < 2 && (
        <div>
          <PlusOutlined />
          <div className="mt-2 w-min">{t("Login.signUp.uploadPic")}</div>
        </div>
      )}
    </Upload>
  );
};

export const UploadFiles: React.FC<UploadFileProps> = ({
  value,
  onChange,
  onRemove,
}) => {
  const [files, setFiles] = useState<UploadFile<any>[]>([]);
  const { t } = useTranslation("common");

  const handleBeforeUpload = (file: RcFile) => {
    if (file.size > 5242880) {
      MessageError.error(
        `${file.name} is too large. Please upload another document that is smaller than 5MB.`
      );
      return Upload.LIST_IGNORE;
    } else {
      return false;
    }
  };

  const handleChange = (info: UploadChangeParam<UploadFile<any>>) => {
    let fileList = info.fileList;

    // Limit to 2 files
    fileList = fileList.slice(-2);

    setFiles(fileList);

    // Notify parent about the changes
    onChange(
      value,
      fileList.map((file) => file.originFileObj as RcFile)
    );
  };

  // Upload props
  const props: UploadProps = {
    name: "file",
    fileList: files,
    listType: "text", // Change listType to 'text' for displaying file names
    beforeUpload: handleBeforeUpload,
    onChange: handleChange,
    onRemove: onRemove,
  };

  return (
    <Upload {...props}>
      <Button icon={<UploadOutlined />}>{t("Login.signUp.uploadFiles")}</Button>
    </Upload>
  );
};
