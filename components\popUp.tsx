import React, { useState, useEffect } from "react";
import { useRouter } from "next/router"; // Import useRouter from Next.js

interface PopUpProps {
  imageURL: string;
  onClose: () => void;
  targetRoute: string;
}

const PopUp: React.FC<PopUpProps> = ({ imageURL, onClose, targetRoute }) => {
  const router = useRouter(); // Access the router
  const [isClosed, setIsClosed] = useState<boolean>(false);

  useEffect(() => {
    // Check if pop-up should be shown based on localStorage when component mounts
    const popupClosed = localStorage.getItem("popupClosed");
    if (popupClosed) {
      setIsClosed(true);
    }
  }, []);

  const handleClick = () => {
    router.push(targetRoute); // Use router.push for navigation
    onClose(); // Close the popup
    setIsClosed(true); // Set the flag indicating pop-up has been closed
    localStorage.setItem("popupClosed", "true"); // Store flag in localStorage
  };

  // Check if pop-up should be shown based on localStorage
  if (isClosed) {
    return null; // Don't render the pop-up if it's closed or flag is set
  }

  return (
    <div className="fixed top-0 left-0 w-full h-full flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-transparent p-8 rounded-lg relative">
        <span
          className="absolute top-[10px] right-[20px] p-4 cursor-pointer text-3xl text-white"
          onClick={onClose}
        >
          &times;
        </span>
        {/* Clickable image invoking the handleClick function */}
        <img
          src={imageURL}
          alt="PopUp Image"
          className="bg-transparent cursor-pointer" // Added cursor-pointer class
          onClick={handleClick}
          style={{ maxWidth: "100%", maxHeight: "80vh" }} // Adjust image size as needed
        />
      </div>
    </div>
  );
};

export default PopUp;
