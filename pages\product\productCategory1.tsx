import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import { Col, Layout, Pagination, Row, Spin } from "antd";
import Header, { supportedLocales } from "../../components/header";
import { ProductBlock } from "@/components/ui";
import noImage from "../../assets/sampleImage/NoImagePlaceholder.jpg";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import {
  CompanyGeneralInfo,
  Outlet,
  OutletProductList,
  OutletPromotion,
  Product,
  ProductCategories,
  ProductPriceGroup,
  ProductTradeInfo,
  Promotion,
  Retailer,
} from "@/components/type";
import { DataSource, PUBLIC_BUCKET_URL, encodeParams } from "@/stores/utilize";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import moment from "moment";
import { FilterSidebar } from "@/components/sidebar";
import AppFooter from "@/components/footer";

function productCategory() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [outletInfo, setOutletInfo] = useState<Outlet>();
  // const [outletPromotionProductList, setOutletPromotionProductList] = useState<
  //   OutletPromotion[]
  // >([]);
  // const [allPromotionList, setAllPromotionList] = useState<Promotion[]>([]);
  // const [allProductList, setAllProductList] = useState<Product[]>([]);
  // const [allProductPriceGroup, setAllProductPriceGroup] = useState<
  //   ProductPriceGroup[]
  // >([]);
  // const [companyData, setCompanyData] = useState<CompanyGeneralInfo[]>([]);
  const [productCategoriesRoot, setProductCategoriesRoot] = useState<
    ProductCategories[]
  >([]);
  const [selectedFilteredCategory, setSelectedCategory] = useState(null);
  const [inputValue, setInputValue] = useState("");
  const [isDataReady, setIsDataReady] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [paginatedData, setPaginatedData] = useState<Product[]>([]);
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({}); //access control
  const [productMap, setProductMap] = useState(new Map());
  const [companyMap, setCompanyMap] = useState(new Map());
  // const [productPriceMap, setProductPriceMap] = useState(new Map());
  // const [productCatalogueMap, setProductCatalogueMap] = useState(new Map());
  const [pageSize, setPageSize] = useState(50); // Default page size is 50

  const [outletProductListId, setOutletProductListId] = useState<string[]>([]);
  const [productSearch, setProductSearch] = useState([]);

  const passedRouter = useRouter();
  // const queryInfo = passedRouter.query;

  useEffect(() => {
    if (router.isReady && Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }
    }
  }, [router.isReady, Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0) {
      getProductCategoriesRoot();
      getOutletProduct();
    }
  }, [retailerAccess]);

  useEffect(() => {
    if (productMap.size === 0 || paginatedData.length > 0) {
      setIsDataReady(true);
    }
  }, [paginatedData, productMap]);

  useEffect(() => {
    // when that two product has value.
    if (
      currentPage &&
      productMap.size &&
      !Object.keys(passedRouter.query).length
    ) {
      setPaginatedData(getPaginatedData(currentPage) as Product[]);
    }
  }, [currentPage, productMap, passedRouter.query]);

  useEffect(() => {
    // when that two product has value.
    if (Object.keys(passedRouter.query).length) {
      getProductFuzzySearch();
    }
  }, [passedRouter.query]);

  const headerItems = [
    {
      label: t("Header.home"),
      route: "/landing",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: "Product Category",
      route: "/product/productCategory1",
      className: "labelTextStyle",
    },
  ];

  const handleToProduct = (route: any, product: any) => {
    router.push({
      pathname: route,
      query: {
        productId: product?.id,
        productInfo: JSON.stringify(product),
      },
    });
  };

  const getProductCategoriesRoot = async () => {
    try {
      const dataSource = new DataSource(
        "productCategories",
        "&sort=createdAt&sortOrder=-1&isRootCategory=TRUE",
        true
      );
      const res: any = await dataSource.load();
      const categories = res?.length ? [...res] : [];
      setProductCategoriesRoot(categories);
      return categories;
    } catch (err) {
      return [];
    }
  };

  const getProductByCategories = async () => {
    const categoryId = router.query.categoryId;
    if (!categoryId) return [];
    try {
      const params = {
        categoryIds: categoryId,
        status: "ACTIVE",
      };
      const dataSource = new DataSource(
        "productCategories",
        encodeParams(params),
        true
      );
      const res: any = await dataSource.load();
      return res?.items || [];
    } catch (err) {
      return [];
    }
  };

  const getOutletProduct = () => {
    // RetailerAcces outletIds can more than 50 ?
    // if yes need to adjsut this function.
    // future need to change for user choose per outlet.
    let params: any = {
      sort: "createdAt",
      sortOrder: "-1",
      id: localStorage.getItem("currentOutletId"),
    };
    const dataSource = new DataSource("outlets", encodeParams(params), false);
    dataSource.load().then(async (res: any) => {
      if (!res?.items?.length) {
        // stop loading here.
        return;
      }

      const objectMap = res.items.reduce(
        (accumulator: any, current: Outlet) => {
          accumulator["companyId"] = accumulator["companyId"] || [];

          if (
            current.companyId &&
            !companyMap.has(current.companyId) &&
            !accumulator["companyId"].includes(current.companyId)
          ) {
            accumulator["companyId"].push(current.companyId ?? "");
          }

          current.outletProductList?.reduce(
            (acc: any, product: OutletProductList) => {
              accumulator["productId"] = accumulator["productId"] || [];
              if (
                product.productId &&
                !productMap.has(product.productId) &&
                !accumulator["productId"].includes(product.productId)
              ) {
                accumulator["productId"].push(product.productId ?? "");
              }
              return acc;
            },
            {}
          );
          return accumulator;
        },
        {}
      );

      const outlet: Outlet = res.items[0];

      delete outlet.outletProductList;

      setOutletInfo(outlet);

      const company = await getCompany(objectMap["companyId"]);
      if (company?.length) {
        setOutletProductListId(objectMap["productId"]);
        getProduct(objectMap["productId"], 0, company[0].id, outlet);
      }
    });
  };

  const getCompany = async (id: string[] = []) => {
    try {
      const params: any = {
        status: "ACTIVE",
        id: [],
      };
      params.id = id?.splice(0, 50);
      const dataSource = new DataSource(
        "companies",
        encodeParams(params),
        false
      );
      const res: any = await dataSource.load();
      if (res !== null && res.items.length > 0) {
        const newDataMap = new Map(companyMap);

        res.items.forEach((item: CompanyGeneralInfo) => {
          if (!newDataMap.has(item.id)) {
            newDataMap.set(item.id, item);
          }
        });

        setCompanyMap(newDataMap);
      }
      return res?.items || [];
    } catch (err) {
      return [];
    }
  };

  const getProduct = async (
    id: string[] = [],
    spliceNumber = 0,
    companyId?: string,
    outlet?: Outlet
  ) => {
    try {
      const { priceGroupId, companyId } = outlet || outletInfo || {};
      const productId = id.splice(spliceNumber * 50, 50);
      const params = {
        id: productId,
      };
      const productPriceGroups = await getProductTradeInfo(
        productId,
        companyId,
        priceGroupId
      );
      const promotionRelated = await getPromotionProduct(productId);
      const promotions = await getPromotion(promotionRelated);

      const dataSoruce = new DataSource(
        "productCatalogues",
        encodeParams(params),
        false
      );
      const res: any = await dataSoruce.load();
      // skip it when does not has product.
      if (!res?.items.length) return;

      const newDataMap = new Map(productMap);

      res.items.forEach((item: Product) => {
        const product = productPriceGroups?.find(
          (price) => price.productCatalogueId === item.id
        );
        const promotionProduct = promotionRelated?.find(
          (promotion) => promotion.productId === item.id
        );
        let isSinglePromo = false,
          isBundlePromo = false;
        if (promotionProduct) {
          promotions.map((promotion) => {
            if (promotionProduct.promoIds.includes(promotion?.id || "")) {
              if ((promotion?.productGroups?.length || 0) > 1)
                isBundlePromo = true;
              if ((promotion?.productGroups?.length || 0) === 1)
                isSinglePromo = true;
            }
          });
        }
        const { sellingPrice, tradeType } = product || {};
        newDataMap.set(item.id, {
          ...item,
          sellingPrice,
          isSinglePromo,
          isBundlePromo,
          priceGroupId,
          outletCompanyId: companyId,
          tradeType,
        });
      });

      setProductMap(newDataMap);

      return res.items;
    } catch (err) {
      return [];
    }
  };

  const getProductTradeInfo = async (
    id: string[] = [],
    companyId?: string,
    priceGroupId?: string
  ): Promise<any[]> => {
    // productTradeInfos/flatten
    try {
      if (!id?.length) return [];

      const companyMapId = Array.from(companyMap.keys());
      // need to change only can access one outlet only.
      const params = {
        productCatalogueId: id,
        companyId: companyMapId?.length ? companyMapId[0] : companyId,
        status: "ACTIVE",
      };
      const dataSource = new DataSource(
        "productTradeInfos",
        encodeParams(params),
        true
      );
      const res: any = await dataSource.load();
      if (!res?.length) return [];

      // group params by uomId

      const groupDefaultUOMId = res.reduce(
        (accum: any, current: ProductTradeInfo) => {
          accum[current.defaultUOMId] = accum[current.defaultUOMId] || [];
          accum[current.defaultUOMId].push(current.productCatalogueId);
          return accum;
        },
        {}
      );

      const arrayGroup = Object.keys(groupDefaultUOMId).map((key) => {
        return {
          uomId: key,
          productId: groupDefaultUOMId[key],
        };
      });

      const results = await getProductPriceGroup(
        arrayGroup,
        companyId,
        priceGroupId
      );

      const groupTradeInfoAndProduct = res.map(
        (tradeInfo: ProductTradeInfo) => {
          const price = results.find(
            (item) =>
              item.productCatalogueId === tradeInfo.productCatalogueId &&
              item.productUOMId === tradeInfo.defaultUOMId
          );
          return Object.assign(tradeInfo, price);
        }
      );

      return groupTradeInfoAndProduct;
    } catch (err) {
      return [];
    }
  };

  const getProductPriceGroup = async (
    tradeInfo: GroupTradeInfo[],
    companyId?: string,
    priceGroupId?: string
  ): Promise<ProductPriceGroup[]> => {
    let results: any = [];
    if (!tradeInfo.length) return results;

    const companyMapId = Array.from(companyMap.keys());
    for (let i = 0; i < tradeInfo.length; i++) {
      const item: any = tradeInfo[i];
      let { uomId, productId } = item;
      while (productId?.length) {
        const params = {
          activeAt: `${moment()
            .startOf("day")
            .format("YYYY-MM-DD")}T00:00:00.000Z`,
          productCatalogueId: productId.splice(0, 50),
          productUOMId: uomId,
          companyId: companyMapId?.length ? companyMapId[0] : companyId,
          priceGroupId: priceGroupId || outletInfo?.priceGroupId,
        };

        const dataSource = new DataSource(
          "productPriceGroups",
          encodeParams(params),
          true
        );
        const res: any = await dataSource.load().catch(() => {
          productId = [];
        });

        if (res?.length) {
          results = results.concat(res);
        }
      }
    }

    return results;
  };

  const getPromotionProduct = async (
    id: string[] = []
  ): Promise<OutletPromotion[]> => {
    try {
      if (!id.length) return [];

      const params = {
        outletId: localStorage.getItem("currentOutletId"),
        productId: id,
      };
      const dataSource = new DataSource(
        "promotion/outlets",
        encodeParams(params),
        false
      );
      const res: any = await dataSource.load();
      if (res?.items?.length) {
        return res.items;
      }
      return [];
    } catch (err) {
      return [];
    }
  };

  const getPromotion = async (
    promotionRelatedOutlet: OutletPromotion[]
  ): Promise<Promotion[]> => {
    try {
      if (!promotionRelatedOutlet.length) return [];

      let groupPromotionId: Array<string> = [];
      promotionRelatedOutlet.map((promotion) => {
        promotion.promoIds.map((item) => {
          if (!groupPromotionId.includes(item)) {
            groupPromotionId.push(item);
          }
        });
      });

      if (!groupPromotionId.length) return [];

      let results: Array<Promotion> = [];

      while (groupPromotionId.length) {
        const params = {
          id: groupPromotionId.splice(0, 50),
        };
        const dataSource = new DataSource(
          "promotion",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch((err) => {
          groupPromotionId = [];
        });
        results = results.concat(res?.items || []);
      }

      return results;
    } catch (err) {
      return [];
    }
  };

  const getProductFuzzySearch = async () => {
    try {
      const { priceGroupId, companyId } = outletInfo || {};
      const value = passedRouter?.query?.product || "";
      const params = {
        fuzzySearch: value,
      };
      const dataSoruce = new DataSource(
        "productCatalogues",
        encodeParams(params),
        false
      );
      const res: any = await dataSoruce.load();
      const products = res?.items || [];
      const productId = products.map((item: Product) => item.id);

      const productPriceGroups = await getProductTradeInfo(
        productId,
        companyId,
        priceGroupId
      );
      const promotionRelated = await getPromotionProduct(productId);
      const promotions = await getPromotion(promotionRelated);

      products.forEach((item: any) => {
        const product = productPriceGroups?.find(
          (price) => price.productCatalogueId === item.id
        );
        const promotionProduct = promotionRelated?.find(
          (promotion) => promotion.productId === item.id
        );
        let isSinglePromo = false,
          isBundlePromo = false;
        if (promotionProduct) {
          promotions.map((promotion) => {
            if (promotionProduct.promoIds.includes(promotion?.id || "")) {
              if ((promotion?.productGroups?.length || 0) > 1)
                isBundlePromo = true;
              if ((promotion?.productGroups?.length || 0) === 1)
                isSinglePromo = true;
            }
          });
        }
        const { sellingPrice, tradeType } = product || {};
        return {
          ...item,
          sellingPrice,
          isSinglePromo,
          isBundlePromo,
          priceGroupId,
          outletCompanyId: companyId,
          tradeType,
        };
      });

      setProductSearch(products);
    } catch (err) {
      console.log(err);
    }
  };

  // const getProduct = async (id: string[] = []) => {
  //   let tempProductMap = new Map(productMap);
  //   if (!id?.length) return tempProductMap;

  //   const params: any = {
  //     //   status: "ACTIVE",
  //     id: [],
  //     // categoryId: router.query.categoryId,
  //     // sellingType: "SELLING"
  //   };

  //   try {
  //     while (id?.length) {
  //       params.id = id?.splice(0, 50);
  //       const dataSource = new DataSource("productCatalogues", encodeParams(params), false);
  //       const res: any = await dataSource.load().catch(() => {
  //         id = [];
  //       });
  //       if (res !== null && res.items.length > 0) {
  //         setProductMap((prevDataMap) => {
  //           const newDataMap = new Map(prevDataMap);
  //           res.items.forEach((item: Product) => {
  //             if (!newDataMap.has(item.id)) {
  //               newDataMap.set(item.id, item);
  //             }
  //           });
  //           return newDataMap;
  //         });
  //         res.items?.map((item: Product) => {
  //           tempProductMap.set(item.id, item);
  //         });
  //       }
  //     }
  //   } catch (err) {
  //     return tempProductMap;
  //   }
  //   return tempProductMap;
  // };

  const handleCategoryClick = (category: any) => {
    setSelectedCategory(category.id); // Update state with the selected category
    router.push({
      pathname: "/product/productCategory1",
      query: { category: category.name, categoryId: category.id },
    });
  };

  const getPaginatedData = (page: number) => {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    // due to the productMap is get from the getProduct, it does not get full product from outlet show that need to check and split it.
    if (endIndex > productMap.size) {
      const subset = Array.from(productMap.values()).slice(
        productMap.size - pageSize,
        productMap.size
      );
      return subset;
    } else {
      const subset = Array.from(productMap.values()).slice(
        startIndex,
        endIndex
      );
      return subset;
    }
  };

  const handlePageChange = async (newPage: number, newPageSize: number) => {
    await getProduct(outletProductListId, newPage);
    setCurrentPage(newPage);
    setPageSize(newPageSize);
  };

  // const handlePageSizeChange = (current: number, newPageSize: number) => {
  //   setCurrentPage(current);  // Update the current page when changing page size
  //   setPageSize(newPageSize);
  // };

  return (
    <div className="flex-1">
      <Header
        items={headerItems}
        hasSearch={true}
        values={(item: any) => setInputValue(item)}
        fieldValue={router.query.product ? router.query.product : inputValue}
      />
      <div className="flex flex-1 ml-32 mr-32">
        <div className="w-1/6 mt-5 pr-6">
          <FilterSidebar
            categories={productCategoriesRoot}
            onHandleClick={handleCategoryClick}
          />
        </div>
        <div className="w-5/6 mt-5">
          {paginatedData.length === 0 && !productSearch.length ? (
            <Spin tip="Loading" size="large">
              <div className="content mt-8 text-primaryBlue" />
            </Spin>
          ) : (
            // <p className="text-primaryBlue flex justify-center items-center">
            //   Loading...
            // </p>
            paginatedData.length > 0 &&
            !productSearch.length && (
              <div>
                <Layout>
                  <Row className="bg-lightPurple">
                    {paginatedData.map(
                      (product: Product & ProductPriceGroup, index: number) => {
                        const defaultUOM = product?.productUOM?.find(
                          (uom: any) => uom.isDefaultUOM === "TRUE"
                        );
                        const defaultPictures = defaultUOM?.pictures || null;
                        // const companyName = companyData.find(
                        //   (item: CompanyGeneralInfo) =>
                        //     item.id === product?.filterDefaultUOM[0].companyId
                        // );
                        return (
                          <Col
                            key={index}
                            onClick={() =>
                              handleToProduct("/product/productDetail", product)
                            }
                            className="product-col"
                          >
                            <ProductBlock
                              productName={product?.name || "EMPTY"}
                              product={product}
                              price={
                                product.sellingPrice || 0.0
                                // product?.filterDefaultUOM[0].costPrice ||
                              }
                              // companyName={
                              //   // companyName?.name ||
                              //   "EMPTY"}
                              image={
                                (defaultPictures &&
                                  PUBLIC_BUCKET_URL + defaultPictures[0]) ||
                                noImage.src
                              }
                            />
                          </Col>
                        );
                      }
                    )}
                  </Row>
                </Layout>
                <div className="pagination-container mb-5">
                  <Pagination
                    current={currentPage}
                    onChange={handlePageChange}
                    pageSize={pageSize}
                    total={outletProductListId?.length}
                    pageSizeOptions={["50"]}
                    showSizeChanger={false}
                  // onShowSizeChange={handlePageSizeChange}
                  />
                </div>
              </div>
            )
          )}
          {productSearch.length > 0 && (
            <div>
              <Layout>
                <Row className="bg-lightPurple">
                  {productSearch.map(
                    (product: Product & ProductPriceGroup, index: number) => {
                      const defaultUOM = product?.productUOM?.find(
                        (uom: any) => uom.isDefaultUOM === "TRUE"
                      );
                      const defaultPictures = defaultUOM?.pictures || null;
                      // const companyName = companyData.find(
                      //   (item: CompanyGeneralInfo) =>
                      //     item.id === product?.filterDefaultUOM[0].companyId
                      // );
                      return (
                        <Col
                          key={index}
                          onClick={() =>
                            handleToProduct("/product/productDetail", product)
                          }
                          className="product-col"
                        >
                          <ProductBlock
                            productName={product?.name || "EMPTY"}
                            product={product}
                            price={
                              product.sellingPrice || 0.0
                              // product?.filterDefaultUOM[0].costPrice ||
                            }
                            // companyName={
                            //   // companyName?.name ||
                            //   "EMPTY"}
                            image={
                              (defaultPictures &&
                                PUBLIC_BUCKET_URL + defaultPictures[0]) ||
                              noImage.src
                            }
                          />
                        </Col>
                      );
                    }
                  )}
                </Row>
              </Layout>
            </div>
          )}
        </div>
      </div>
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

type GroupTradeInfo = {
  uomId: string;
  productId: any;
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default productCategory;
