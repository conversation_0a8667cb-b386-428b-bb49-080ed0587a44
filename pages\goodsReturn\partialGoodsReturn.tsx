import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import Header, { supportedLocales } from "../../components/header";
import {
  BackButtonUI,
  CounterComponent,
  ForgotPasswordPreviousButtonUI,
  PrimaryButtonUI,
  SecondaryButtonUI,
} from "@/components/buttonUI";
import { useRouter } from "next/router";
import {
  ArrowUpOutlined,
  EditFilled,
  LeftOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  SearchOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import {
  DataSource,
  NumberThousandSeparator,
  PUBLIC_BUCKET_URL,
  encodeParams,
  formateDate,
} from "@/stores/utilize";
import {
  BatchDetail,
  CompanyGeneralInfo,
  GoodsReturn,
  GoodsReturnProduct,
  Invoice,
  InvoiceToteBoxes,
  Outlet,
  Product,
  ProductOrdered,
  ProductUOM,
  Retailer,
  SelectOption,
  ToteBoxDetail,
  UOM,
} from "@/components/type";
import {
  <PERSON><PERSON>,
  Col,
  FloatButton,
  Form,
  Row,
  Spin,
  Tooltip,
  Upload,
  UploadProps,
} from "antd";
import defaultImage from "../../assets/default/emptyImage.png";
import {
  DebounceFilterTextInput,
  FormTextInput,
  SelectInput,
  SingleDateInput,
} from "@/components/input";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import moment from "moment";
import apiHelper from "../api/apiHelper";
import {
  ListingTableUI,
  MessageErrorUI,
  MessageInfoUI,
  MessageSuccessUI,
} from "@/components/ui";
import { returnModeOption } from "@/components/config";
import { ModalUI } from "@/components/modalUI";
import _, { capitalize, cloneDeep, map } from "lodash";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import dayjs from "dayjs";
import AppFooter from "@/components/footer";
import { UploadFile } from "antd/lib";

function PartialGoodsReturn() {
  interface SelectedInvoiceProps {
    invoiceNo: string;
    companyId: string;
    productId: string;
    goodReturnDetails: {
      reasonId: string;
      returnQuantity: number;
    };
    badReturnDetails: {
      reasonId: string;
      returnQuantity: number;
    };
    productLineDetails: ProductOrdered[];
    productDetails: Product;
  }

  type ReturnConditionType = "GOOD" | "BAD";

  const { t } = useTranslation("common");
  const router = useRouter();
  const [form] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [validationQtyForm] = Form.useForm();
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [showButtonLoader, setShowButtonLoader] = useState(false);
  const [cursor, setCursor] = useState("");
  const [tableLoading, setTableLoading] = useState(false);
  const [fuzzySearchData, setFuzzySearchData] = useState("");
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  const [currentStep, setCurrentStep] = useState(0);
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  const [data, setData] = useState<any[]>([]);
  const [companyMap, setCompanyMap] = useState(new Map());
  const [outletMap, setOutletMap] = useState(new Map());
  const [uomMap, setUomMap] = useState(new Map());
  const [uomOptionMap, setUomOptionMap] = useState(new Map());
  const [reasonOption, setReasonOption] = useState<SelectOption[]>([]);

  const [selectedProduct, setSelectedProduct] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [invoiceData, setInvoiceData] = useState<any[]>([]);
  const [editedInvoiceData, setEditedInvoiceData] = useState<any[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRowData, setSelectedRowData] = useState<any[]>([]);
  const [outletData, setOutletData] = useState<Outlet>({});
  const [productUOMConversion, setProductUOMConversion] = useState(new Map());
  const [editedData, setEditedData] = useState<any[]>([]);
  const [selectedProductRowKeys, setSelectedProductRowKeys] = useState<any[]>(
    []
  );
  // const [selectedProductRowData, setSelectedProductRowData] = useState<
  //   Product[]
  // >([]);
  // const [allFiles, setAllFiles] = useState<{ [key: string]: any }>({});
  const [allFiles, setAllFiles] = useState<UploadFile[]>([]);
  const [companyProductMap, setCompanyProductMap] = useState(new Map());
  const [returnDateDisable, setReturnDateDisable] = useState(false);

  const [productExpiryDate, setProductExpiryDate] = useState<any>(new Map());
  // const [number, setNumber] = useState(0);
  const [fetchingInvoice, setFetchingInvoice] = useState<boolean>(false);
  const [selectedInvoiceData, setSelectedInvoiceData] = useState<
    Map<string, SelectedInvoiceProps[] | undefined>
  >(new Map());
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  // use the retailStoreState;
  const outletId = useRetailerStore((state) => state.currentOutletData?.id);

  const disabledDate = (current: any) => {
    // Can not select days before today and today
    return current && current < dayjs().endOf("day");
  };

  const headerItems = [
    {
      label: t("Header.dashboard"),
      route: "/profile/dashboard",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Header.goodsReturn"),
      route: "/goodsReturn/goodsReturnListing",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Header.partialGoodsReturn"),
      route: "/goodsReturn/partialGoodsReturn",
      className: "labelTextStyle",
    },
  ];

  useEffect(() => {
    // Function to check screen size and update state
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 500); // Define your breakpoint for small screens
    };

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Call handleResize initially to set initial screen size
    handleResize();

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    if (router.isReady && Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }
      getProduct();
      getReasonsOption();
    }
  }, [router.isReady, Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollButton(window.pageYOffset > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    if (cursor === "") return;
    const handleScroll = () => {
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight - 50;
      const scrollPosition = window.scrollY;
      if (windowHeight + scrollPosition >= documentHeight) {
        // Stop API calling when cursor is equal to '0'
        if (cursor !== "0") {
          getProduct();
        }
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [cursor]);

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  useEffect(() => {
    // Check params whether is same
    if (fuzzySearchData) {
      getProduct(true);
    } else {
      getProduct(true);
    }
  }, [fuzzySearchData]);

  useEffect(() => {
    if (uomMap.size > 0) {
      const expectedResult = data?.map((item: Product) => {
        const value = item.productUOM?.map((uom) => {
          const uomInfo = uomMap.get(uom.productUOMId);
          return {
            value: uom.productUOMId,
            label: uomInfo ? uomInfo.name : "Unknown",
          };
        });

        return {
          key: item.id,
          value,
        };
      });

      const expectedResultMap = new Map<
        string,
        { value: string; label: string }[]
      >();
      expectedResult?.forEach((item: any) => {
        expectedResultMap.set(item.key, item.value);
      });

      uomOptionMap.forEach((value: any, key: any) => {
        expectedResultMap.set(key, value);
      });

      // Set the 'expectedResultMap' in the 'uomOptionMap' state
      setUomOptionMap(expectedResultMap);
    }
  }, [uomMap, data]);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0 && outletId) {
      getOutletData(outletId);
    }
  }, [retailerAccess, outletId]);

  // *************************************************************************************
  // *** Call API ***
  // *************************************************************************************

  let isLoading = false;

  const getProduct = (isRefresh = false) => {
    // const accessBranches = otherViewBranchAccess === false ? retailerAccess.companyBranchIds : otherCompanyBranchesIds;
    if (isLoading) return;
    setTableLoading(true);
    setShowButtonLoader(true);
    isLoading = true;
    setTimeout(() => {
      let params: any = {
        sort: "createdAt",
        sortOrder: "-1",
        status: "ACTIVE",
      };
      if (isRefresh === false) {
        params.cursor = cursor;
      }
      if (fuzzySearchData) {
        params.fuzzySearch = fuzzySearchData;
      }
      const checkFilterRights = fuzzySearchData
        ? encodeParams(params) +
          (cursor && cursor !== "0" ? `&cursor=${cursor}` : "")
        : encodeParams(params);
      // if (!isAdmin) {
      //   params.companyBranchIds = accessBranches;
      // }
      // const checkAdminRights = isAdmin ? params : params + "&companyId=" + retailerAccess.companyId;
      // const checkFilterRights = filterSetting && !isClearFilter ? filterSetting : encodeParams(params);
      const dataSource = new DataSource(
        "productCatalogues",
        checkFilterRights,
        false
      );

      // !isAdmin && !otherViewBranchAccess ? filterForm.setFieldValue("companyId", retailerAccess.companyId) : null;

      // if ((accessBranches && accessBranches.length > 0) || isAdmin) {
      dataSource
        .load()
        .then((res: any) => {
          if (res && res.items !== null) {
            let data = res.items;
            let latestProductUOMConversion = new Map(productUOMConversion);
            const objectMap = data.reduce(
              (accumulator: any, current: Product) => {
                // if not yet has product uom conversion for the record.
                if (!latestProductUOMConversion.has(current.id)) {
                  latestProductUOMConversion.set(
                    current.id,
                    current.productUOM || []
                  );
                }

                current.productUOM?.reduce((acc: any, product: ProductUOM) => {
                  accumulator["productUOMId"] =
                    accumulator["productUOMId"] || [];
                  if (
                    product.productUOMId &&
                    !uomMap.has(product.productUOMId) &&
                    !accumulator["productUOMId"].includes(product.productUOMId)
                  ) {
                    accumulator["productUOMId"].push(
                      product.productUOMId ?? ""
                    );
                  }

                  return acc;
                }, {});

                return accumulator;
              },
              {}
            );

            // set the loaded product and the conversion .
            setProductUOMConversion(latestProductUOMConversion);

            // getOutlets(objectMap["outletId"]);
            // getProduct(objectMap["productId"]);
            getUOM(objectMap["productUOMId"]);
            // getCompanyBranch(objectMap["companyBranchId"]);
            const after = res.items.map((item: any) => {
              const replaceData = editedData.find((val) => val.id === item.id);
              if (replaceData) {
                return replaceData;
              } else {
                return item;
              }
            });

            if (res.cursor !== cursor || isRefresh) {
              // Avoid duplicates
              if (!isRefresh) {
                // setFullData((prevData) => [...prevData, ...data]);
                setData((prevData) => [...prevData, ...after]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              } else {
                // setFullData([...data]);
                setData([...after]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              }
            }
            // setData(after);
            setCursor(res.cursor);
          }
        })
        .catch(() => {
          //* This Part need re-edit*//
        });
      // } else {
      //   setGoodsReturnListingInfoData([]);
      // }
    }, 500);
  };

  // this function is to get invoice after selected product and search related Invoices.

  const getInvoice = async (record: any, selectedProductId: string) => {
    const smallestUOMId = record?.productUOM.find(
      (item: ProductUOM) => item.isSmallest === "TRUE"
    )?.productUOMId;

    if (!record || !record.id || !smallestUOMId) {
      console.error("Invalid product record or UOM.");
      return [];
    }

    const params: any = {
      productId: record.id,
      productUOMId: smallestUOMId,
      quantity: record.quantity || 1,
      companyId: retailerAccess.companyId,
      outletId: localStorage.getItem("currentOutletId"),
    };

    try {
      setIsModalOpen(true);
      setFetchingInvoice(true);

      const dataSource = new DataSource(
        "invoices",
        encodeParams(params),
        false
      );
      const res: any = await dataSource.load();

      const latestCompanyData = new Map(companyMap);

      if (res?.items?.length > 0) {
        const restructureInvoiceData = res.items.map((invoice: Invoice) => {
          let totalReturnableQuantity = 0;
          let totalProcessingQuantity = 0;
          let productLineDetails: ProductOrdered[] = [];

          // only set  company data if no appear before.
          if (!companyMap.has(invoice.companyId)) {
            latestCompanyData.set(invoice.companyId, {
              companyName: invoice.companyName,
              companyCode: invoice.companyCode,
            });
          }

          // Accumulate quantities for invoice products
          const invoiceProducts = invoice.invoiceProducts || [];

          invoiceProducts
            .filter(
              (item: ProductOrdered) => item.productId === selectedProductId
            )
            .forEach((item: ProductOrdered) => {
              totalProcessingQuantity += item.processingQuantity || 0;
              totalReturnableQuantity += item.returnableQuantity || 0;
              productLineDetails.push(item);
            });

          // Accumulate quantities for totebox products
          const invoiceToteboxes = invoice.invoiceToteboxes || [];
          const toteboxProducts = invoiceToteboxes.flatMap(
            (box: InvoiceToteBoxes) => box.toteboxProducts || []
          );
          toteboxProducts
            .filter(
              (item: ProductOrdered) => item.productId === selectedProductId
            )
            .forEach((item: ProductOrdered) => {
              totalProcessingQuantity += item.processingQuantity || 0;
              totalReturnableQuantity += item.returnableQuantity || 0;
              productLineDetails.push(item);
            });

          return {
            ...invoice,
            totalReturnableQuantity,
            totalProcessingQuantity,
            productLineDetails,
          };
        });

        setCompanyMap(latestCompanyData);
        setInvoiceData(restructureInvoiceData);
      } else {
        console.warn("No invoice data found for the provided product.");
        MessageErrorUI(t("GoodsReturn.noInvoiceFound"));
      }
    } catch (err) {
      console.error("Error fetching invoice data:", err);
      MessageErrorUI(t("GoodsReturn.fetchError"));
    } finally {
      setFetchingInvoice(false);
    }

    return [];
  };

  const getOutlets = async (id: string[] = []) => {
    let tempProductMap = new Map(outletMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "outlets",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch((error) => {
          id = [];
        });
        if (res !== null && res.items.length > 0) {
          setOutletMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Outlet) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }

    return tempProductMap;
  };

  const getCompany = async (id: string[] = []) => {
    let tempProductMap = new Map(outletMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      // status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "companies",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch((error) => {
          id = [];
        });
        if (res !== null && res.items.length > 0) {
          setCompanyMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: CompanyGeneralInfo) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Outlet) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }

    return tempProductMap;
  };

  const getUOM = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("uoms", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setUomMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: UOM) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: UOM) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getOutletData = async (id: string[] = []) => {
    const dataSource = new DataSource(
      "outlets",
      encodeParams({ id: id }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setOutletData(res.items?.[0]);
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getReasonsOption = () => {
    const dataSource = new DataSource(
      "reason/outletGoodsReturn/create",
      "status?=ACTIVE",
      true
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null) {
          let reasonOption: SelectOption[] = [];
          if (res !== null) {
            res.map((value: any) => {
              reasonOption.push({
                value: value.id,
                label: value.description,
              });
            });
          }
          setReasonOption(reasonOption);
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const submitGoodsReturn = async () => {
    try {
      setIsSubmitting(true);
      await form.validateFields();

      const formValue = form.getFieldsValue();
      const goodsReturnDataArray: GoodsReturn[] = [];

      // Prepare the goods return data

      // {Array.from(companyProductMap.entries()).map(([key, value]) => {

      Array.from(companyProductMap.entries()).map(([key, value]) => {
        const remapProduct: GoodsReturnProduct[] = [...value];
        const d = new Date(formValue[`returnDate${key}`]);

        const newCommonGoodsReturnData: GoodsReturn = {
          returnDate: moment(d).format("YYYY-MM-DDT00:00:00") + "Z",
          returnMode: formValue[`returnMode${key}`],
          outletId: localStorage.getItem("currentOutletId") || "",
          retailerId: retailerAccess.id,
          remark: formValue[`remark${key}`],
          products: remapProduct,
          companyId: retailerAccess.companyId,
          companyBranchId: outletData?.companyBranchId || "",
          staffId: "",
        };

        goodsReturnDataArray.push(newCommonGoodsReturnData);
      });

      // Handle file uploads and submit goods return data
      for (const item of goodsReturnDataArray) {
        try {
          // Upload files
          const formData: any = new FormData();

          if (allFiles.length > 0) {
            for (let i = 0; i < allFiles.length; i++) {
              formData.append("file", allFiles[i].originFileObj);
            }
          }

          const uploadResponse: any = await apiHelper.POST(
            "uploadFile",
            formData,
            {
              "Content-Type": "multipart/form-data",
            }
          );

          const document: string[] = Object.values(uploadResponse.item);
          const data = {
            ...item,
            goodsReturnDocuments: document,
          };
          await apiHelper.POST("goodsReturn", data);
          MessageSuccessUI(t("Common.Approval.Submit.Success"));
          router.push("/goodsReturn/goodsReturnListing");
        } catch (uploadError) {
          // Handle file upload or goods return submission errors
          MessageErrorUI(t("Common.Approval.Submit.Failed"));
        }
      }
    } catch (validationError) {
      // Handle form validation errors
      MessageErrorUI(t("Login.signUp.notFillRequiredFields"));
    } finally {
      setIsSubmitting(false);
    }
  };

  // *************************************************************************************
  // *** Invoice ***
  // *************************************************************************************

  const rowSelection = {
    preserveSelectedRowKeys: true,

    onChange: (selectedRowKeys: string[], selectedRows: any[]) => {
      // Update selected row keys directly

      setSelectedRowKeys(() => selectedRowKeys);

      // Update selected row data directly
      setSelectedRowData(() => selectedRows);
    },
    getCheckboxProps: (record: any) => {
      return {
        disabled:
          record.totalReturnableQuantity - record.totalProcessingQuantity <= 0,
      };
    },

    selectedRowKeys: selectedRowKeys,
  };

  const calculateBatchDetatils = (
    qty: number,
    batchDetails: BatchDetail[],
    currentUomConvert: number
  ) => {
    batchDetails?.map((batchDetail: BatchDetail) => {
      if (batchDetail.batchQuantity) {
        batchDetail.batchQuantity =
          batchDetail.batchQuantity * currentUomConvert;
        return batchDetail;
      }
    });
    for (let i = 0; i < batchDetails.length; i++) {
      const availableQty = batchDetails[i].batchQuantity ?? 0;
      if (qty >= availableQty) {
        batchDetails[i].batchQuantity = availableQty;
        qty -= availableQty;
      } else if (qty !== 0 && availableQty >= qty) {
        batchDetails[i].batchQuantity = qty;
        qty = 0;
      } else {
        batchDetails[i].batchQuantity = 0;
        // break; // Exit the loop if qty cannot be completely filled
      }
    }

    return batchDetails;
  };

  // Loop all the edited invoice check good, bad reason is filled when quantity is adjust more than 0
  const handleOk = () => {
    let tempData: any = new Map(selectedInvoiceData);
    let hasError = false; // Flag to track validation errors

    // Loop through the Map
    for (const [key, value] of tempData) {
      value.forEach((entry: any) => {
        // Check good return details
        if (
          entry?.goodReturnDetails?.returnQuantity > 0 &&
          !entry?.goodReturnDetails?.reasonId
        ) {
          hasError = true;

          MessageErrorUI(
            "If the quantity is already selected, a reason must also be provided for good product."
          );

          return;
        }

        // Check bad return details
        if (
          entry?.badReturnDetails?.returnQuantity > 0 &&
          !entry?.badReturnDetails?.reasonId
        ) {
          hasError = true;

          MessageErrorUI(
            "If the quantity is already selected, a reason must also be provided for bad product."
          );
          return;
        }
      });
    }

    if (!hasError) {
      setIsModalOpen(false);
      MessageSuccessUI("Record Update successful.");
    }
  };

  // const handleOk = (ids: string[]) => {
  //   let pass = true;

  //   let tempData = [...data];

  //   const array: any[] = [];
  //   //   //remap for each selected product
  //   for (const val of ids) {
  //     const matchProduct = tempData.findIndex((item) => item.id === val);
  //     let eachInvoiceData: any = {};

  //     //loop for invoice
  //     selectedRowKeys.forEach((invoiceId) => {
  //       const invoice = _.cloneDeep(
  //         editedInvoiceData.find((item) => item.id === invoiceId)
  //       );
  //       if (!invoice) {
  //         MessageErrorUI(t("GoodsReturn.chooseInvoiceError"));
  //         return false;
  //       }

  //       tempData[matchProduct].invoiceDetail = [];
  //       //general info
  //       eachInvoiceData.invoiceId = invoice.id;
  //       eachInvoiceData.invoiceNo = invoice.invoiceNo;
  //       eachInvoiceData.outletId = invoice.outletId;
  //       eachInvoiceData.company = invoice.companyId;
  //       eachInvoiceData.returnType = "PARTIAL";
  //       eachInvoiceData.expiryDate = tempData[matchProduct].expiryDate;

  //       const unitKey = Array.from(uomMap.entries()).find(([key, value]) => {
  //         return value.name === "UNT";
  //       });
  //       const unitId: string = unitKey ? uomMap.get(unitKey[0]).id : "";

  //       const product = invoice.invoiceProducts?.find(
  //         (item: ProductOrdered) => item.productId === tempData[matchProduct].id
  //       );

  //       // this is the uom using from the invoice line.
  //       const invoiceProductUOMId = product?.productUOMId;

  //       eachInvoiceData.returnUOMId = product?.returnableUOMId;
  //       //product info
  //       eachInvoiceData.type = product?.type;
  //       eachInvoiceData.quantity = product?.quantity;
  //       eachInvoiceData.productId = tempData[matchProduct].id;
  //       eachInvoiceData.productUOMId = product?.productUOMId;
  //       //price info
  //       if (product?.taxId) {
  //         eachInvoiceData.taxId = product?.taxId;
  //       }
  //       eachInvoiceData.taxRate = product?.taxRate;

  //       const thisProductSmallestUOM = tempData[matchProduct].productUOM.find(
  //         (item: ProductUOM) => item.isSmallest === "TRUE"
  //       )?.productUOMId;

  //       let conversionNumber = 1;
  //       // calculate conversion

  //       if (product?.returnableUOMId === unitId) {
  //         const invoiceUOM = tempData[matchProduct].productUOM.find(
  //           (item: ProductUOM) => item.productUOMId === product?.returnableUOMId
  //         );

  //         eachInvoiceData.price = product?.price;
  //         eachInvoiceData.discount =
  //           (product?.discount ?? 0) / (tempData[matchProduct].quantity ?? 0);
  //         conversionNumber =
  //           tempData[matchProduct].productUOM.find(
  //             (item: ProductUOM) => item.productUOMId === invoiceProductUOMId
  //           )?.conversionToSmallestUOM || 1;
  //       } else if (thisProductSmallestUOM === product?.returnableUOMId) {
  //         eachInvoiceData.price = product?.price;
  //         eachInvoiceData.discount =
  //           (product?.discount ?? 0) / (tempData[matchProduct].quantity ?? 0);
  //       } else {
  //         const returnUOM = tempData[matchProduct].productUOM.find(
  //           (item: ProductUOM) => item.productUOMId === unitId
  //         );

  //         if (returnUOM.isSmallest === "TRUE") {
  //           //return UOM is smaller than invoice
  //           const invoiceUOM = tempData[matchProduct].productUOM.find(
  //             (item: ProductUOM) =>
  //               item.productUOMId === product?.returnableUOMId
  //           );
  //           const invoicePrice = product?.price ?? 0;
  //           const conversion = invoiceUOM.conversionToSmallestUOM;
  //           const price = invoicePrice / conversion;
  //           eachInvoiceData.price = price;

  //           const invoiceDiscount = product?.discount ?? 0;
  //           const invoiceDiscountPerQuantity =
  //             invoiceDiscount / (product?.quantity ?? 0);
  //           const afterConversion = invoiceDiscountPerQuantity / conversion;
  //           eachInvoiceData.discount = afterConversion;
  //           conversionNumber = conversion;
  //         } else {
  //           const unitPrice = product?.price ?? 0;
  //           const conversion = returnUOM.conversionToSmallestUOM;
  //           const price = unitPrice * conversion;
  //           eachInvoiceData.price = price;

  //           const invoiceDiscount = product?.discount ?? 0;
  //           const invoiceDiscountPerQuantity =
  //             invoiceDiscount / (product?.quantity ?? 0);
  //           const afterConversion = invoiceDiscountPerQuantity * conversion;
  //           eachInvoiceData.discount = afterConversion;
  //           conversionNumber = conversion;
  //         }
  //       }

  //       if (invoice.goodReturnQuantity) {
  //         const good = {
  //           ...eachInvoiceData,
  //           reasonId: invoice.goodReturnReasonId,
  //           reasonName: invoice.goodReturnReasonName,
  //           productCondition: "GOOD",
  //           returnQuantity: invoice.goodReturnQuantity
  //             ? invoice.goodReturnQuantity
  //             : 1,
  //         };
  //         const actualBatchDetail = calculateBatchDetatils(
  //           good.returnQuantity,
  //           product?.batchDetails,
  //           conversionNumber
  //         );
  //         good.batchDetails = actualBatchDetail;
  //         array.push(good);
  //       }

  //       if (invoice.badReturnQuantity) {
  //         const bad = {
  //           ...eachInvoiceData,
  //           reasonId: invoice.badReturnReasonId,
  //           reasonName: invoice.badReturnReasonName,
  //           productCondition: "BAD",
  //           returnQuantity: invoice.badReturnQuantity
  //             ? invoice.badReturnQuantity
  //             : 1,
  //         };
  //         const actualBatchDetail = calculateBatchDetatils(
  //           bad.returnQuantity,
  //           product?.batchDetails,
  //           conversionNumber
  //         );
  //         bad.batchDetails = actualBatchDetail;
  //         array.push(bad);

  //         // eachInvoiceData.reasonId = invoice.badReturnReasonId;
  //         // eachInvoiceData.reasonName = invoice.badReturnReasonName;
  //         // eachInvoiceData.productCondition = "BAD";
  //         // eachInvoiceData.returnQuantity = invoice.badReturnQuantity
  //         //   ? invoice[0].badReturnQuantity
  //         //   : 1;
  //       }
  //     });

  //     //save data
  //     tempData[matchProduct].invoiceDetail = array;
  //     setData(tempData);
  //     const found = editedData.findIndex(
  //       (item) => item.id === tempData[matchProduct].id
  //     );

  //     if (found !== -1) {
  //       editedData[found] = tempData[matchProduct];
  //       setEditedData(editedData);
  //     }
  //   }
  //   // } else {
  //   //   MessageErrorUI(t("Please select product to return"));
  //   //   return false;
  //   // }

  //   setIsModalOpen(false);
  //   return pass;
  // };

  // Adjust invoice return quantity
  const adjustInvoiceReturnQuantity = (
    invoiceNo: string,
    companyId: string,
    returnQuantity: number,
    productId: string,
    invoiceId: string,
    type: ReturnConditionType, // 'good' or 'bad'
    productLineDetails: ProductOrdered[],
    productDetails: Product
  ) => {
    // Retrieve the invoice details for the given record ID

    let updatedSelectedInvoiceData = new Map(_.cloneDeep(selectedInvoiceData));
    let invoiceDetails: SelectedInvoiceProps[] | undefined =
      updatedSelectedInvoiceData.get(invoiceId);

    // Initialize the array if no invoice details exist for the record
    if (!invoiceDetails) {
      invoiceDetails = [];

      const newDetails: SelectedInvoiceProps = {
        invoiceNo: invoiceNo,
        companyId: companyId,
        productId: productId,
        goodReturnDetails: {
          reasonId: "",
          returnQuantity: type === "GOOD" ? returnQuantity : 0,
        },
        badReturnDetails: {
          reasonId: "",
          returnQuantity: type === "BAD" ? returnQuantity : 0,
        },
        productLineDetails: productLineDetails,
        productDetails: productDetails,
      };

      invoiceDetails.push(newDetails);
    } else {
      // Find the index of the product in the invoice details
      const productIndex = invoiceDetails.findIndex(
        (item: SelectedInvoiceProps) => item.productId === productId
      );

      if (productIndex >= 0) {
        // If the product already exists, update its return details based on type
        const existingDetails = invoiceDetails[productIndex];
        if (type === "GOOD") {
          invoiceDetails[productIndex] = {
            ...existingDetails,
            goodReturnDetails: {
              ...existingDetails.goodReturnDetails,
              returnQuantity: returnQuantity,
            },
          };
        } else if (type === "BAD") {
          invoiceDetails[productIndex] = {
            ...existingDetails,
            badReturnDetails: {
              ...existingDetails.badReturnDetails,
              returnQuantity: returnQuantity,
            },
          };
        }
      } else {
        // If the product does not exist, add a new entry
        const newDetails: SelectedInvoiceProps = {
          invoiceNo: invoiceNo,
          companyId: companyId,
          productId: productId,
          goodReturnDetails: {
            reasonId: "",
            returnQuantity: type === "GOOD" ? returnQuantity : 0,
          },
          badReturnDetails: {
            reasonId: "",
            returnQuantity: type === "BAD" ? returnQuantity : 0,
          },
          productLineDetails: productLineDetails,
          productDetails: productDetails,
        };

        invoiceDetails.push(newDetails);
      }
    }

    // Update the map and the state with the modified invoice details
    updatedSelectedInvoiceData.set(invoiceId, invoiceDetails);

    setSelectedInvoiceData(updatedSelectedInvoiceData);
  };

  // Update return reason for good / bad
  const updateReason = (
    invoiceNo: string,
    companyId: string,
    invoiceId: string,
    productId: string,
    type: ReturnConditionType,
    reasonId: string,
    productDetails: Product
  ) => {
    // Retrieve the invoice details for the given record ID

    let updatedSelectedInvoiceData = new Map(_.cloneDeep(selectedInvoiceData));
    let invoiceDetails: SelectedInvoiceProps[] | undefined =
      updatedSelectedInvoiceData.get(invoiceId);

    // Initialize the array if no invoice details exist for the record
    if (!invoiceDetails) {
      invoiceDetails = [];

      const newDetails: SelectedInvoiceProps = {
        invoiceNo: invoiceNo,
        companyId: companyId,
        productId: productId,
        goodReturnDetails: {
          reasonId: type === "GOOD" ? reasonId : "",
          returnQuantity: 0,
        },
        badReturnDetails: {
          reasonId: type === "BAD" ? reasonId : "",
          returnQuantity: 0,
        },
        productLineDetails: [],
        productDetails: productDetails,
      };

      invoiceDetails.push(newDetails);
    } else {
      // Find the index of the product in the invoice details
      const productIndex = invoiceDetails.findIndex(
        (item: SelectedInvoiceProps) => item.productId === productId
      );

      if (productIndex >= 0) {
        // If the product already exists, update its return details based on type
        const existingDetails = invoiceDetails[productIndex];
        if (type === "GOOD") {
          invoiceDetails[productIndex] = {
            ...existingDetails,
            goodReturnDetails: {
              ...existingDetails.goodReturnDetails,
              reasonId: reasonId,
            },
          };
        } else if (type === "BAD") {
          invoiceDetails[productIndex] = {
            ...existingDetails,
            badReturnDetails: {
              ...existingDetails.badReturnDetails,
              reasonId: reasonId,
            },
          };
        }
      } else {
        // If the product does not exist, add a new entry
        const newDetails: SelectedInvoiceProps = {
          invoiceNo: invoiceNo,
          companyId: companyId,
          productId: productId,
          goodReturnDetails: {
            reasonId: type === "GOOD" ? reasonId : "",
            returnQuantity: 0,
          },
          badReturnDetails: {
            reasonId: type === "BAD" ? reasonId : "",
            returnQuantity: 0,
          },
          productLineDetails: [],
          productDetails: productDetails,
        };

        invoiceDetails.push(newDetails);
      }
    }

    // Update the map and the state with the modified invoice details
    updatedSelectedInvoiceData.set(invoiceId, invoiceDetails);

    setSelectedInvoiceData(updatedSelectedInvoiceData);
  };

  // Get default reason if selected
  const getDefaultReasonValue = (
    invoiceId: string,
    productId: string,
    type: ReturnConditionType
  ) => {
    let defaultValue = "";
    let updatedData = new Map(_.cloneDeep(selectedInvoiceData)).get(invoiceId);
    if (!updatedData) {
      return defaultValue;
    }

    if (type === "GOOD") {
      defaultValue =
        updatedData.find((item) => item.productId === productId)
          ?.goodReturnDetails?.reasonId || "";
    } else {
      defaultValue =
        updatedData.find((item) => item.productId === productId)
          ?.badReturnDetails?.reasonId || "";
    }

    return defaultValue;
  };

  // Get default return value if selected.
  const getDefaultReturnQuantity = (
    invoiceId: string,
    productId: string,
    type: ReturnConditionType
  ) => {
    let defaultValue = 0;
    let updatedData = new Map(_.cloneDeep(selectedInvoiceData))?.get(invoiceId);

    if (!updatedData) {
      return defaultValue;
    }

    if (type === "GOOD") {
      defaultValue =
        updatedData.find((item) => item.productId === productId)
          ?.goodReturnDetails?.returnQuantity || 0;
    } else {
      defaultValue =
        updatedData.find((item) => item.productId === productId)
          ?.badReturnDetails?.returnQuantity || 0;
    }

    return defaultValue;
  };

  // Calculate balnce returnable quantity prevent adjust quantity more than returnable quantity.
  const currentTotalReturnableQuantity = (
    invoiceId: string,
    totalReturnableQuantity: number = 0,
    totalProcessingQuantity: number = 0,
    type: ReturnConditionType
  ) => {
    let goodQuantity = 0;
    let badQuantity = 0;

    let selectedInvoice = selectedInvoiceData.get(invoiceId);

    if (!selectedInvoice) {
      selectedInvoice = [];
    }

    let index = selectedInvoice.findIndex(
      (item: SelectedInvoiceProps) => selectedProduct === item.productId
    );

    if (index >= 0) {
      goodQuantity =
        selectedInvoice[index]?.goodReturnDetails?.returnQuantity || 0;
      badQuantity =
        selectedInvoice[index]?.badReturnDetails?.returnQuantity || 0;
    }

    let selectedAmount = type === "BAD" ? goodQuantity : badQuantity;

    let maxQuantity =
      totalReturnableQuantity - totalProcessingQuantity - selectedAmount;

    return maxQuantity;
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const invoiceColumn = [
    {
      title: t("GoodsReturn.invoice") + " " + t("Common.no"),
      dataIndex: "invoiceNo",
      key: "invoiceNo",
      width: 80,
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.invoiceNo}</p>;
      },
    },
    {
      title: t("GoodsReturn.invoiceDate"),
      dataIndex: "invoiceDate",
      key: "invoiceDate",
      render: (_: any, record: any) => {
        return (
          <p className="tableRowNameDesign">
            {formateDate(record.invoiceDate)}
          </p>
        );
      },
    },
    {
      title: t("GoodsReturn.companyName"),
      dataIndex: "companyId",
      key: "companyId",
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.companyName}</p>;
      },
    },
    {
      title: t("GoodsReturn.returnableQuantity"),
      dataIndex: "totalReturnableQuantity",
      key: "totalReturnableQuantity",
      render: (_: any, record: any) => {
        return (
          <p className="tableRowNameDesign">{record.totalReturnableQuantity}</p>
        );
      },
    },
    {
      title: t("GoodsReturn.processingQuantity"),
      dataIndex: "processingQuantity",
      key: "processingQuantity",
      render: (_: any, record: any) => {
        return (
          <p className="tableRowNameDesign">{record.totalProcessingQuantity}</p>
        );
      },
    },
    {
      title: t("GoodsReturn.uom"),
      dataIndex: "uom",
      key: "uom",
      render: (_: any, record: any) => {
        const product = record.invoiceProducts?.find(
          (item: ProductOrdered) => item.productId === selectedProduct
        );
        return (
          <p className="tableRowNameDesign">
            {uomMap.get(product?.returnableUOMId)?.name}
          </p>
        );
      },
    },

    {
      title: t("GoodsReturn.good") + " " + t("GoodsReturn.returnQuantity"),
      dataIndex: "goodQuantity",
      key: "goodQuantity",
      width: 180,
      render: (_: any, record: any, index: number) => {
        const key = `${record.id}/GOOD`;

        const balanceReturnableQuantity: number =
          currentTotalReturnableQuantity(
            record.id,
            record.totalReturnableQuantity,
            record.totalProcessingQuantity,
            "GOOD"
          );

        const defaultValue = getDefaultReturnQuantity(
          record.id,
          selectedProduct,
          "GOOD"
        );

        const productDetails = data?.find(
          (item: any) => item.id === selectedProduct
        );

        return (
          <Form.Item name={key} className="flex-1 my-4">
            <CounterComponent
              defaultValue={defaultValue}
              className="bg-buttonOrange text-white font-bold "
              max={balanceReturnableQuantity}
              onCountChange={(value) => {
                adjustInvoiceReturnQuantity(
                  record.invoiceNo,
                  record.companyId,
                  value,
                  selectedProduct,
                  record.id,
                  "GOOD",
                  record.productLineDetails,
                  productDetails
                );
              }}
            />
          </Form.Item>
        );
      },
    },
    {
      title: t("GoodsReturn.good") + " " + t("GoodsReturn.reason"),
      dataIndex: "goodReason",
      key: "goodReason",
      width: 200,
      render: (_: any, record: any, index: number) => {
        const productDetails = data?.find(
          (item: any) => item.id === selectedProduct
        );

        return (
          <Form.Item
            name={`goodReason${record.id + "-" + selectedProduct}`}
            className="flex-1 my-4"
            rules={[
              {
                required: validationQtyForm.getFieldValue(
                  `goodQuantity${record.id + "-" + selectedProduct}`
                ),
                message:
                  t("GoodsReturn.goodsReturn") +
                  " " +
                  t("GoodsReturn.reason") +
                  " " +
                  t("Validation.requiredField"),
              },
            ]}
          >
            <div className="flex flex-1">
              <SelectInput
                defaultValue={getDefaultReasonValue(
                  record.id,
                  selectedProduct,
                  "GOOD"
                )}
                placeholder={t("GoodsReturn.selectReason")}
                showArrow={true}
                options={reasonOption}
                onChange={(value: any) => {
                  updateReason(
                    record.invoiceNo,
                    record.companyId,
                    record.id,
                    selectedProduct,
                    "GOOD",
                    value,
                    productDetails
                  );
                }}
              />
            </div>
          </Form.Item>
        );
      },
    },
    {
      title: t("GoodsReturn.bad") + " " + t("GoodsReturn.returnQuantity"),
      dataIndex: "badQuantity",
      key: "badQuantity",
      width: 180,
      render: (_: any, record: any, index: number) => {
        const key = `${record.id}/BAD`;
        const balanceReturnableQuantity: number =
          currentTotalReturnableQuantity(
            record.id,
            record.totalReturnableQuantity,
            record.totalProcessingQuantity,
            "BAD"
          );

        const productDetails = data?.find(
          (item: any) => item.id === selectedProduct
        );

        return (
          <Form.Item name={key} className="flex-1 my-4">
            <CounterComponent
              defaultValue={getDefaultReturnQuantity(
                record.id,
                selectedProduct,
                "BAD"
              )}
              className="bg-buttonOrange text-white font-bold"
              max={balanceReturnableQuantity}
              disabled={record.invoiceProducts?.some(
                (product: any) =>
                  product.returnableQuantity - product.processingQuantity === 0
              )}
              onCountChange={(value) => {
                adjustInvoiceReturnQuantity(
                  record.invoiceNo,
                  record.companyId,
                  value,
                  selectedProduct,
                  record.id,
                  "BAD",
                  record.productLineDetails,
                  productDetails
                );
              }}
            />
          </Form.Item>
        );
      },
    },
    {
      title: t("GoodsReturn.bad") + " " + t("GoodsReturn.reason"),
      dataIndex: "badReason",
      key: "badReason",
      width: 200,
      render: (_: any, record: any, index: number) => {
        const productDetails = data?.find(
          (item: any) => item.id === selectedProduct
        );

        return (
          <Form.Item
            name={`badReason${record.id + "-" + selectedProduct}`}
            className="flex-1 my-4"
            rules={[
              {
                required: validationQtyForm.getFieldValue(
                  `badQuantity${record.id + "-" + selectedProduct}`
                ),
                message:
                  t("GoodsReturn.goodsReturn") +
                  " " +
                  t("GoodsReturn.reason") +
                  " " +
                  t("Validation.requiredField"),
              },
            ]}
          >
            <div className="flex flex-1">
              <SelectInput
                defaultValue={getDefaultReasonValue(
                  record.id,
                  selectedProduct,
                  "BAD"
                )}
                placeholder={t("GoodsReturn.selectReason")}
                showArrow={true}
                options={reasonOption}
                onChange={(value: any) => {
                  updateReason(
                    record.invoiceNo,
                    record.companyId,
                    record.id,
                    selectedProduct,
                    "BAD",
                    value,
                    productDetails
                  );
                }}
              />
            </div>
          </Form.Item>
        );
      },
    },
  ];

  const showModal = () => {
    return (
      <div className="w-full">
        <Form form={validationQtyForm} scrollToFirstError>
          <Col>
            {!fetchingInvoice ? (
              <ListingTableUI
                // EditableCell={EditableCell}
                bordered
                dataSource={invoiceData}
                columns={invoiceColumn}
                // rowClassName="editable-row"
                rowKey={(record: any) => `${record.id}/${selectedProduct}`}
                cursor={false}
                // loader={showButtonLoader}
                loading={fetchingInvoice}
                pagination={false}
                endMessage={""}
                rowSelection={rowSelection}
              />
            ) : (
              <div>
                <Spin />
                <p>fetching Data...</p>
              </div>
            )}
          </Col>
        </Form>
        <Row className="justify-end pr-2 pt-4">
          <PrimaryButtonUI
            disabled={fetchingInvoice}
            // disabled={invoiceData.some((item) => (item.badReturnQuantity && !item?.badReturnReasonId) || (item.goodReturnQuantity && !item?.goodReturnReasonId))}
            label={t("OK")}
            onClick={async () => {
              // if (selectedRowKeys) {
              //   const validationPromises: any[] = [];
              //   let errorOccurred = false;

              //   selectedRowKeys.forEach((item) => {
              //     const invoice = invoiceData.find((val) => val.id === item);

              //     if (invoice) {
              //       if (
              //         !invoice.goodReturnQuantity &&
              //         !invoice.badReturnQuantity
              //       ) {
              //         errorOccurred = true;
              //         MessageErrorUI(t("GoodsReturn.enterQuantity"));
              //         return;
              //       }
              //       if (invoice.goodReturnQuantity) {
              //         const promise = validationQtyForm.validateFields([
              //           `goodReason${item + "-" + selectedProduct}`,
              //         ]);
              //         validationPromises.push(promise);
              //       }

              //       if (invoice.badReturnQuantity) {
              //         const promise = validationQtyForm.validateFields([
              //           `badReason${item + "-" + selectedProduct}`,
              //         ]);
              //         validationPromises.push(promise);
              //       }
              //     }
              //   });

              //   if (errorOccurred === false) {
              //     if (validationPromises.length > 0) {
              //       handleOk([selectedProduct]);
              //     }
              //   }
              // }
              handleOk();
            }}
          />
          {/* <SecondaryButtonUI
            label={t("Common.cancel")}
            onClick={() => {
              handleCancel();
            }}
          /> */}
        </Row>
      </div>
    );
  };

  // *************************************************************************************
  // *** Product Listing ***
  // *************************************************************************************

  const productRowSelection = {
    preserveSelectedRowKeys: true,
    onChange: (selectedKeys: string[], selectedRows: any[]) => {
      // Update selected row keys
      setSelectedProductRowKeys(() => {
        // Just use the new selectedKeys array since it already reflects the selection state
        return selectedKeys;
      });

      // // Update selected row data
      // setSelectedProductRowData(() => {
      //   // Use selectedRows since it reflects the current selected data
      //   return selectedRows;
      // });
    },
    selectedRowKeys: selectedProductRowKeys,
  };

  // const handleCheck = (id: string, check: boolean) => {
  //   form
  //     .validateFields([
  //       `expiryDate${id}`,
  //       `goodsCondition${id}`,
  //       `reason${id}`,
  //       // `uom${id}`,
  //     ])
  //     .then(() => {
  //       const tempData = [...data];
  //       const productIndex = tempData.findIndex((item) => item.id === id);

  //       if (productIndex !== -1) {
  //         tempData[productIndex].checked = check;

  //         if (tempData[productIndex].invoiceDetail !== undefined) {
  //           const someproductChecked = tempData.some(
  //             (product: any) => product.checked
  //           );

  //           if (someproductChecked) {
  //             tempData[productIndex].indeterminate = true;
  //           } else {
  //             tempData[productIndex].indeterminate = false;
  //           }

  //           const everyProductChecked = tempData.every(
  //             (product: any) => product.checked
  //           );

  //           if (everyProductChecked) {
  //             tempData[productIndex].checkAll = true;
  //             tempData[productIndex].indeterminate = false;
  //             // setIndeterminate(true);
  //           } else {
  //             tempData[productIndex].checkAll = false;
  //             // setIndeterminate(false);
  //           }
  //           setData([...tempData]);
  //         } else {
  //           MessageErrorUI(t("chooseInvoiceError"));
  //         }
  //       }
  //       // Your code here
  //     })
  //     .catch(() => {});
  // };

  const column = [
    {
      title: t("GoodsReturn.product"),
      key: "mobile",
      responsive: ["xs"], // only on mobile
      render: (_: any, record: any, index: number) => {
        const smallest = record?.productUOM.find(
          (item: ProductUOM) => item.isSmallest === "TRUE"
        )?.productUOMId;
        return (
          <div className="flex flex-row gap-x-2">
            <div className="flex flex-col">
              {/* image  */}
              <div className="flex flex-row">
                {" "}
                <img
                  className="object-contain h-[80px] w-[80px] p-2 flex"
                  src={(() => {
                    const pictures =
                      record.productUOM && record.productUOM.length > 0
                        ? record.productUOM[0].pictures
                        : null;

                    // let currentData = selectedProductRowData.find((item)=> item.id === record.productId)
                    // const pictures = currentData?.productUOM?.find((item)=> item.productUOMId === record.productUOMId)?.pictures
                    return pictures && pictures.length > 0
                      ? PUBLIC_BUCKET_URL + pictures[0]
                      : defaultImage.src;
                  })()}
                  loading="lazy"
                ></img>
                <div className="flex flex-col w-full font-bold">
                  <p className="font-bold text-[14px]">{record.name}&nbsp;</p>
                  <p className="text-gray-400 text-[10px] w-full flex ">
                    <span>
                      {t("GoodsReturn.productCode")}: {record.sku}
                    </span>
                  </p>
                  <p className="text-gray-400 text-[10px] w-full flex ">
                    <span>
                      {record?.invoiceNo
                        ? t("GoodsReturn.invoice") +
                          ": " +
                          record.invoiceNo?.join(", ")
                        : ""}
                    </span>
                  </p>
                  <p className="text-gray-400 text-[10px] w-full flex ">
                    <span>
                      {record.company
                        ? t("GoodsReturn.company") +
                          ": " +
                          companyMap.get(record.company)?.name
                        : null}
                    </span>
                  </p>
                </div>
              </div>
              <div className="flex flex-row gap-x-2 items-center">
                <SingleDateInput
                  placeholder={t("Select") + " " + t("GoodsReturn.expiryDate")}
                  defaultValue={
                    productExpiryDate.get(record.id)
                      ? dayjs(record?.expiryDate)
                      : ""
                  }
                  onChange={(value: any) => {
                    const formatDate =
                      moment(new Date(value)).format("YYYY-MM-DDT00:00:00") +
                      "Z";

                    setProductExpiryDate((prev: any) => {
                      let tempMap = prev;
                      tempMap.set(record.id, formatDate);
                      return tempMap;
                    });
                  }}
                />

                <div className="w-[180px]">
                  <CounterComponent
                    uom={uomMap.get(smallest)?.name}
                    defaultValue={record?.quantity ? record.quantity : 1}
                    className="bg-buttonOrange text-white font-bold"
                    onCountChange={(value) => {
                      const found = editedData?.findIndex(
                        (item) => item.id === record.id
                      );
                      if (value) {
                        data[index].quantity = value;
                        setData(data);
                        record.quantity = value;

                        if (found === -1) {
                          setEditedData((prev) => [...prev, record]);
                        } else {
                          let updatedData = [...editedData];
                          updatedData[found] = record;
                          setEditedData(updatedData);
                        }
                      } else {
                        delete data[index].quantity;

                        if (found === -1) {
                          setEditedData((prev) => [...prev, record]);
                        } else {
                          editedData[found] = record;
                          setEditedData(editedData);
                        }
                      }
                    }}
                  />
                </div>
                {/* <SelectInput
                disabled
                value={smallest}
                placeholder={t("GoodsReturn.selectUOM")}
                showArrow={true}
                options={uomOptionMap.get(record.id)}
                // onChange={(value: any) => {
                //   if (value) {
                //     data[index].uomId = unitId;
                //     setData(data);
                //     record.uomId = unitId;
                //     const found = editedData?.findIndex(
                //       (item) => item.id === record.id
                //     );
                //     if (found === -1) {
                //       setEditedData((prev) => [...prev, record]);
                //     } else {
                //       editedData[found] = record;
                //       setEditedData(editedData);
                //     }
                //   }
                // }}
              /> */}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: t("GoodsReturn.product"),
      dataIndex: "product",
      responsive: ["md"],
      key: "product",
      // width: 300,
      render: (_: any, record: any) => {
        return (
          <div className="flex flex-row">
            {" "}
            <img
              className="object-contain h-[80px] w-[80px] p-2 flex"
              src={(() => {
                const pictures =
                  record.productUOM && record.productUOM.length > 0
                    ? record.productUOM[0].pictures
                    : null;

                // let currentData = selectedProductRowData.find((item)=> item.id === record.productId)
                // const pictures = currentData?.productUOM?.find((item)=> item.productUOMId === record.productUOMId)?.pictures
                return pictures && pictures.length > 0
                  ? PUBLIC_BUCKET_URL + pictures[0]
                  : defaultImage.src;
              })()}
              loading="lazy"
            ></img>
            <div className="flex flex-col w-full font-bold">
              <p className="font-bold text-[14px]">{record.name}&nbsp;</p>
              <p className="text-gray-400 text-[10px] w-full flex ">
                <span>
                  {t("GoodsReturn.productCode")}: {record.sku}
                </span>
              </p>
              <p className="text-gray-400 text-[10px] w-full flex ">
                <span>
                  {record?.invoiceNo
                    ? t("GoodsReturn.invoice") +
                      ": " +
                      record.invoiceNo?.join(", ")
                    : ""}
                </span>
              </p>
              <p className="text-gray-400 text-[10px] w-full flex ">
                <span>
                  {record.company
                    ? t("GoodsReturn.company") +
                      ": " +
                      companyMap.get(record.company)?.name
                    : null}
                </span>
              </p>
            </div>
          </div>
        );
      },
    },
    {
      title: t("GoodsReturn.expiryDate"),
      dataIndex: "expiryDate",
      key: "expiryDate",
      responsive: ["md"],
      width: 180,
      render: (_: any, record: any, index: number) => {
        return (
          <SingleDateInput
            defaultValue={
              productExpiryDate.get(record.id) ? dayjs(record?.expiryDate) : ""
            }
            onChange={(value: any) => {
              const formatDate =
                moment(new Date(value)).format("YYYY-MM-DDT00:00:00") + "Z";

              setProductExpiryDate((prev: any) => {
                let tempMap = prev;
                tempMap.set(record.id, formatDate);
                return tempMap;
              });
            }}
          />
        );
      },
    },
    // {
    //   title: t("GoodsReturn.goodsCondition"),
    //   dataIndex: "goodsCondition",
    //   key: "goodsCondition",
    //   width: 120,
    //   render: (_: any, record: any, index: number) => {
    //     return (
    //       <SelectInput
    //         defaultValue={record?.goodsCondition ? record?.goodsCondition : ""}
    //         placeholder={t("")}
    //         showArrow={true}
    //         options={goodsConditionOption}
    //         onChange={(value: any) => {
    //           const found = editedData?.findIndex(
    //             (item) => item.id === record.id
    //           );
    //           if (value) {
    //             data[index].goodsCondition = value;
    //             setData(data);
    //             record.goodsCondition = value;
    //             if (found === -1) {
    //               setEditedData((prev) => [...prev, record]);
    //             } else {
    //               editedData[found] = record;
    //               setEditedData(editedData);
    //             }
    //           } else {
    //             delete data[index].goodsCondition;

    //             if (found === -1) {
    //               setEditedData((prev) => [...prev, record]);
    //             } else {
    //               editedData[found] = record;
    //               setEditedData(editedData);
    //             }
    //           }
    //         }}
    //       />
    //     );
    //   },
    // },
    // {
    //   title: t("GoodsReturn.reason"),
    //   dataIndex: "reason",
    //   key: "reason",
    //   width: 250,
    //   render: (_: any, record: any, index: number) => {
    //     return (
    //       <div className="flex flex-1">
    //         <SelectInput
    //           defaultValue={record?.reasonId ? record?.reasonId : ""}
    //           placeholder={t("GoodsReturn.selectReason")}
    //           showArrow={true}
    //           options={reasonOption}
    //           onChange={(value: any) => {
    //             const found = editedData?.findIndex(
    //               (item) => item.id === record.id
    //             );
    //             if (value) {
    //               const reasonId = value;
    //               const reason = reasonOption.find(
    //                 (val) => val.value === reasonId
    //               )?.label;
    //               data[index].reasonName = reason;
    //               data[index].reasonId = reasonId;
    //               setData(data);
    //               record.reasonId = reasonId;
    //               record.reasonName = reason;

    //               if (found === -1) {
    //                 setEditedData((prev) => [...prev, record]);
    //               } else {
    //                 editedData[found] = record;
    //                 setEditedData(editedData);
    //               }
    //             } else {
    //               delete data[index].reasonName;
    //               delete data[index].reasonId;
    //               if (found === -1) {
    //                 setEditedData((prev) => [...prev, record]);
    //               } else {
    //                 editedData[found] = record;
    //                 setEditedData(editedData);
    //               }
    //             }
    //           }}
    //         />
    //       </div>
    //     );
    //   },
    // },
    {
      title: t("GoodsReturn.quantity"),
      dataIndex: "quantity",
      key: "quantity",
      responsive: ["md"],
      width: 150,
      render: (_: any, record: any, index: number) => {
        return (
          <CounterComponent
            defaultValue={record?.quantity ? record.quantity : 1}
            className="bg-buttonOrange text-white font-bold"
            onCountChange={(value) => {
              const found = editedData?.findIndex(
                (item) => item.id === record.id
              );
              if (value) {
                data[index].quantity = value;
                setData(data);
                record.quantity = value;

                if (found === -1) {
                  setEditedData((prev) => [...prev, record]);
                } else {
                  let updatedData = [...editedData];
                  updatedData[found] = record;
                  setEditedData(updatedData);
                }
              } else {
                delete data[index].quantity;

                if (found === -1) {
                  setEditedData((prev) => [...prev, record]);
                } else {
                  editedData[found] = record;
                  setEditedData(editedData);
                }
              }
            }}
          />
        );
      },
    },
    {
      title: t("GoodsReturn.uom"),
      dataIndex: "uom",
      key: "uom",
      responsive: ["md"],
      width: 180,
      render: (_: any, record: any, index: number) => {
        const smallest = record?.productUOM.find(
          (item: ProductUOM) => item.isSmallest === "TRUE"
        )?.productUOMId;
        // const unitKey = Array.from(uomMap.entries()).find(([key, value]) => {
        //   return value.name === "UNT";
        // });
        // const unitId: string = unitKey ? uomMap.get(unitKey[0]).id : "";
        return (
          <SelectInput
            disabled
            value={smallest}
            placeholder={t("GoodsReturn.selectUOM")}
            showArrow={true}
            options={uomOptionMap.get(record.id)}
            // onChange={(value: any) => {
            //   if (value) {
            //     data[index].uomId = unitId;
            //     setData(data);
            //     record.uomId = unitId;
            //     const found = editedData?.findIndex(
            //       (item) => item.id === record.id
            //     );
            //     if (found === -1) {
            //       setEditedData((prev) => [...prev, record]);
            //     } else {
            //       editedData[found] = record;
            //       setEditedData(editedData);
            //     }
            //   }
            // }}
          />
        );
      },
    },
    {
      title: t("GoodsReturn.invoice"),
      dataIndex: "invoice",
      key: "invoice",
      width: isSmallScreen ? 50 : 100,
      render: (_: any, record: any) => {
        return (
          <Button
            type="link"
            className="break-words font-bold xs:px-2"
            // disabled={disable}
            onClick={() => {
              let isExpiryFill = productExpiryDate.get(record.id);

              if (isExpiryFill === undefined) {
                MessageErrorUI(
                  t("GoodsReturn.expiryDate") +
                    " " +
                    t("Validation.requiredField")
                );
                return;
              }

              let temp = data.find((item: Product) => item.id === record?.id);
              searchProductInvoice(temp, record.id);

              // } else if (temp.goodsCondition === undefined) {
              //   MessageErrorUI(
              //     t("GoodsReturn.goodsCondition") +
              //       " " +
              //       t("Validation.requiredField")
              //   );
              //   return;
              // } else if (temp.reasonId === undefined) {
              //   MessageErrorUI(
              //     t("GoodsReturn.reason") + " " + t("Validation.requiredField")
              //   );
              //   return;
              // }
              // setIsModalOpen(true);
            }}
          >
            <Tooltip title={t("GoodsReturn.chooseInvoice")}>
              <EditFilled style={{ color: "blue" }} />
            </Tooltip>
          </Button>
        );
      },
    },
  ];

  const props: UploadProps = {
    name: "file",
    multiple: true,
    maxCount: 5,
    showUploadList: {
      showPreviewIcon: false,
    },
    beforeUpload: (file) => {
      if (
        file.type !== "image/png" &&
        file.type !== "image/jpg" &&
        file.type !== "image/jpeg" &&
        file.type !== "application/pdf"
      ) {
        MessageErrorUI(
          `${file.name} is an invalid file format. Please change the file extension to either .pdf, .png, .jpg, .jpeg.`
        );
        return Upload.LIST_IGNORE;
      } else if (file.size > 5242880) {
        MessageErrorUI(
          `${file.name} is too large. Please upload another document that is smaller than 5MB.`
        );
        return Upload.LIST_IGNORE;
      } else {
        return false;
      }
    },
  };

  // Search product invoice when open.
  const searchProductInvoice = (temp: any[], invoiceId: string) => {
    setSelectedProduct(invoiceId);
    setInvoiceData([]);
    getInvoice(temp, invoiceId);
  };

  // Get product smallest UOM.
  const getProductSmallestConversionUnit = (
    productId: string,
    currentUOMId: string
  ) => {
    if (!productId || !currentUOMId || !productUOMConversion.size) {
      MessageErrorUI("Invalid product ID OR CURRENT uom id for conversion");
      return 1;
    }

    let updatedDateUOMConversion: ProductUOM[] = new Map(
      productUOMConversion
    ).get(productId);

    let result = updatedDateUOMConversion.find(
      (item) => item.productUOMId === currentUOMId
    )?.conversionToSmallestUOM;

    return result || 1;
  };

  // Loop through all the invoice's product batch detail and allocate the quantity.
  // const arrangeProductBatchSource = (
  //   invoiceNo:string,
  //   invoiceId: string,
  //   data: ProductOrdered[],
  //   goodReturnDetails: { reasonId: string; returnQuantity: number },
  //   badReturnDetails: { reasonId: string; returnQuantity: number },
  //   productDetail: Product
  // ) => {
  //   // data conatin the invoice and how many want to return .
  //   let updatedData = data.map((item: ProductOrdered) => {
  //     let updateBatchDetailsInSmallest = item?.batchDetails?.map((product) => {
  //       let latestBatchDetail: any = product;
  //       if (!item.productUOMId || !item.productId || !product.batchQuantity) {
  //         return product;
  //       }

  //       latestBatchDetail.invoiceQuantityInUnit =
  //         product?.batchQuantity *
  //           getProductSmallestConversionUnit(
  //             item?.productId,
  //             item?.productUOMId
  //           ) || 1;
  //       latestBatchDetail.discountInUnit = !item.discount
  //         ? 0
  //         : item?.discount / latestBatchDetail.invoiceQuantityInUnit;

  //       return latestBatchDetail;
  //     });

  //     return { ...item, batchDetails: updateBatchDetailsInSmallest };
  //   });

  //   const newBatchDetails: any = [];
  //   updatedData.forEach((product) => {

  //     if (product.batchDetails && Array.isArray(product.batchDetails)) {

  //       let thisBatchDetailsCanUse = product.returnableQuantity || 0
  //       if(typeof(product.returnableQuantity) === "number" && typeof(product.processingQuantity) ==="number" ){
  //         thisBatchDetailsCanUse = product.returnableQuantity - product.processingQuantity;
  //       }

  //       // Arrays to store aggregated batch details for good and bad conditions
  //       let aggregatedGoodBatchDetails: any[] = [];
  //       let aggregatedBadBatchDetails: any[] = [];

  //       // Variables to aggregate total quantities and discounts
  //       let totalGoodReturnQuantity = 0;
  //       let totalBadReturnQuantity = 0;
  //       let totalGoodDiscount = 0;
  //       let totalBadDiscount = 0;

  //       const defaultValue = {
  //         invoiceNo: invoiceNo,
  //         productId: product.productId,
  //         productUOMId: product.productUOMId,
  //         returnUOMId: product.returnableUOMId,
  //         invoiceId: invoiceId,
  //         price: product.price,
  //         quantity: product.quantity,
  //         type: product.type,
  //         tax: product.taxRate,
  //         // returnType: product.returnType,
  //         taxId: product.taxId,
  //         sku: productDetail?.sku,
  //         name: productDetail.name,
  //       };

  //       let totalAllocated = 0; // Track total allocated quantity

  //       product.batchDetails.forEach((batch) => {
  //         const invoiceQuantity = batch.invoiceQuantityInUnit;

  //         let goodBatchDetails = [];
  //         let badBatchDetails = [];

  //         // Allocate good quantities
  //         let allocateGood = 0;
  //         if (goodReturnDetails?.returnQuantity > 0 && totalAllocated < thisBatchDetailsCanUse) {
  //           allocateGood = Math.min(
  //             goodReturnDetails.returnQuantity,
  //             invoiceQuantity,
  //             thisBatchDetailsCanUse - totalAllocated // Limit to max allowed
  //           );

  //           goodBatchDetails.push({
  //             inventoryId: batch.inventoryId,
  //             batchQuantity: allocateGood,
  //           });

  //           totalGoodReturnQuantity += allocateGood;
  //           totalGoodDiscount += batch.discountInUnit
  //             ? batch.discountInUnit * allocateGood
  //             : 0;

  //           aggregatedGoodBatchDetails =
  //             aggregatedGoodBatchDetails.concat(goodBatchDetails);

  //           // Reduce the remaining good return quantity
  //           goodReturnDetails.returnQuantity -= allocateGood;
  //         }

  //         // Allocate bad quantities
  //         let allocateBad = 0;
  //         if (badReturnDetails?.returnQuantity > 0 && totalAllocated < thisBatchDetailsCanUse) {
  //           allocateBad = Math.min(
  //             badReturnDetails.returnQuantity,
  //             invoiceQuantity - allocateGood,
  //             thisBatchDetailsCanUse - totalAllocated // Limit to max allowed

  //           );

  //           if (allocateBad > 0) {
  //             badBatchDetails.push({
  //               inventoryId: batch.inventoryId,
  //               batchQuantity: allocateBad,
  //             });

  //             totalBadReturnQuantity += allocateBad;
  //             totalBadDiscount += batch.discountInUnit
  //               ? batch.discountInUnit * allocateBad
  //               : 0;

  //             aggregatedBadBatchDetails =
  //               aggregatedBadBatchDetails.concat(badBatchDetails);

  //             // Reduce the remaining bad return quantity
  //             badReturnDetails.returnQuantity -= allocateBad;
  //           }
  //         }
  //       });

  //       // Push aggregated good details
  //       if (totalGoodReturnQuantity > 0) {
  //         newBatchDetails.push({
  //           ...defaultValue,
  //           batchDetails: aggregatedGoodBatchDetails,
  //           discount: totalGoodDiscount,
  //           returnQuantity: totalGoodReturnQuantity,
  //           reasonId: goodReturnDetails?.reasonId,
  //           returnType: "PARTIAL",
  //           productCondition: "GOOD",
  //           returnUOMId: product.returnableUOMId || "",
  //           sku: productDetail?.sku,
  //           name: productDetail.name,
  //           pictures:
  //             productDetail.productUOM?.find(
  //               (item) => item.productUOMId === product.returnableUOMId
  //             )?.pictures || [],
  //         });
  //       }

  //       // Push aggregated bad details
  //       if (totalBadReturnQuantity > 0) {
  //         newBatchDetails.push({
  //           ...defaultValue,
  //           batchDetails: aggregatedBadBatchDetails,
  //           discount: totalBadDiscount,
  //           returnQuantity: totalBadReturnQuantity,
  //           reasonId: badReturnDetails?.reasonId,
  //           returnType: "PARTIAL",
  //           productCondition: "BAD",
  //           returnUOMId: product.returnableUOMId || "",
  //           sku: productDetail.sku,
  //           name: productDetail.name,
  //           pictures:
  //             productDetail.productUOM?.find(
  //               (item) => item.productUOMId === product.returnableUOMId
  //             )?.pictures || [],
  //         });
  //       }
  //     }
  //   });

  //   return newBatchDetails;
  // };

  // Chcek returnable quantity of the bsatch .
  const arrangeProductBatchSource = (
    invoiceNo: string,
    invoiceId: string,
    data: ProductOrdered[],
    goodReturnDetails: { reasonId: string; returnQuantity: number },
    badReturnDetails: { reasonId: string; returnQuantity: number },
    productDetail: Product
  ) => {
    let updatedData = data.map((item: ProductOrdered) => {
      let updateBatchDetailsInSmallest = item?.batchDetails?.map((product) => {
        let latestBatchDetail: any = product;
        if (!item.productUOMId || !item.productId || !product.batchQuantity) {
          return product;
        }

        latestBatchDetail.invoiceQuantityInUnit =
          product?.batchQuantity *
            getProductSmallestConversionUnit(
              item?.productId,
              item?.productUOMId
            ) || 1;
        latestBatchDetail.discountInUnit = !item.discount
          ? 0
          : item?.discount / latestBatchDetail.invoiceQuantityInUnit;

        return latestBatchDetail;
      });

      return { ...item, batchDetails: updateBatchDetailsInSmallest };
    });

    const newBatchDetails: any = [];
    let remainingGoodQuantity = goodReturnDetails.returnQuantity;
    let remainingBadQuantity = badReturnDetails.returnQuantity;

    for (let product of updatedData) {
      if (remainingGoodQuantity <= 0 && remainingBadQuantity <= 0) {
        break; // Stop processing if all quantities are fulfilled
      }

      if (product.batchDetails && Array.isArray(product.batchDetails)) {
        let thisBatchDetailsCanUse =
          (product?.returnableQuantity || 0) -
          (product.processingQuantity || 0);

        let aggregatedGoodBatchDetails: any[] = [];
        let aggregatedBadBatchDetails: any[] = [];
        let totalGoodReturnQuantity = 0;
        let totalBadReturnQuantity = 0;
        let totalGoodDiscount = 0;
        let totalBadDiscount = 0;

        const defaultValue = {
          invoiceNo: invoiceNo,
          productId: product.productId,
          productUOMId: product.productUOMId,
          returnUOMId: product.returnableUOMId,
          originalReturnUOMId: product.returnableUOMId,
          invoiceId: invoiceId,
          price: product.price,
          quantity: product.quantity,
          originalReturnQuantity: product.quantity,
          type: product.type,
          tax: product.taxRate,
          taxId: product.taxId,
          sku: productDetail?.sku,
          name: productDetail.name,
        };

        let totalAllocated = 0;

        product.batchDetails.forEach((batch) => {
          if (
            remainingGoodQuantity > 0 &&
            totalAllocated < thisBatchDetailsCanUse
          ) {
            const allocateGood = Math.min(
              remainingGoodQuantity,
              batch.invoiceQuantityInUnit,
              thisBatchDetailsCanUse - totalAllocated
            );

            if (allocateGood > 0) {
              aggregatedGoodBatchDetails.push({
                inventoryId: batch.inventoryId,
                batchQuantity: allocateGood,
              });
              totalGoodReturnQuantity += allocateGood;
              totalGoodDiscount += batch.discountInUnit
                ? batch.discountInUnit * allocateGood
                : 0;
              remainingGoodQuantity -= allocateGood;
              totalAllocated += allocateGood;
            }
          }

          if (
            remainingBadQuantity > 0 &&
            totalAllocated < thisBatchDetailsCanUse
          ) {
            const allocateBad = Math.min(
              remainingBadQuantity,
              batch.invoiceQuantityInUnit - totalGoodReturnQuantity,
              thisBatchDetailsCanUse - totalAllocated
            );

            if (allocateBad > 0) {
              aggregatedBadBatchDetails.push({
                inventoryId: batch.inventoryId,
                batchQuantity: allocateBad,
              });
              totalBadReturnQuantity += allocateBad;
              totalBadDiscount += batch.discountInUnit
                ? batch.discountInUnit * allocateBad
                : 0;
              remainingBadQuantity -= allocateBad;
              totalAllocated += allocateBad;
            }
          }
        });

        if (totalGoodReturnQuantity > 0) {
          newBatchDetails.push({
            ...defaultValue,
            batchDetails: aggregatedGoodBatchDetails,
            discount: totalGoodDiscount,
            returnQuantity: totalGoodReturnQuantity,
            reasonId: goodReturnDetails.reasonId,
            returnType: "PARTIAL",
            productCondition: "GOOD",
            pictures:
              productDetail.productUOM?.find(
                (item) => item.productUOMId === product.returnableUOMId
              )?.pictures || [],
          });
        }

        if (totalBadReturnQuantity > 0) {
          newBatchDetails.push({
            ...defaultValue,
            batchDetails: aggregatedBadBatchDetails,
            discount: totalBadDiscount,
            returnQuantity: totalBadReturnQuantity,
            reasonId: badReturnDetails.reasonId,
            returnType: "PARTIAL",
            productCondition: "BAD",
            pictures:
              productDetail.productUOM?.find(
                (item) => item.productUOMId === product.returnableUOMId
              )?.pictures || [],
          });
        }
      }
    }

    return newBatchDetails;
  };

  // Group the product by differnt company.
  const remapProductByCompany = (
    selectedInvoiceDetails: SelectedInvoiceProps[],
    invoiceId: string,
    tempData: any
  ) => {
    // Create a copy of the existing company-product map
    let updatedCompanyProductMap = new Map(tempData);

    //selected invoice deatils is that product to return from this invoices.

    for (let i = 0; i < selectedInvoiceDetails.length; i++) {
      let key = `${invoiceId}/${selectedInvoiceDetails[i].productId}`;
      if (!selectedRowKeys.includes(key)) {
        continue;
      }

      let goodReturnDetails = selectedInvoiceDetails[i]?.goodReturnDetails;
      let badReturnDetails = selectedInvoiceDetails[i]?.badReturnDetails;
      let companyId = selectedInvoiceDetails[i]?.companyId;

      // Arrange product batches based on good and bad return details
      let payload = arrangeProductBatchSource(
        selectedInvoiceDetails[i]?.invoiceNo,
        invoiceId,
        selectedInvoiceDetails[i].productLineDetails,
        goodReturnDetails,
        badReturnDetails,
        selectedInvoiceDetails[i].productDetails
      );

      // Check if company ID already exists in the map
      if (updatedCompanyProductMap.has(companyId)) {
        // If it exists, concatenate the new payload
        let details: any = updatedCompanyProductMap.get(companyId) || [];
        details = details.concat(payload);
        updatedCompanyProductMap.set(companyId, details);
      } else {
        // If it doesn't exist, initialize with the new payload
        updatedCompanyProductMap.set(companyId, payload);
      }
      //  setCompanyProductMap(updatedCompanyProductMap);
    }
    return updatedCompanyProductMap;
  };

  const arrangePayload = async () => {
    // handle no select product
    if (!selectedProductRowKeys.length) {
      MessageInfoUI("You have no select any product to return yet.");
      return;
    }

    let updatedSelectedInvoice = new Map(selectedInvoiceData);

    // if select product but no select invoices.
    if (!updatedSelectedInvoice.size) {
      MessageInfoUI(
        "No invoice product selected for the product wish to return"
      );
      return;
    }

    let tempData = new Map();
    let tempSelectedRowKeys = [...selectedRowKeys];

    const remapInvoiceKey = new Set(
      tempSelectedRowKeys.map((item: any) => item.split("/")[0])
    );

    for (const key of Array.from(new Set(remapInvoiceKey))) {
      // the row key pattern is "invoiceId/ProductId"
      let selectedInvoiceForCurrentProduct: SelectedInvoiceProps[] | undefined =
        updatedSelectedInvoice.get(key);

      // if no invoiced found for this selected product.
      if (!selectedInvoiceForCurrentProduct) {
        MessageInfoUI(`Fail to find selected invoice for${key} `);
        return;
      }

      const invoiceProducts = cloneDeep(selectedInvoiceForCurrentProduct);

      tempData = remapProductByCompany(invoiceProducts, key, tempData);
    }

    const currentDate = new Date();
    form.setFieldValue("returnDate", currentDate);
    setCompanyProductMap(tempData);
    setCurrentStep(1);
  };

  // *************************************************************************************
  // *** UI ***
  // *************************************************************************************

  // const handleFirstButtonClick = async () => {
  //   const progress: boolean = await handleOk(selectedProductRowKeys);
  //   if (!progress) {
  //     return;
  //   }
  //   // let allCheckedProduct = data.filter((item) => item.checked === true);
  //   let allCheckedProduct = selectedProductRowKeys;
  //   let tempMap = new Map(companyProductMap);

  //   allCheckedProduct.forEach((item) => {
  //     const product = editedData.find((val) => val.id === item);
  //     product.invoiceDetail.forEach((val: any) => {
  //       let remapProduct = {
  //         ...val,
  //         // pictures: pic,
  //         sku: product.sku,
  //         name: product.name,
  //         productUOM: product?.productUOM,
  //       };

  //       if (tempMap.has(val.company)) {
  //         let existingProducts = tempMap.get(val.company);

  //         const found = existingProducts?.findIndex(
  //           (i: any) =>
  //             i.productId === remapProduct.productId &&
  //             i.invoiceId === remapProduct.invoiceId &&
  //             i.productCondition === remapProduct.productCondition
  //         );

  //         if (found > -1) {
  //           existingProducts[found] = remapProduct;
  //         } else {
  //           existingProducts.push(remapProduct);
  //           tempMap.set(val.company, existingProducts);
  //         }
  //       } else {
  //         tempMap.set(val.company, [remapProduct]);
  //       }
  //     });
  //   });

  //   setCompanyProductMap(tempMap);
  //   const currentDate = new Date();
  //   form.setFieldValue("returnDate", currentDate);
  //   setCurrentStep(1);
  // };

  const buttons = [
    {
      label: t("Common.next"),
      // onClick: handleFirstButtonClick,
      onClick: arrangePayload,
      disabled: !(selectedProductRowKeys.length > 0),
    },
  ];

  const submitButtons = [
    {
      label: t("Submit"),
      onClick: submitGoodsReturn,
      loading: isSubmitting,
      // disabled: !(selectedRowData.length > 0),
    },
  ];

  const showContent = () => {
    if (currentStep === 0) {
      return (
        <div>
          <BackButtonUI
            title={t("GoodsReturn.partialGoodsReturn")}
            buttons={buttons}
          ></BackButtonUI>
          <Row className="gap-x-4 w-full flex flex-row pb-5">
            <Form form={filterForm} className="pt-1 flex-1">
              <Form.Item name="fuzzySearch" className="mb-0 h-full">
                <DebounceFilterTextInput
                  prefix={<SearchOutlined />}
                  placeholder={"Search"}
                  maxLength={50}
                  debounceTime={500}
                  // value={debounceValue}
                  onDebouncedChange={(item: any) => {
                    setFuzzySearchData(item);
                    setCursor("0");
                  }}
                />
              </Form.Item>
            </Form>
            <Tooltip
              placement="right"
              title={`This Field search by Product SKU, Product Name and Category`}
            >
              <QuestionCircleOutlined className="text-gray-400" />
            </Tooltip>
          </Row>
          <ListingTableUI
            loading={tableLoading}
            cursor={cursor}
            loader={showButtonLoader}
            dataSource={data}
            columns={column}
            pageSize={20}
            rowSelection={productRowSelection}
            rowKey={(record: any) => record.id}
            screenSize={isSmallScreen}
            isHeaderNotShow={isSmallScreen}
          ></ListingTableUI>
        </div>
      );
    } else if (currentStep === 1) {
      return (
        <div className="flex flex-col gap-y-3">
          <BackButtonUI
            buttons={submitButtons}
            title={t("GoodsReturn.goodsReturnSummary")}
          ></BackButtonUI>

          {/* <div className="bg-white flex-row flex justify-between py-2 ">
            <Col className="flex justify-center" span={5}></Col>
            <Col className="flex justify-center" span={4}>
              {t("GoodsReturn.invoiceNo")}
            </Col>
            <Col className="flex justify-center" span={2}>
              {t("GoodsReturn.invoiceQuantity")}
            </Col>
            <Col className="flex justify-center" span={2}>
              {t("GoodsReturn.returnQuantity")}
            </Col>
            <Col className="flex justify-center" span={2}>
              {t("GoodsReturn.unitPrice")}
            </Col>
            <Col className="flex justify-center" span={3}>
              {t("GoodsReturn.expiryDate")}
            </Col>
            <Col className="flex justify-center" span={2}>
              {t("GoodsReturn.goodsCondition")}
            </Col>
            <Col className="flex justify-center" span={4}>
              {t("GoodsReturn.reasonToReturn")}
            </Col>
          </div> */}

          {/* {Array.from(companyProductMap.values()).map((company) => { */}

          {Array.from(companyProductMap.entries()).map(([key, value]) => {
            return (
              <Row
                style={{
                  boxShadow:
                    "rgba(14, 63, 126, 0.06) 0px 0px 0px 1px, rgba(42, 51, 70, 0.03) 0px 1px 1px -0.5px, rgba(42, 51, 70, 0.04) 0px 2px 2px -1px, rgba(42, 51, 70, 0.04) 0px 3px 3px -1.5px, rgba(42, 51, 70, 0.03) 0px 5px 5px -2.5px, rgba(42, 51, 70, 0.03) 0px 10px 10px -5px, rgba(42, 51, 70, 0.03) 0px 24px 24px -8px",
                }}
              >
                <div
                  className="bg-purple px-5 py-1 w-full rounded-t-md"
                  key={key}
                >
                  <p className="text-textBlue font-bold">
                    {companyMap.get(key)?.companyName || ""}
                  </p>
                </div>
                {/* <Row className="w-full flex justify-between  bg-white">
                  <Col className="flex items-center pt-1 " span={5}>
                    <img
                      className="object-contain h-[80px] w-[80px] p-2"
                      src={(() => {
                        return item.pictures
                          ? PUBLIC_BUCKET_URL + item.pictures[0]
                          : defaultImage.src;
                      })()}
                      loading="lazy"
                      alt="company Image"
                    />
                    <div className="flex flex-col w-full">
                      <p className="font-bold text-[14px]">
                        {item?.name}&nbsp;
                      </p>
                      <p className="text-gray-500 text-[10px] w-full flex ">
                        <span>
                          {t("GoodsReturn.productCode")}: {item?.sku}
                        </span>
                      </p>
                    </div>
                  </Col>

                  <Col
                    className="flex items-center justify-center "
                    span={4}
                  >
                    {item.invoiceNo || ""}
                  </Col>
                  <Col
                    className="flex items-center justify-center "
                    span={2}
                  >
                    <p>
                      {item.quantity +
                        " " +
                        uomMap.get(item.productUOMId)?.name}
                    </p>
                  </Col>
                  <Col
                    className="flex items-center justify-center "
                    span={2}
                  >
                    <p>
                      {item.returnQuantity +
                        " " +
                        uomMap.get(item.returnUOMId)?.name}
                    </p>
                  </Col>
                  <Col
                    className="flex items-center justify-center "
                    span={2}
                  >
                    {"RM " + NumberThousandSeparator(item.price)}
                  </Col>
                  <Col
                    className="flex items-center justify-center "
                    span={3}
                  >
                    {formateDate(productExpiryDate.get(item.productId))}
                  </Col>
                  <Col
                    className="flex items-center justify-center "
                    span={2}
                  >
                    {capitalize(item.productCondition)}
                  </Col>
                  <Col
                    className="flex items-center justify-center "
                    span={4}
                  >
                    {reasonOption.find(
                      (reason) => reason.value === item.reasonId
                    )?.label || ""}
                  </Col>
                </Row> */}
                {value?.map((item: any, index: number) => {
                  return (
                    <div
                      className="w-full flex flex-row border-gray-300 bg-white gap-x-3 py-1"
                      key={item.productId + index}
                    >
                      <Col>
                        <img
                          className="object-contain h-[90px] sm:w-[80px] w-[200px] p-2"
                          src={(() => {
                            return item.pictures
                              ? PUBLIC_BUCKET_URL + item.pictures[0]
                              : defaultImage.src;
                          })()}
                          loading="lazy"
                          alt="company Image"
                        />
                      </Col>
                      <Col className="flex items-center pt-1 flex-col">
                        <Row className="flex flex-col w-full">
                          <p className="font-bold text-[14px]">
                            {item?.name}&nbsp;
                          </p>
                          <p className="text-labelGray  text-[10px] w-full flex ">
                            <span>
                              {t("GoodsReturn.productCode")}: {item?.sku}
                            </span>
                          </p>
                        </Row>
                        <Row className="w-full flex gap-x-8 sm:pt-1 pt-0 sm:gap-y-0 gap-y-3">
                          <Row className="flex flex-col ">
                            <span className="text-labelGray/70   font-semibold">
                              {t("GoodsReturn.invoiceNo")}
                            </span>
                            <span className="font-semibold text-labelGray ">
                              {item.invoiceNo || ""}
                            </span>
                          </Row>
                          <Row className="flex flex-col ">
                            <span className="text-labelGray/70   font-semibold">
                              {t("GoodsReturn.invoiceQuantity")}
                            </span>
                            <span className="font-semibold text-labelGray ">
                              {item.quantity +
                                " " +
                                uomMap.get(item.productUOMId)?.name}
                            </span>
                          </Row>
                          <Row className="flex flex-col ">
                            <span className="text-labelGray/70   font-semibold">
                              {t("GoodsReturn.returnQuantity")}
                            </span>
                            <span className="font-semibold text-labelGray ">
                              {item.returnQuantity +
                                " " +
                                uomMap.get(item.returnUOMId)?.name}
                            </span>
                          </Row>
                          <Row className="flex flex-col ">
                            <span className="text-labelGray/70   font-semibold">
                              {t("GoodsReturn.unitPrice")}
                            </span>
                            <span className="font-semibold text-labelGray ">
                              {"RM " + NumberThousandSeparator(item.price)}
                            </span>
                          </Row>
                          <Row className="flex flex-col ">
                            <span className="text-labelGray/70   font-semibold">
                              {t("GoodsReturn.expiryDate")}
                            </span>
                            <span className="font-semibold text-labelGray ">
                              {formateDate(
                                productExpiryDate.get(item.productId)
                              )}
                            </span>
                          </Row>
                          <Row className="flex flex-col ">
                            <span className="text-labelGray/70   font-semibold">
                              {t("GoodsReturn.goodsCondition")}
                            </span>
                            <span className="font-semibold text-labelGray ">
                              {capitalize(item.productCondition)}
                            </span>
                          </Row>
                          <Row className="flex flex-col ">
                            <span className="text-labelGray/70   font-semibold">
                              {t("GoodsReturn.reasonToReturn")}
                            </span>
                            <span className="font-semibold text-labelGray ">
                              {reasonOption.find(
                                (reason) => reason.value === item.reasonId
                              )?.label || ""}
                            </span>
                          </Row>
                        </Row>
                      </Col>
                    </div>
                  );
                })}

                <div
                  className="bg-labelGray/10 px-4 py-2 w-full rounded-b-md"
                  key={key}
                >
                  <Form
                    className="w-full "
                    form={form}
                    layout="horizontal"
                    scrollToFirstError
                  >
                    <Row className="w-full gap-y-2">
                      <Row className="w-full flex gap-x-3">
                        <Form.Item
                          rules={[
                            {
                              required: true,
                              message:
                                t("GoodsReturn.returnMode") +
                                " " +
                                t("Validation.requiredField"),
                            },
                          ]}
                          name={"returnMode" + key}
                          className="flex-1"
                          label={
                            <p className="text-neutral700 text-[12px]">
                              {t("GoodsReturn.returnMode")}
                            </p>
                          }
                        >
                          <SelectInput
                            placeholder={t("Invoice.selectReturnMode")}
                            showArrow={true}
                            options={returnModeOption}
                            onChange={(value) => {
                              if (value === "IMMEDIATE") {
                                form.setFieldValue(
                                  `returnDate${key}`,
                                  dayjs(new Date())
                                );
                                setReturnDateDisable(true);
                              } else {
                                setReturnDateDisable(false);
                                form.setFieldValue(`returnDate${key}`, "");
                              }
                            }}
                          />
                        </Form.Item>
                        <Form.Item
                          rules={[
                            {
                              required: true,
                              message:
                                t("GoodsReturn.returnDate") +
                                " " +
                                t("Validation.requiredField"),
                            },
                          ]}
                          name={"returnDate" + key}
                          className="flex-1 w-1/5"
                          label={
                            <p className="text-neutral700 text-[12px]">
                              {t("GoodsReturn.returnDate")}
                            </p>
                          }
                        >
                          <SingleDateInput
                            disabledDate={disabledDate}
                            disabled={returnDateDisable}
                            onChange={() => {}}
                          />
                        </Form.Item>
                      </Row>
                      <Row className="w-full flex gap-x-3">
                        <Form.Item
                          rules={[
                            {
                              required: true,
                              message:
                                t("GoodsReturn.remarks") +
                                " " +
                                t("Validation.requiredField"),
                            },
                          ]}
                          name={"remark" + key}
                          className="flex-1"
                          label={
                            <p className="text-neutral700 text-[12px]">
                              {t("GoodsReturn.remarks")}
                            </p>
                          }
                        >
                          <FormTextInput
                            placeholder={t("GoodsReturn.remarks")}
                            maxLength={100}
                          />
                        </Form.Item>
                        <Form.Item
                          name={"document" + key}
                          className="flex-1 "
                          label={
                            <p className="text-neutral700 text-[12px]">
                              {t("CreditNote.supportedDocument")}
                            </p>
                          }
                          rules={[
                            {
                              required: true,
                              message:
                                t("CreditNote.supportedDocument") +
                                " " +
                                t("Validation.requiredField"),
                            },
                          ]}
                        >
                          <Upload
                            {...props}
                            onPreview={(val: any) => {
                              // only uploaded photo can download and show
                              // new upload wont be able to click
                              if (val.url != undefined) {
                                let link = document.createElement("a");
                                link.target = "_blank";
                                link.href = val.url;
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                              }
                            }}
                            onChange={(info) => {
                              setAllFiles(info.fileList);
                            }}
                            onRemove={(info) => {
                              let updatedRecords = [...allFiles].filter(
                                (item) => item.uid !== info.uid
                              );

                              setAllFiles(updatedRecords);
                            }}
                            fileList={allFiles}
                          >
                            <div
                              className="flex items-center gap-x-4 p-1.5 text-buttonOrange border-2 border-buttonOrange bg-white font-semibold rounded-lg
                              hover:bg-buttonOrange hover:text-white transition-colors duration-300 cursor-pointer
                              "
                              // style={{
                              //   background: "rgb(255,201,153)",
                              //   background:
                              //     "radial-gradient(circle, rgba(255,201,153,1) 28%, rgba(254,166,84,1) 100%)",
                              // }}
                            >
                              <UploadOutlined />
                              <p>{t("CreditNote.supportedDocument")}</p>
                            </div>
                          </Upload>
                        </Form.Item>
                      </Row>
                    </Row>
                  </Form>
                </div>
              </Row>
            );
          })}
          <ForgotPasswordPreviousButtonUI
            label="Previous"
            onClick={() => {
              setCurrentStep(0);
              setCompanyProductMap(new Map());
            }}
            icon={<LeftOutlined />}
          ></ForgotPasswordPreviousButtonUI>
        </div>
      );
    }
  };

  return (
    <div className="flex-1 bg-bgOrange min-w-full min-h-screen">
      <Header items={headerItems} hasSearch={false} values={() => {}} />
      <Content className="flex flex-col mt-3 sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
      </Content>
      <Row className="justify-center w-full pt-4">
        {showScrollButton && (
          <FloatButton
            shape="circle"
            type="primary"
            onClick={() => {
              handleScrollToTop();
            }}
            style={{ right: 25, bottom: 120 }}
            icon={
              <ArrowUpOutlined
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              />
            }
          />
        )}
      </Row>
      <ModalUI
        closable={false}
        maskClosable={false}
        title={t("GoodsReturn.choose") + " " + t("GoodsReturn.invoice")}
        width="90%"
        visible={isModalOpen}
        // onOk={() => console.log("haha")}
        // onCancel={handleCancel}
        content={showModal()}
      />
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default PartialGoodsReturn;
