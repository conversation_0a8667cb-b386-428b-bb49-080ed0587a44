import apiHelper from "../pages/api/apiHelper";
import { DataSource, encodeParams, getNextQualifiedDate } from "../stores/utilize";
import { OutletNameAPI, RoleIds } from "../components/type";
import moment from "moment";

export const retailerInfo: any = {};
export const outletNameInfo: any = {};

export const getRoles = (value: any) => {
  const params = encodeParams({ id: value });
  const dataSource = new DataSource("roles", params, false);
  return dataSource
    .load()
    ?.then((res: any) => {
      if (res.items?.length > 0) {
        const policies = res.items.map((items: RoleIds) => items.policies).flat();
        const policiesPermission: any = {};

        // Policies Permission
        policies.map((key: string) => {
          const [policy, permission] = key.split("-");
          if (policiesPermission[policy]) {
            let temp = [...policiesPermission[policy], permission];
            const uniqueSet = Array.from(new Set(temp));
            const uniqueArray = uniqueSet.join("");
            policiesPermission[policy] = uniqueArray;
          } else policiesPermission[policy] = permission;
        });
        return policiesPermission;
      }

      return [];
    })
    .catch((error) => {
      console.error("Error fetching roles:", error);
      return [];
    });
};

export interface Retailer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  contact: string;
  ic: string;
  companyId: string;
  registeredRetailerId: string;
  // staffLevelId: string;
  // roleIds: string[];
  // policies: string[] | {};
}

export const getSuperAdminData = async () => {
  const res = await apiHelper.GET("admin", "v1");
  const user: Retailer = (res as any).item;

  // user.policies = await getRoles(user.roleIds);

  // Added empty companyId
  user.companyId = "";
  Object.assign(retailerInfo, user);

  return user;
};

export const getRetailerData = async () => {
  const res = await apiHelper.GET("retailer", undefined, undefined, "v2");
  const retailer: Retailer = (res as any).item;
  // user.policies = await getRoles(user.roleIds);
  Object.assign(retailerInfo, retailer);

  return retailer;
};

export const clearUserInfoAuth = () => {
  Object.keys(retailerInfo)?.map((key) => {
    delete retailerInfo[key];
  });
};

// get outlet info to check autoCheckout eligibility
export const getOutletData = async () => {
  const res = await apiHelper.GET(`outlet/name?id=${localStorage.getItem("currentOutletId")}`, undefined, undefined);
  const outlet: OutletNameAPI = (res as any)?.items?.[0] || {};

  // check autoCheckout eligibility
  if (Object.keys(outlet).length && outlet?.autoCheckoutSchedule && Object.keys(outlet?.autoCheckoutSchedule)?.length) {
    const nextQualifiedDate = await getNextQualifiedDate(outlet?.autoCheckoutSchedule)
    outlet.nextQualifiedDate = moment(nextQualifiedDate).format("YYYY-MM-DDT00:00:01") + "Z"

  } else if (!outlet?.autoCheckoutSchedule || Object.keys(outlet?.autoCheckoutSchedule)?.length === 0) {
    outlet.nextQualifiedDate = moment(new Date()).format("YYYY-MM-DDT00:00:01") + "Z"
  }
  Object.assign(outletNameInfo, outlet);

  return outlet;
};