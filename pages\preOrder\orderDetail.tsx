import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import defaultImage from "../../assets/default/emptyImage.png";
import { Form, Col, Row, Collapse } from "antd";
import {
  MessageErrorUI,
  MessageSuccessUI,
  ModalConfirmUI,
  TableUI,
} from "@/components/ui";
import { BackButtonUI } from "@/components/buttonUI";
import { FormTextInput, SingleDateInput } from "@/components/input";
import {
  CompanyGeneralInfo,
  Outlet,
  OutletShippingAddress,
  Product,
  ProductUOM,
  PreOrder,
  UOM,
  ProductsPreordered,
} from "@/components/type";
import {
  DataSource,
  NumberThousandSeparator,
  PUBLIC_BUCKET_URL,
  encodeParams,
  formateDate,
} from "@/stores/utilize";
import Header, { supportedLocales } from "@/components/header";
import apiHelper from "../api/apiHelper";
import AppFooter from "@/components/footer";
import moment from "moment";

function Checkout() {
  const router = useRouter();
  const { t } = useTranslation("common");
  const [form] = Form.useForm();

  const [data, setData] = useState<PreOrder[]>([]);
  const [objectStatus, setObjectStatus] = useState<string>("");
  const [productData, setProductData] = useState<any[]>([]);
  const [estimatedDeliveredDate, setEstimatedDeliveredDate] = useState("");
  const [outletMap, setOutletMap] = useState(new Map());
  const [companyMap, setCompanyMap] = useState(new Map());
  const [productMap, setProductMap] = useState(new Map());
  const [shippingAddressMap, setShippingAddressMap] = useState(new Map());
  const [uomMap, setUomMap] = useState(new Map());

  const topBarButtonOption = () => {
    // if (salesOrderStatus === "ONHOLD") {
    return [
      {
        buttonColor: "redButtonBg",
        type: "SECONDARY",
        onclick: () => {
          confirmDeletePreOrder();
        },
        disabled: false,
      },
    ];
  };

  const headerItems = [
    {
      label: t("Header.dashboard"),
      route: "/profile/dashboard",
      className: "labelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("PreOrder.preOrder"),
      route: "/preOrder/preOrderListing",
      className: "labelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("PreOrder.orderDetail"),
      route: "/preOrder/preOrderListing",
      className: "labelTextStyle",
    },
  ];

  useEffect(() => {
    if (router.isReady) {
      getPreOrder();
    }
  }, [router.isReady]);

  const getPreOrder = (
    isClearFilter = false,
    otherViewBranchAccess = false
  ) => {
    // const accessBranches = otherViewBranchAccess === false ? retailerAccess.companyBranchIds : otherCompanyBranchesIds;

    setTimeout(() => {
      let params: any = {
        sort: "createdAt",
        sortOrder: "-1",
        id: router.query.id,
      };

      // if (!isAdmin) {
      //   params.companyBranchIds = accessBranches;
      // }
      // const checkAdminRights = isAdmin ? params : params + "&companyId=" + retailerAccess.companyId;
      // const checkFilterRights = filterSetting && !isClearFilter ? filterSetting : encodeParams(params);
      const dataSource = new DataSource(
        "preorders",
        encodeParams(params),
        false
      );

      // !isAdmin && !otherViewBranchAccess ? filterForm.setFieldValue("companyId", retailerAccess.companyId) : null;

      // if ((accessBranches && accessBranches.length > 0) || isAdmin) {
      dataSource
        .load()
        .then((res: any) => {
          if (res && res.items !== null) {
            let data = res.items;
            const objectMap = data.reduce(
              (accumulator: any, current: PreOrder) => {
                accumulator["outletId"] = accumulator["outletId"] || [];
                if (
                  current.outletId &&
                  !outletMap.has(current.outletId) &&
                  !accumulator["outletId"].includes(current.outletId)
                ) {
                  accumulator["outletId"].push(current.outletId ?? "");
                }

                accumulator["companyId"] = accumulator["companyId"] || [];
                if (
                  current.companyId &&
                  !companyMap.has(current.companyId) &&
                  !accumulator["companyId"].includes(current.companyId)
                ) {
                  accumulator["companyId"].push(current.companyId ?? "");
                }

                accumulator["shippingAddressId"] =
                  accumulator["shippingAddressId"] || [];
                if (
                  current.shippingAddressId &&
                  !shippingAddressMap.has(current.shippingAddressId) &&
                  !accumulator["shippingAddressId"].includes(
                    current.shippingAddressId
                  )
                ) {
                  accumulator["shippingAddressId"].push(
                    current.shippingAddressId ?? ""
                  );
                }

                current.productsPreordered?.reduce(
                  (acc: any, product: ProductsPreordered) => {
                    accumulator["productId"] = accumulator["productId"] || [];
                    if (
                      product.productId &&
                      !productMap.has(product.productId) &&
                      !accumulator["productId"].includes(product.productId)
                    ) {
                      accumulator["productId"].push(product.productId ?? "");
                    }

                    accumulator["productUOMId"] =
                      accumulator["productUOMId"] || [];
                    if (
                      product.productUOMId &&
                      !uomMap.has(product.productUOMId) &&
                      !accumulator["productUOMId"].includes(
                        product.productUOMId
                      )
                    ) {
                      accumulator["productUOMId"].push(
                        product.productUOMId ?? ""
                      );
                    }

                    return acc;
                  },
                  {}
                );

                return accumulator;
              },
              {}
            );
            const addKeyData = data?.map((item: any, index: number) => {
              return {
                ...item,
                key: (index + 1).toString(),
              };
            });

            setObjectStatus(
              data[0]?.status
                ?.toLowerCase()
                .replace(/(?:^|\s)\S/g, (char: string) => char.toUpperCase())
            );

            getCompany(objectMap["companyId"]);
            getOutlets(objectMap["outletId"]);
            getProduct(objectMap["productId"]);
            getUOM(objectMap["productUOMId"]);
            getShippingAddress(objectMap["shippingAddressId"]);
            // getCompanyBranch(objectMap["companyBranchId"]);
            setData(addKeyData);
            setProductData(data[0].productsPreordered);
            let eta = "";
            if (
              data[0].status === "ACKNOWLEDGED" ||
              data[0].status === "COMPLETED"
            ) {
              eta = moment(data[0].estimatedDeliveredAt).format("DD-MM-YYYY");
            } else {
              eta = "-";
            }
            setEstimatedDeliveredDate(eta);
            form.setFieldValue("remark", data[0].remarks);
          }
        })
        .catch(() => {
          //* This Part need re-edit*//
        });
      // } else {
      //   setGoodsReturnListingInfoData([]);
      // }
    }, 500);
  };

  const getCompany = async (id: string[] = []) => {
    const dataSource = new DataSource(
      "companies",
      encodeParams({ id: id }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setCompanyMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: CompanyGeneralInfo) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getOutlets = async (id: string[] = []) => {
    let tempProductMap = new Map(outletMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "outlets",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });
        if (res !== null && res.items.length > 0) {
          setOutletMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Outlet) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getProduct = async (id: string[] = []) => {
    let tempProductMap = new Map(productMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "productCatalogues",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setProductMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Product) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Product) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getUOM = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("uoms", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setUomMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: UOM) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: UOM) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getShippingAddress = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "shippingAddresses",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setShippingAddressMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: OutletShippingAddress) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: OutletShippingAddress) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const confirmDeletePreOrder = () => {
    ModalConfirmUI({
      title: t("Modal.confirmCancel"),
      content: t("Modal.ensureContent"),
      okText: t("Common.ok"),
      cancelText: t("Common.cancel"),
      onOk: () => {
        cancelOrder(); //get all the taxes and exclude the selected one and update the data to status.
      },
      onCancel: () => { },
    });
  };

  const cancelOrder = () => {
    // //validate wheteher use select reason
    // cancelReasonForm
    //   .validateFields()
    //   .then(() => {
    let data = {
      // id: router.query.id,
      status: "CANCELLED",
    };
    apiHelper
      .PUT("preorder?id=" + router.query.id, data)
      ?.then(() => {
        MessageSuccessUI(
          t("PreOrder.preOrder") +
          " " +
          t("Common.cancel") +
          " " +
          t("Common.successful")
        );

        setTimeout(() => {
          router.reload();
        }, 500);
      })
      ?.catch(() => {
        //* This Part need re-edit*//
        MessageErrorUI(
          t("PreOrder.preOrder") +
          " " +
          t("Common.cancel") +
          " " +
          t("Common.failed")
        );
        router.reload();
      });
    //   })
    //   .catch(() => {
    //     MessageErrorUI(t("ReasonIsRequired"));
    //   });
  };

  const totalPriceOfMyCart = data.reduce((total: number, item: PreOrder) => {
    const totalPrice =
      (item.totalPrice ?? 0) - (item.totalDiscount ?? 0) + (item.totalTax ?? 0);
    total += totalPrice ?? 0;
    return total;
  }, 0);

  const column = [
    {
      title: t("PreOrder.product"),
      dataIndex: "productId",
      sorter: (a: any, b: any) => a.productId.localeCompare(b.productId),
      showSorterTooltip: false,
      key: "id",
      render: (_: any, record: ProductsPreordered) => {
        const item = productMap.get(record.productId);
        if (item) {
          return (
            <div>
              <Col className="flex items-center w-full tableRowNameDesign">
                <img
                  className="object-contain h-[80px] min-w-[80px] p-2 "
                  src={
                    item.productUOM.find(
                      (item: ProductUOM) =>
                        record.productUOMId === item.productUOMId
                    )?.pictures
                      ? PUBLIC_BUCKET_URL +
                      item.productUOM.find(
                        (item: ProductUOM) =>
                          record.productUOMId === item.productUOMId
                      )?.pictures[1]
                      : defaultImage.src
                  }
                  loading="lazy"
                ></img>
                <div className="flex flex-col w-full">
                  <p className="font-bold text-[14px]">{item.name}&nbsp;</p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("PreOrder.productCode")}: {item.sku}
                    </span>
                  </p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("PreOrder.uom")}:{" "}
                      {uomMap.get(record.productUOMId)?.name}
                    </span>
                  </p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("PreOrder.unitPrice")}:{" "}
                      {NumberThousandSeparator(record.unitPrice ?? 0)}
                    </span>
                  </p>
                </div>
              </Col>
            </div>
          );
        } else return null;
      },
    },
    {
      title: t("PreOrder.unitPrice"),
      dataIndex: "unitPrice",
      key: "unitPrice",
      render: (_: any, record: ProductsPreordered) => {
        return (
          <p className="tableRowNameDesign">
            {"RM " + NumberThousandSeparator(record.unitPrice ?? 0)}
          </p>
        );
      },
    },
    {
      title: t("PreOrder.quantity"),
      dataIndex: "quantity",
      key: "quantity",
      render: (_: any, record: ProductsPreordered) => {
        return <p className="tableRowNameDesign">{record.quantity}</p>;
      },
    },
    {
      title: t("PreOrder.uom"),
      dataIndex: "uom",
      key: "uom",
      render: (_: any, record: ProductsPreordered) => {
        return (
          <p className="tableRowNameDesign">
            {uomMap.get(record.productUOMId)?.name}
          </p>
        );
      },
    },
    // {
    //   title: t("PreOrder.discount"),
    //   dataIndex: "discount",
    //   key: "discount",
    //   render: (_: any, record: ProductsPreordered) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {"RM " + NumberThousandSeparator(record.totalDiscount ?? 0)}
    //       </p>
    //     );
    //   },
    // },
    {
      title: t("PreOrder.tax"),
      dataIndex: "tax",
      key: "tax",
      render: (_: any, record: ProductsPreordered) => {
        const total =
          (record.unitPrice ?? 0) *
          (record.quantity ?? 0) *
          (record.taxRate ?? 0);
        return (
          <p className="tableRowNameDesign">
            {"RM " + NumberThousandSeparator(total)}
          </p>
        );
      },
    },
    {
      title: t("PreOrder.itemSubtotal"),
      dataIndex: "totalPrice",
      key: "totalPrice",
      render: (_: any, record: ProductsPreordered) => {
        const tax =
          (record.unitPrice ?? 0) *
          (record.quantity ?? 0) *
          (record.taxRate ?? 0);

        const totalPrice =
          (record.unitPrice ?? 0) * (record.quantity ?? 0) +
          tax -
          (record.totalDiscount ?? 0);
        return (
          <p className="tableRowNameDesign text-right font-bold">
            {"RM " + NumberThousandSeparator(totalPrice)}
          </p>
        );
      },
    },
  ];

  const showContent = () => {
    return (
      <div className="flex flex-col gap-y-7">
        <BackButtonUI
          title={t("PreOrder.orderDetail")}
          buttons={[
            {
              buttonColor: "redButtonBg",
              label: t("PreOrder.cancelOrder"),
              onClick: confirmDeletePreOrder,
            },
          ]}
        ></BackButtonUI>

        {/* <Row className="mt-2 mb-7">
          <IconDescriptionButtonUI
            label={selectedLabel || t("Checkout.selectAddress")}
            onClick={() => setSelectAddressVisible(true)}
            icon={<EnvironmentFilled />}
            buttonIcon={<RightOutlined />}
            description={selectedDescription || ""}
          />
          <ModalUI
            className="font-bold"
            title={t("Checkout.deliveryAddress")}
            visible={isSelectAddressVisible}
            onCancel={() => setSelectAddressVisible(false)}
            width="60%"
          />
        </Row> */}
        <Row>
          <Collapse defaultActiveKey={["1"]} className="w-full">
            {/* <Collapse defaultActiveKey={[data?.length ? data[0].id || '' : '']} className="w-full"> */}
            {data.length > 0 ? (
              <Collapse.Panel
                header={
                  <p className="text-textBlue font-bold">
                    {companyMap.get(data[0].companyId)?.name}
                  </p>
                }
                key={data[0]?.key || "1"}
              >
                <div className="w-full ">
                  {/* order items */}
                  <div className="px-4 ">
                    <div className="flex justify-between md:flex-row flex-col">
                      <p className="mb-4 font-bold text-[20px]">
                        Ordered Items
                      </p>
                      <Form.Item
                        name={"status"}
                        label={
                          <p className="text-neutral700 text-[12px]">
                            {t("PreOrder.status")}
                          </p>
                        }
                      >
                        <FormTextInput
                          disabled
                          maxLength={100}
                          placeholder={objectStatus}
                        />
                      </Form.Item>
                    </div>
                    <TableUI
                      bordered
                      dataSource={productData}
                      columns={column}
                    />
                    <div className="flex flex-col w-full bg-lightGrey p-3">
                      {/* remark form */}
                      <Row className="justify-between pb-5 ">
                        <Form
                          className="w-full "
                          form={form}
                          layout="horizontal"
                          scrollToFirstError
                        >
                          <Row className="w-full  ">
                            <Row className="w-full flex gap-x-4 ">
                              <Form.Item
                                name={"remark"}
                                className="flex-1"
                                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                                  <p className="text-neutral700 text-[12px]">
                                    {t("PreOrder.remark")}
                                  </p>
                                }
                              >
                                <FormTextInput
                                  disabled
                                  maxLength={100}
                                  placeholder={""}
                                />
                              </Form.Item>
                              {/* <Col className="w-2/6">
                                <Form.Item
                                  name={"returnDate"}
                                  className="flex-1 "
                                >
                                  <SingleDateInput
                                    disabled
                                    placeholder={`Estimate delivery: Received by ${estimatedDeliveredDate}`}
                                    onChange={() => { }}
                                  />
                                </Form.Item>
                              </Col> */}
                              <Col className="flex items-center">
                                <p className="font-bold text-[16px] pr-2 ">
                                  {"RM " +
                                    NumberThousandSeparator(
                                      data[0].totalPrice ?? 0
                                    )}
                                </p>
                              </Col>
                            </Row>
                          </Row>
                        </Form>
                      </Row>
                      {/* order detail */}
                      <div>
                        <p className="font-bold mb-2 text-[16px]">
                          Order Detail
                        </p>
                        <Row className="flex justify-between">
                          <p>Sub Total</p>
                          <p className="">
                            {"RM " +
                              NumberThousandSeparator(data[0].totalPrice ?? 0)}
                          </p>
                        </Row>
                        <Row className="flex justify-between">
                          <p>Total Discount</p>
                          <p className="discount-color">
                            {"- RM " +
                              NumberThousandSeparator(
                                data[0].totalDiscount ?? 0
                              )}
                          </p>
                        </Row>
                        {/* <Row className="flex justify-between">
                          <p>Shipping Fee</p>
                          <p className="">
                            {"RM " +
                              NumberThousandSeparator(data[0].shippingFee ?? 0)}
                          </p>
                        </Row> */}
                        <Row className="flex justify-between">
                          <p>Total Tax</p>
                          <p className="">
                            {"RM " +
                              NumberThousandSeparator(data[0].totalTax ?? 0)}
                          </p>
                        </Row>
                        <div className="semiBold-horizontal-divider"> </div>
                        <Row className="flex justify-between">
                          <p className="font-bold text-[16px]">Total Amount</p>
                          <p className="font-bold text-[16px]">
                            {"RM " +
                              NumberThousandSeparator(totalPriceOfMyCart)}
                          </p>
                        </Row>
                      </div>
                    </div>
                  </div>
                </div>
              </Collapse.Panel>
            ) : null}
          </Collapse>
        </Row>
      </div>
    );
  };

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => { }} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
      </Content>
      <AppFooter retailerAccessValues={{}} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default Checkout;
