import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Logo1 from "../assets/error/serverDown.svg";
import { PrimaryButtonUI } from "../components/buttonUI";
import { supportedLocales } from "@/components/header";

export default function serverDown() {
  return (
    <div className="flex flex-col justify-center items-center relative gap-8 h-screen w-full">
      <div className="relative" style={{ width: 204.72, height: 259 }}>
        <Logo1 />
      </div>
      <div className="flex flex-col space-y-2 ">
        <p className="text-4xl font-bold text-center text-transparent bg-clip-text bg-gradient-to-br from-red-400 to-yellow-300">Server Down</p>
        <p className="text-base text-center text-gray-500">
          We’re sorry, the server is not available at the moment.
          <br />
          Please try again later or refresh the page.
        </p>
      </div>
      <div className="inline-flex space-x-2 items-center justify-center ">
        <PrimaryButtonUI onClick={() => window.location.reload()} label={"Refresh Page"} />
      </div>
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'], null, supportedLocales)),
    },
  };
}
