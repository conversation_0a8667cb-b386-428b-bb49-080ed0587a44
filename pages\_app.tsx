import "../styles/globals.css";
// import "antd/dist/antd";
import "../styles/style.scss";
import "tailwindcss/tailwind.css";
import type { AppProps } from "next/app";
import { ConfigProvider, Layout } from "antd";
import { appWithTranslation } from "next-i18next";
import "react-phone-number-input/style.css";
import nprogress from "nprogress";
import Loader from "./pageLoader";
import { useRouter } from "next/router";
import Router from "next/router";
import { useState, useEffect } from "react";
import Head from "next/head";
import useRetailerStore from "@/stores/store";
import { checkAccessControl } from "@/stores/utilize";
import apiHelper from "./api/apiHelper";

function MyApp({ Component, pageProps }: AppProps) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { Sider, Content } = Layout;
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // during router rendering
  Router.events.on("routeChangeStart", () => {
    setLoading(true);
  });

  // after router finish rendering
  Router.events.on("routeChangeComplete", () => {
    nprogress.done();
    setLoading(false);
  });

  const getRetailerAccess = async () => {
    const res: any = await apiHelper.GET(
      "retailer",
      undefined,
      undefined,
      "v2"
    );
    // const policies = await getRoles(res.item.roleIds);
    // res.item.policies = policies;
    // const policies = "";

    // let isUserAdmin = false;
    // if (policies?.["companyAdmin" as any]) isUserAdmin = true;
    useRetailerStore.setState({ retailer: res.item });
    // return policies;
  };

  // const getAdminAccess = async () => {
  //   let res: any = await apiHelper.GET("admin", "v1");
  //   // const policies = await getRoles(res.item.roleIds);
  //   const policies = "";
  //   // res.item.policies = policies;

  //   // Added empty companyId
  //   res.item.companyId = "";
  //   useRetailerStore.setState({ retailer: res.item, isAdmin: true, isCheckingAdmin: true, policies: policies });
  //   return policies;
  // };

  // when page changed, checking permission performed
  // disabled to direct reach page required

  useEffect(() => {
    const paths = [
      "login",
      "forgotPassword",
      "firstTimeLogin",
      "preRegister",
      "adminLogin",
      "signUp",
    ];

    // Set it to false when detected login page
    if (paths.some((path) => router.pathname.includes(path))) {
      setIsAuthenticated(false);
      router.push(router.pathname);
      return;
    }
    getAccess();
  }, [router.pathname]);

  const getAccess = async () => {
    if (!router.pathname.includes("login")) {
      const accessToken = await localStorage.getItem("accessToken");
      if (!accessToken) {
        setIsAuthenticated(false);
        router.push("/login");
      } else {
        setIsAuthenticated(true);
      }
      await getRetailerAccess();
      // const retailerData = useRetailerStore.getState().retailer;
      // if (!retailerData) {
      //   useRetailerStore.setState({ isCheckingAdmin: false });

      //   try {
      //     let accessPermission = await getRetailerAccess();
      //     // if (!accessPermission) {
      //     //   accessPermission = await getAdminAccess();
      //     // }

      //     if (!checkAccessControl(router.pathname, accessPermission as any) && router.pathname !== "/" && !router.pathname.includes("/pdf")) {
      //       // Perform your desired function here
      //       router.push("/500"); // need to modified later
      //     }
      //     setLoading(false);
      //   }
      //   catch (err: any) {
      //     // const accessPermission = await getAdminAccess();
      //     // if (!checkAccessControl(router.pathname, accessPermission as any) && router.pathname !== "/" && !router.pathname.includes("/pdf")) {
      //     //   // Perform your desired function here
      //     //   router.push("/500"); // need to modified later
      //     // }
      //     // setLoading(false);
      //   }
      // } else {
      //   useRetailerStore.setState({ policies: retailerData.policies });
      //   setLoading(false);
      // }
    }
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          fontFamily: "Mulish",
        },
        hashed: false,
      }}
    >
      <Layout className="font-mulish">
        <Head>
          <title>Neuroforce</title>
          <link rel="icon" href="/Neuroforce.svg" />
        </Head>
        <Layout>
          {loading ? <Loader /> : null}
          <Content className="flex w-full bg-lightPurple">
            <Component {...pageProps} />
          </Content>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
}

export default appWithTranslation(MyApp);
