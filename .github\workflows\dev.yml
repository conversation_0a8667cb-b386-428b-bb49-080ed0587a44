name: Deploy to GCP Dev VM

on:
  pull_request:
    types: [closed]
    branches:
      - dev  # Trigger when a PR is merged into dev

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository
      - name: Checkout code
        uses: actions/checkout@v3

      # Step 2: Set up SSH agent and load the private key from GitHub Secrets
      - name: Set up SSH agent
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}  # SSH key stored in GitHub Secrets

      # Step 3: Deploy to the GCP VM 1 via SSH
      - name: Deploy to GCP VM 1
        run: |
          ssh -o StrictHostKeyChecking=no zweileow@************* << 'EOF'
          echo "Switching to root and navigating to the app directory..."
          sudo su -  # Ensure the user has passwordless sudo privileges
          
          # Navigate to the application directory
          cd /var/www/yltc/ || exit
          
          echo "Pulling the latest changes from the 'dev' branch..."
          git pull origin dev

          echo "npm run build"
          npm run build
          
          echo "pm2 delete yltc"
          pm2 delete yltc

          echo "pm2 start npm --name yltc -- start"
          pm2 start npm --name yltc -- start -- --port 3001
          
          echo "Deployment completed successfully!"
          EOF
      
      # Step 3: Deploy to the GCP VM 2 via SSH
      - name: Deploy to GCP VM 2
        run: |
          ssh -o StrictHostKeyChecking=no zweileow@************** << 'EOF'
          echo "Switching to root and navigating to the app directory..."
          sudo su -  # Ensure the user has passwordless sudo privileges
          
          # Navigate to the application directory
          cd /var/www/yltc/ || exit
          
          echo "Pulling the latest changes from the 'dev' branch..."
          git pull origin dev

          echo "npm run build"
          npm run build
          
          echo "pm2 delete yltc"
          pm2 delete yltc

          echo "pm2 start npm --name yltc -- start"
          pm2 start npm --name yltc -- start -- --port 3001
          
          echo "Deployment completed successfully!"
          EOF
