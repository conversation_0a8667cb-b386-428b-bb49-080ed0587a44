import React, { useEffect, useState } from "react";
import { Row, Col, List, Card } from "antd";
import Header, { supportedLocales } from "../components/header";
import { Carousel } from "antd";
import { useRouter } from "next/router";

import PopUp from "../components/popUp";
import { ModalInfoUI, ProductBlock } from "../components/ui";
import PopUpNoImage from "../assets/sampleImage/popup_NoImage.jpg";
// import bigbanner1 from "../assets/sampleImage/Delfi.png";
// import bigbanner2 from "../assets/sampleImage/Dutch Lady.png";
// import bigbanner3 from "../assets/sampleImage/Etika.png";
// import bigbanner4 from "../assets/sampleImage/Etika 2.png";
// import bigbanner5 from "../assets/sampleImage/Mamee Double Decker.png";
// import bigbanner6 from "../assets/sampleImage/Mondelez.png";
// import bigbanner7 from "../assets/sampleImage/Nestle.png";
// import bigbanner8 from "../assets/sampleImage/Nestle 2.png";
// import bigbanner9 from "../assets/sampleImage/Socma.png";
// import bigbanner10 from "../assets/sampleImage/Tong Lee.png";
// import bigbanner11 from "../assets/sampleImage/Yee Lee Marketing.png";
// import defaultSmall1 from "../assets/sampleImage/9175.jpg";
// import defaultSmall2 from "../assets/sampleImage/small2.jpg";
import defaultBig from "../assets/sampleImage/temp1.png";
import noImage from "../assets/sampleImage/NoImagePlaceholder.jpg";
import { DataSource, PUBLIC_BUCKET_URL, encodeParams, removeDuplicateArray } from "@/stores/utilize";
import {
  Outlet,
  OutletPromotion,
  ProductCategories,
  Promotion,
  Retailer,
  TradeInfoAggregate,
  TradeInfoAggregateUI,
} from "@/components/type";
import moment from "moment";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import useRetailerStore from "@/stores/store";
import { getOutletData, getRetailerData } from "@/stores/authContext";
import { cloneDeep } from "lodash";
import Loader from "./pageLoader";
import AppFooter from "@/components/footer";

function Landing() {
  // const currentLanguage =
  // typeof window !== "undefined"
  //   ? localStorage.getItem("language") || "en"
  //   : "en";
  const { t } = useTranslation("common");
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({}); //access control
  const router = useRouter();
  const [isPopUpOpen, setIsPopUpOpen] = useState<boolean>(false); // Initialize with false initially
  const [activeCategory, setActiveCategory] = useState(null);
  const [outletInfo, setOutletInfo] = useState<Outlet>();
  const [activeAt, setActiveAt] = useState<string>(moment().startOf("day").add(1, "millisecond").toISOString());
  // const [productCategorySummary, setProductCategorySummary] = useState<
  //   ProductCategorySummary[]
  // >([]);
  const [productCategoriesSecondLevel, setProductCategoriesSecondLevel] = useState<ProductCategories[]>([]);
  const [outletPromotionProductList, setOutletPromotionProductList] = useState<string[]>([]);

  //Aggregate trade data
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [promotionProduct, setPromotionProduct] = useState<any[]>([]);
  // const [hotSellingId, setHotSellingId] = useState<any[]>([]);
  const [hotSellingProduct, setHotSellingProduct] = useState<any[]>([]);
  const [newProduct, setNewProduct] = useState<any[]>([]);
  // const [mustSellId, setMustSellId] = useState<any[]>([]);
  const [mustSellProduct, setMustSellProduct] = useState<any[]>([]);
  const [lowInventoryItems, setLowInventoryItems] = useState<any[]>([]);
  const [topSellingInMyArea, setTopSellingInMyArea] = useState<any[]>([]);
  // const [bannerData, setBannerData] = useState<RetailerBanner[]>([]);
  const [carouselImage, setCarouselImage] = useState<any[]>([]);
  const [popUpImage, setPopUpImage] = useState("");
  // const [small1Image, setSmall1Image] = useState("");
  // const [small2Image, setSmall2Image] = useState("");

  // Loader
  const [loading, setLoading] = useState(true);
  const [promotionLoading, setPromotionLoading] = useState(true);
  const [hotSellingLoading, setHotSellingLoading] = useState(true);
  const [mustSellLoading, setMustSellLoading] = useState(true);
  const [newProductLoading, setNewProductLoading] = useState(true);
  const [lowInventoryLoading, setLowInventoryLoading] = useState(true);
  const [topSellingLoading, setTopSellingLoading] = useState(true);

  useEffect(() => {
    if (router.isReady && Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }

      // get outlet info to check autoCheckout eligibility
      const outletNameData: any = useRetailerStore.getState().outletNameData || {};
      if (!Object.keys(outletNameData).length) {
        getOutletData().then((value: any) => {
          setActiveAt(value?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString());
        });
      } else {
        setActiveAt(outletNameData?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString());
      }
    }
  }, [router.isReady, Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    const popupClosed = localStorage.getItem("popupClosed");
    if (!popupClosed) {
      setIsPopUpOpen(true); // Open the pop-up if popupClosed is not set in localStorage
    }
  }, []);

  useEffect(() => {
    if (Object.keys(retailerAccess).length > 0) {
      // const fetchData = async () => {
      //   await getProductCategoriesSummary();
      // };
      // fetchData();
      // getProductCategoriesSecondLevel();
      // getSelfOutletPromotionProducts();
      // // getHotSellingProductTradeData();
      // getOutletProduct();
      // getBanner();

      if (retailerAccess?.outletIds && retailerAccess?.outletIds?.length === 0) {
        setLoading(false);
        setHotSellingLoading(false);
        setPromotionLoading(false);
        setMustSellLoading(false);
        setNewProductLoading(false);
        ModalInfoUI({
          title: t("Notice"),
          content: (
            <div>
              <p>Account did not assigned any Outlet. Please get admin to assign Outlet before capable of using retailer web.</p>
              <br />
              <p>Thank you for your understanding and patience.</p>
            </div>
          ),
          okText: t("Common.ok"),
          cancelText: t("Common.cancel"),
          onOk: () => {},
          onCancel: () => {},
        });
      } else {
        getProductCategoriesSecondLevel();
        getSelfOutletPromotionProducts();
        getOutletProduct();
        getMustSell();
        getBanner();
        getHotSelling();
        getAggreateProducts({}, "newProduct");
      }
    }
  }, [retailerAccess]);

  // useEffect(() => {
  //   if (productCategorySummary.length > 0) {
  //     getProductCategoriesSecondLevel(); // Call here after productCategorySummary is updated
  //   }
  // }, [productCategorySummary]);

  useEffect(() => {
    if (Object.keys(outletInfo || {}).length) {
      if (outletPromotionProductList.length > 0) {
        getAggreateProducts(outletInfo, "", outletPromotionProductList);
      } else {
        setPromotionLoading(false);
      }
      // getAggreateProducts({}, "HOTSELLING");
    }
  }, [outletInfo, outletPromotionProductList]);

  // page loading
  useEffect(() => {
    if (
      hotSellingLoading === false &&
      promotionLoading === false &&
      mustSellLoading === false &&
      newProductLoading === false
      // lowInventoryLoading === false &&
      // topSellingLoading === false
    ) {
      setLoading(false);
    }
  }, [hotSellingLoading, promotionLoading, mustSellLoading, newProductLoading, lowInventoryLoading, topSellingLoading]);

  // useEffect(() => {
  //   console.log("mustSellProduct: ", mustSellProduct);
  // }, [mustSellProduct]);

  //**************************************************************//
  //                    API Call
  //**************************************************************//

  const getBanner = async () => {
    try {
      let params = {
        id: localStorage.getItem("currentOutletId"),
      };
      const dataSource = new DataSource("retailerBanner/distinct", encodeParams(params), true, "v2");
      const res: any = await dataSource.load();
      if (res !== null) {
        const imageList: { image: string; links: string }[] = [];

        res.forEach((item: any) => {
          if (item.POPUP) {
            const popupImage = item.POPUP[0]?.image || "";
            if (popupImage) {
              setPopUpImage(popupImage.startsWith("http") ? popupImage : PUBLIC_BUCKET_URL + popupImage);
            }
          }

          if (item.CAROUSEL) {
            item.CAROUSEL.forEach((c: any) => {
              if (c.image) {
                const img = c.image.startsWith("http") ? c.image : PUBLIC_BUCKET_URL + c.image;
                imageList.push({
                  image: img,
                  links: c.link || "", // keep link too
                });
              }
            });
          }
        });

        if (imageList.length > 0) {
          setCarouselImage(imageList);
        }
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const getMustSell = async () => {
    try {
      let params = {
        // companyId: retsailerAccess?.companyId,
        id: localStorage.getItem("currentOutletId"),
      };
      const dataSource = new DataSource("mustSellList/distinct", encodeParams(params), false);
      const res: any = await dataSource.load();
      if (res !== null) {
        getAggreateProducts({}, "mustSell", res);
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const getHotSelling = async () => {
    try {
      let params = {
        // companyId: retsailerAccess?.companyId,
        id: localStorage.getItem("currentOutletId"),
      };
      const dataSource = new DataSource("hotSellingList/distinct", encodeParams(params), false);
      const res: any = await dataSource.load();
      if (res !== null) {
        getAggreateProducts({}, "hotSelling", res);
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  // const getProduct = async (productIds: string[] = []) => {
  //   if (!productIds?.length) return [];

  //   try {
  //     while (productIds?.length) {
  //       const dataSource = new DataSource(
  //         "productCatalogues",
  //         encodeParams({ id: productIds.splice(0, 50), status: "ACTIVE" }),
  //         false
  //       );
  //       dataSource
  //         .load()
  //         .then((res: any) => {
  //           if (res !== null && res.items.length > 0) {
  //             setProductCatalogueMap((prevDataMap) => {
  //               const newDataMap = new Map(prevDataMap);
  //               res.items.forEach((item: Product) => {
  //                 if (!newDataMap.has(item.id)) {
  //                   newDataMap.set(item.id, item);
  //                 }
  //               });
  //               return newDataMap;
  //             });
  //           }
  //         })
  //         .catch(() => {
  //           //* This Part need re-edit*//
  //         });
  //     }
  //   } catch (err) {}
  //   return [];
  // };

  // const headerItems = [
  //   {
  //     label: t("Landing.promotion"),
  //     route: "/product/promotion",
  //     className: "clickableLabelTextStyle",
  //   },
  //   {
  //     label: t("Landing.hotSelling"),
  //     route: "/landing",
  //     className: "clickableLabelTextStyle",
  //   },
  //   {
  //     label: t("Landing.lowInventoryItems"),
  //     route: "/landing",
  //     className: "clickableLabelTextStyle",
  //   },
  //   {
  //     label: t("Landing.topSellingInMyArea"),
  //     route: "/landing",
  //     className: "clickableLabelTextStyle",
  //   },
  // ];

  // Function to close the pop-up
  const handleClosePopUp = () => {
    setIsPopUpOpen(false); // Update the state to close the pop-up
    localStorage.setItem("popupClosed", "true"); // Set flag in localStorage to prevent pop-up from showing again
  };

  const popUpImageURL = popUpImage !== "" ? popUpImage : PopUpNoImage.src;

  // const handleToCategory = (categoryName: any) => {
  //   router.push(`/product/${categoryName}`);
  // };

  const handleSeeMore = (route: any, category: any, id: any) => {
    router.push({
      pathname: route,
      query: { category: category, categoryId: id },
    });
  };

  const handleToProduct = (route: any, product: any) => {
    router.push({
      pathname: route,
      query: {
        productId: product?.productCatalogueId,
        productInfo: JSON.stringify(product),
      },
    });
  };

  // const getProductCategoriesSummary = async () => {
  //   try {
  //     const dataSource = new DataSource("productCategory/summary", "", true);
  //     const res: any = await dataSource.load();
  //     if (res?.length) {
  //       setProductCategorySummary(res);
  //       return res;
  //     }
  //     return [];
  //   } catch (err) {
  //     return [];
  //   }
  // };

  const getProductCategoriesSecondLevel = async () => {
    try {
      // const findTrading = productCategorySummary.find(
      //   (a: any) => a._id === "65a78877325f3fd96a808b33"
      // );
      // const subCategoryIds = findTrading?.subCategoryIds;
      // const dataSource = new DataSource(
      //   "productCategories",
      //   `?parentCategoryIds=${subCategoryIds?.join(
      //     "&parentCategoryIds="
      //   )}&sort=createdAt&sortOrder=-1`,
      //   true
      // );
      const dataSource = new DataSource("productCategories", "parentCategoryIds=65a78877325f3fd96a808b33", true);
      const res: any = await dataSource.load();
      if (res && res !== null) {
        setProductCategoriesSecondLevel(res);
      } else {
        setProductCategoriesSecondLevel([]);
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const getSelfOutletPromotionProducts = () => {
    const dataSource = new DataSource(
      "promotion/outlets",
      encodeParams({
        outletId: localStorage.getItem("currentOutletId"),
        activeAt: activeAt,
      }),
      true
    );
    dataSource.load().then((res: any) => {
      if (res !== null && res.length > 0) {
        let id: string[] = [];
        res.forEach((item: OutletPromotion) => {
          if (item.productId) {
            id.push(item.productId);
          }
        });
        setOutletPromotionProductList(id);
      }
    });
  };

  const getOutletProduct = () => {
    let params: any = {
      sort: "createdAt",
      sortOrder: "-1",
      id: localStorage.getItem("currentOutletId"),
    };
    const dataSource = new DataSource("outlets", encodeParams(params), false);
    dataSource.load().then(async (res: any) => {
      if (!res?.items?.length) {
        // stop loading here.
        return;
      }

      // const objectMap = res.items.reduce((accumulator: any, current: Outlet) => {

      //   accumulator["companyId"] = accumulator["companyId"] || [];

      //   if (
      //     current.companyId &&
      //     !companyMap.has(current.companyId) &&
      //     !accumulator["companyId"].includes(current.companyId)
      //   ) {
      //     accumulator["companyId"].push(current.companyId ?? "");
      //   }

      //   current.outletProductList?.reduce(
      //     (acc: any, product: OutletProductList) => {
      //       accumulator["productId"] = accumulator["productId"] || [];
      //       if (
      //         product.productId &&
      //         !productMap.has(product.productId) &&
      //         !accumulator["productId"].includes(product.productId)
      //       ) {
      //         accumulator["productId"].push(product.productId ?? "");
      //       }
      //       return acc;
      //     }, {}
      //   );
      //   return accumulator;
      // }, {});

      const outlet: Outlet = res.items[0];

      delete outlet.outletProductList;

      setOutletInfo(outlet);
      // const company = await getCompany(objectMap["companyId"]);
      // if (company?.length) {
      // setOutletProductListId(objectMap['productId']);
      // getProduct(objectMap["productId"], 0, company[0].id, outlet);
      // }
    });
  };

  const getPromotions = async (outletCategoryIds: string[] = []) => {
    try {
      const params = {
        activeAt: activeAt,
        status: "ACTIVE",
        outletCategoryIds,
      };
      const dataSource = new DataSource("promotions", encodeParams(params), true);
      const res: any = await dataSource.load();
      if (res?.length) {
        setPromotions(res);
        return res;
      }
      return [];
    } catch (err) {
      return [];
    }
  };

  const getAggreateProducts = async (outlet?: Outlet, tradeLabel?: string, productCatalogueIds?: string[]) => {
    let results: TradeInfoAggregate[] = [];
    try {
      let firstTenItems: any = [];

      while (productCatalogueIds && productCatalogueIds.length) {
        const params: any = {
          // autoCheckout eligibility date
          activeAt: activeAt,
          sellingType: "SELLING",
          companyId: retailerAccess.companyId,
          outletId: localStorage.getItem("currentOutletId"),
          maxResultsPerPage: 100,
          pageNumber: "1",
        };

        if (productCatalogueIds?.length) {
          params.productCatalogueId = productCatalogueIds.slice(0, 50);
          // Remove processed items from the array
          productCatalogueIds = productCatalogueIds.slice(50);
        }

        // if (tradeLabel) {
        //   params.tradeLabels = tradeLabel;
        // }
        // params.tradeType = filterValue.tradeType
        // params.sellingPriceGT = filterValue.sellingPriceGT
        // params.sellingPriceLT = filterValue.sellingPriceLT

        if (tradeLabel === "newProduct") {
          params.tradeLabels = "NEWARRIVAL";
        }

        const dataSource = new DataSource("productTradeInfo/aggregate", encodeParams(params), false);
        const res: any = await dataSource.load();

        if (res?.items?.length > 0) {
          results = results.concat(res.items);
        }

        if (productCatalogueIds.length === 0) {
          setPromotionProduct([]);
          setPromotionLoading(false);
          setHotSellingLoading(false);
          setMustSellLoading(false);
          setNewProductLoading(false);
          break;
        }
      }

      const productTradeInfoAggregate: TradeInfoAggregate[] = results;

      const productCatalogueId = productTradeInfoAggregate.map((item) => item.productCatalogueId);

      const promotionRelateds = await getPromotionProduct(cloneDeep(productCatalogueId));

      const promotionRelatedOutlets = await getPromotionRelatedOutlet(
        promotionRelateds,
        [],
        // outletInfo?.companyBranchId || ""
        ""
      );

      const promotionRelatedOutletIds = promotionRelatedOutlets.map((promotion) => promotion.id);

      const promotionsCanApply = promotionRelateds.map((promotion) => {
        const intersection = promotion.promoIds.filter((item) => promotionRelatedOutletIds.includes(item));
        if (intersection?.length) {
          promotion.promoIds = intersection.flat();
        } else {
          promotion.promoIds = [];
        }
        return promotion;
      });

      let tradeInfoProductUI = productTradeInfoAggregate.map((item: TradeInfoAggregateUI) => {
        const promotionProduct = promotionsCanApply?.find((promotion) => promotion.productId === item.productCatalogueId);

        let isSinglePromo = false,
          isBundlePromo = false;
        let promotionInitId: string[] = [];

        // for upload outlet list
        if (promotionProduct) {
          promotionRelatedOutlets.map((promotion) => {
            if (promotionProduct.promoIds.includes(promotion?.id || "")) {
              if ((promotion?.productGroups?.length || 0) > 1) isBundlePromo = true;
              if ((promotion?.productGroups?.length || 0) === 1) isSinglePromo = true;
              promotionInitId = promotionInitId.concat(promotion?.id || "");
            }
          });
        }

        const promotionProductByCategoires = getPromotionByProductId(item.productCatalogueId, cloneDeep(promotionRelatedOutlets));

        if (promotionProductByCategoires.length) {
          promotionProductByCategoires.map((promotion: any) => {
            if ((promotion?.productGroups?.length || 0) > 1) isBundlePromo = true;
            if ((promotion?.productGroups?.length || 0) === 1) isSinglePromo = true;
            promotionInitId = promotionInitId.concat(promotion?.id || "");
          });
        }

        item.isSinglePromo = isSinglePromo;
        item.isBundlePromo = isBundlePromo;
        item.outletCompanyId = outlet?.companyId || outletInfo?.companyId;
        item.promotionInitId = Array.from(new Set(promotionInitId));
        return item;
      });

      if (promotionRelatedOutlets?.length && !tradeLabel) {
        firstTenItems = removeDuplicateArray(
          tradeInfoProductUI.filter((item) => item.isSinglePromo || item.isBundlePromo).slice(0, 9),
          "productCatalogueId"
        );
      } else if (tradeLabel) {
        firstTenItems = tradeInfoProductUI.slice(0, 9);
      }

      if (firstTenItems) {
        firstTenItems.sort((a: any, b: any) => {
          if (a.name < b.name) {
            return -1;
          }
          if (a.name > b.name) {
            return 1;
          }
          return 0;
        });
      }

      if (tradeLabel === "mustSell") {
        // setHotSelling(firstTenItems);
        setMustSellProduct(firstTenItems);
        setMustSellLoading(false);
      } else if (tradeLabel === "hotSelling") {
        // setHotSelling(firstTenItems);
        setHotSellingProduct(firstTenItems);
        setHotSellingLoading(false);
      } else if (tradeLabel === "newProduct") {
        // setHotSelling(firstTenItems);
        setNewProduct(firstTenItems);
        setNewProductLoading(false);
      } else {
        setPromotionProduct(firstTenItems);
        setPromotionLoading(false);
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const getPromotionProduct = async (id: string[] = []): Promise<OutletPromotion[]> => {
    try {
      if (!id.length) return [];

      const params = {
        outletId: localStorage.getItem("currentOutletId"),
        productId: id,
        activeAt: activeAt,
      };
      const dataSource = new DataSource("promotion/outlets", encodeParams(params), false);
      const res: any = await dataSource.load();
      if (res?.length) {
        return res;
      }
      return [];
    } catch (err) {
      return [];
    }
  };

  const getPromotionRelatedOutlet = async (
    promotionRelatedOutlet: OutletPromotion[],
    outletCategoryIds: string[],
    outletCompanBranchId: string
  ): Promise<Promotion[]> => {
    try {
      if (!promotionRelatedOutlet.length) return [];

      let groupPromotionId: Array<string> = [];
      promotionRelatedOutlet.map((promotion) => {
        promotion.promoIds.map((item) => {
          if (!groupPromotionId.includes(item)) {
            groupPromotionId.push(item);
          }
        });
      });

      if (!groupPromotionId.length) return [];

      let results: Array<Promotion> = [];

      while (groupPromotionId.length) {
        const params = {
          id: groupPromotionId.splice(0, 50),
          outletCategoryIds,
          companyBranchIds: outletCompanBranchId,
          activeAt: activeAt,
          status: "ACTIVE",
        };
        const dataSource = new DataSource("promotions", encodeParams(params), false);
        const res: any = await dataSource.load().catch((err) => {
          groupPromotionId = [];
        });
        results = results.concat(res?.items || []);
      }
      return results;
    } catch (err) {
      return [];
    }
  };

  const getPromotionByProductId = (productId: string, tempPromotions?: Promotion[]) => {
    if (!productId) return [];

    const promotionsCheck = tempPromotions?.length ? tempPromotions : promotions;

    if (promotionsCheck?.length) return [];

    const productPromotions = promotionsCheck.filter((item) => {
      const productIds = item.productGroups?.flatMap((product) => product.selectedProducts?.flatMap((selected) => selected.productId));
      return productIds?.includes(productId);
    });
    return productPromotions;
  };

  // Category Carousel responsive settings
  const responsiveSettings = [
    {
      breakpoint: 600,
      settings: {
        slidesToShow: Math.min(3, productCategoriesSecondLevel.length),
        slidesToScroll: Math.min(3, productCategoriesSecondLevel.length),
        dots: productCategoriesSecondLevel.length > 3,
        draggable: productCategoriesSecondLevel.length > 3,
        autoplay: false,
      },
    },
    {
      breakpoint: 950,
      settings: {
        slidesToShow: Math.min(4, productCategoriesSecondLevel.length),
        slidesToScroll: Math.min(4, productCategoriesSecondLevel.length),
        dots: productCategoriesSecondLevel.length > 4,
        draggable: productCategoriesSecondLevel.length > 4,
        autoplay: false,
      },
    },
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: Math.min(6, productCategoriesSecondLevel.length),
        slidesToScroll: Math.min(6, productCategoriesSecondLevel.length),
        dots: productCategoriesSecondLevel.length > 6,
        draggable: productCategoriesSecondLevel.length > 6,
        autoplay: false,
      },
    },
    {
      breakpoint: 3360,
      settings: {
        slidesToShow: Math.min(8, productCategoriesSecondLevel.length),
        slidesToScroll: Math.min(8, productCategoriesSecondLevel.length),
        dots: productCategoriesSecondLevel.length > 8,
        draggable: productCategoriesSecondLevel.length > 8,
        autoplay: false,
      },
    },
  ];

  if (loading) return <Loader />;

  return (
    <div className="flex-1 w-full bg-bgOrange">
      <Header items={[]} hasSearch={true} values={() => {}} />
      {isPopUpOpen && <PopUp imageURL={popUpImageURL} onClose={handleClosePopUp} targetRoute={"/landing"} />}
      <div className="w-full sm:w-4/5 sm:mx-auto mb-8 sm:pt-20 pt-16 sm:mb-0">
        {/* Content to be centered */}
        {/* <div className="grid xl:grid-cols-3 tablet:grid-cols-2 gap-3"> */}
        {/* <div className="col-span-2 custom-carousel-container relative"> */}
        {/* Large item carousel */}

        {carouselImage.length > 1 ? (
          <Carousel autoplay autoplaySpeed={4000} dotPosition="bottom">
            {carouselImage.map((item, index) => (
              <div key={index} className="relative">
                <a href={item.links || "#"} target="_blank" rel="noopener noreferrer">
                  <img className="w-full h-auto" src={item.image} alt={`carousel image ${index}`} />
                </a>
              </div>
            ))}
          </Carousel>
        ) : carouselImage.length === 1 ? (
          <div className="relative">
            <img className="w-full h-auto" src={carouselImage[0]} alt="Single carousel image" />
          </div>
        ) : (
          <Carousel autoplay autoplaySpeed={4000} dotPosition="bottom">
            <div className="relative">
              <img
                className="w-full h-auto"
                src={defaultBig.src} // Use your default image source here
                alt="default image 1"
              />
            </div>
            <div className="relative">
              <img
                className="w-full h-auto"
                src={defaultBig.src} // You can change this to another default image if needed
                alt="default image 2"
              />
            </div>
          </Carousel>
        )}
        {/* </div> */}
        {/* Right two small banners */}
        {/* <div className="xl:col-span-1 tablet:col-span-2 xs:col-span-3 xl:flex-col tablet:flex-row justify-between hidden xl:flex ">
            <div className="custom-carousel-container">
              <img
                className="w-full h-auto object-cover"
                src={small1Image !== "" ? small1Image : defaultSmall1.src}
                alt="Small Item 1"
              />
            </div>
            <div className="custom-carousel-container">
              <img
                className="w-full h-auto object-cover"
                src={small2Image !== "" ? small2Image : defaultSmall2.src}
                alt="Small Item 2"
              />
            </div>
          </div> */}
        {/* </div> */}

        {/* Category */}
        <div className="sm:mt-5 p-2 mt-2">
          <Row className=" justify-between w-full h-full bg-button pb-2">
            <Col className="textSubTitle font-semibold text-labelDark/80">
              {t("Landing.shopByCategory")}
              <hr
                className="w-full border-2 border-transparent rounded-full "
                style={{
                  borderWidth: "2px",
                  borderStyle: "solid",
                  borderImage: "linear-gradient(to right, #FEA6544D, #FEA65480, transparent) 1",
                }}
              />
            </Col>
          </Row>
          {/* <div className="max-w-full h-[150px] overflow-hidden pt-2"> */}
          {/* <div className="pt-2 max-w-full xs:max-w-xl md:max-w-3xl lg:max-w-4xl xl:max-w-full pb-[20px]"> */}
          <div className="flex flex-wrap w-full gap-x-2">
            {/* <Carousel
                responsive={responsiveSettings}
                className="category-carousel"
              > */}
            {productCategoriesSecondLevel.map((category, index) => (
              <Card
                key={index}
                className="flex-1 min-w-0 flex flex-col items-center justify-center gap-3 
                      hover:shadow-lg transition-all duration-300 cursor-pointer
                      border border-labelGray/20 hover:border-buttonOrange/30"
                // style={{
                //   background:
                //     "linear-gradient(to bottom, white, rgba(128, 128, 128, 0.1))",
                // }}
                onClick={() => {
                  handleSeeMore("/product/productCategory", category.name, category.id);
                }}
              >
                <div className="grid grid-row-1 md:grid-row-3 sm:py-6 ">
                  <div className="flex justify-center items-center flex-col gap-4">
                    <img
                      src={
                        category.picture
                          ? PUBLIC_BUCKET_URL + category.picture
                          : PUBLIC_BUCKET_URL + "productCategory/6434057ec0230ed7c57b0803/box.png"
                      }
                      alt={category.name}
                      className="category-icon "
                    />
                    <p className="capitalize text-labelDark/80 text-[10px] sm:text-[14px]">
                      {category.name ? category.name.toLowerCase() : ""}
                    </p>
                  </div>
                </div>
              </Card>
            ))}
            {/* </Carousel> */}
          </div>
          {/* </div> */}
        </div>

        {promotionProduct.length <= 0 ? null : (
          <div className="sm:mt-5 p-2 mt-2">
            <Row className=" justify-between w-full h-full pb-2">
              <Col className="textSubTitle font-semibold text-labelDark/80">
                {t("Landing.promotion")}
                <hr
                  className="w-full border-2 border-transparent rounded-full "
                  style={{
                    borderWidth: "2px",
                    borderStyle: "solid",
                    borderImage: "linear-gradient(to right, #FEA6544D, #FEA65480, transparent) 1",
                  }}
                />
              </Col>
              <Col className="flex items-center">
                <button
                  onClick={() => {
                    handleSeeMore("/product/productCategory", "", "");
                    localStorage.setItem("stockType", "PROMOTION");
                  }}
                  className="text-brand-blue text-sm font-medium flex items-center gap-1 transition-transform hover:translate-x-1"
                >
                  <span className="relative after:absolute after:left-0 after:bottom-[-1px] after:w-full after:h-[1px] after:bg-current after:scale-x-0 hover:after:scale-x-100 after:transition-transform after:duration-300">
                    See More
                  </span>
                  <span className="text-lg transition-transform duration-300 group-hover:translate-x-1">→</span>
                </button>
              </Col>
            </Row>
            <Row className="w-full justify-evenly">
              <List
                grid={{
                  gutter: [8, 8],
                  xs: 2,
                  sm: 3,
                  md: 4,
                  lg: 6,
                  xl: 7,
                  xxl: 9,
                }}
                className="overflow-x-hidden w-full"
                dataSource={promotionProduct}
                renderItem={(product: TradeInfoAggregate) => (
                  <List.Item key={product.productCatalogueId}>
                    <div
                      onClick={() => handleToProduct("/product/productDetail", product)}
                      // className="product-col"
                      className="rounded-xl overflow-hidden border border-brand-orange/10 hover:shadow-md transition-all duration-300 cursor-pointer group "
                      style={{
                        background: "linear-gradient(to right, rgba(249, 245, 240, 100), white)",
                      }}
                    >
                      <ProductBlock
                        className="flex flex-col items-center w-full"
                        productName={product?.name || "EMPTY"}
                        product={product}
                        price={product.sellingPrice || 0.0}
                        image={(product?.picture && PUBLIC_BUCKET_URL + product.picture) || noImage.src}
                      />
                    </div>
                  </List.Item>
                )}
              />
            </Row>
          </div>
        )}

        {mustSellProduct.length <= 0 ? null : (
          <div className="sm:mt-5 p-2 mt-2">
            <Row className=" justify-between w-full h-full pb-2">
              <Col className="textSubTitle font-semibold text-labelDark/80">
                {t("Landing.mustSellList")}
                <hr
                  className="w-full border-2 border-transparent rounded-full "
                  style={{
                    borderWidth: "2px",
                    borderStyle: "solid",
                    borderImage: "linear-gradient(to right, #FEA6544D, #FEA65480, transparent) 1",
                  }}
                />
              </Col>
              <Col className="flex items-center">
                <button
                  onClick={() => {
                    handleSeeMore("/product/productCategory", "", "");
                    localStorage.setItem("stockType", "MUSTSELL");
                  }}
                  className="text-brand-blue text-sm font-medium flex items-center gap-1 transition-transform hover:translate-x-1"
                >
                  <span className="relative after:absolute after:left-0 after:bottom-[-1px] after:w-full after:h-[1px] after:bg-current after:scale-x-0 hover:after:scale-x-100 after:transition-transform after:duration-300">
                    See More
                  </span>
                  <span className="text-lg transition-transform duration-300 group-hover:translate-x-1">→</span>
                </button>
              </Col>
            </Row>
            <Row className="w-full justify-evenly">
              <List
                grid={{
                  gutter: [8, 8],
                  xs: 2,
                  sm: 3,
                  md: 4,
                  lg: 6,
                  xl: 7,
                  xxl: 9,
                }}
                className="overflow-x-hidden w-full"
                dataSource={mustSellProduct}
                renderItem={(product: TradeInfoAggregate) => (
                  <List.Item key={product.productCatalogueId}>
                    <div
                      onClick={() => handleToProduct("/product/productDetail", product)}
                      className="rounded-xl overflow-hidden border border-brand-orange/10 hover:shadow-md transition-all duration-300 cursor-pointer group "
                      style={{
                        background: "linear-gradient(to right, rgba(249, 245, 240, 100), white)",
                      }}
                    >
                      <ProductBlock
                        className="flex flex-col items-center w-full "
                        productName={product?.name || "EMPTY"}
                        product={product}
                        price={product.sellingPrice || 0.0}
                        image={(product?.picture && PUBLIC_BUCKET_URL + product.picture) || noImage.src}
                      />
                    </div>
                  </List.Item>
                )}
              />
            </Row>
          </div>
        )}

        {hotSellingProduct.length <= 0 ? null : (
          <div className="sm:mt-5 p-2 mt-2">
            <Row className=" justify-between w-full h-full pb-2">
              <Col className="textSubTitle font-semibold text-labelDark/80">
                {t("Landing.myHotSelling")}
                <hr
                  className="w-full border-2 border-transparent rounded-full "
                  style={{
                    borderWidth: "2px",
                    borderStyle: "solid",
                    borderImage: "linear-gradient(to right, #FEA6544D, #FEA65480, transparent) 1",
                  }}
                />
              </Col>
              <Col className="flex items-center">
                <button
                  onClick={() => {
                    handleSeeMore("/product/productCategory", "", "");
                    localStorage.setItem("stockType", "HOTSELLING");
                  }}
                  className="text-brand-blue text-sm font-medium flex items-center gap-1 transition-transform hover:translate-x-1"
                >
                  <span className="relative after:absolute after:left-0 after:bottom-[-1px] after:w-full after:h-[1px] after:bg-current after:scale-x-0 hover:after:scale-x-100 after:transition-transform after:duration-300">
                    See More
                  </span>
                  <span className="text-lg transition-transform duration-300 group-hover:translate-x-1">→</span>
                </button>
              </Col>
            </Row>
            <Row className="w-full justify-evenly">
              <List
                grid={{
                  gutter: [8, 8],
                  xs: 2,
                  sm: 3,
                  md: 4,
                  lg: 6,
                  xl: 7,
                  xxl: 9,
                }}
                className="overflow-x-hidden w-full"
                dataSource={hotSellingProduct}
                renderItem={(product: TradeInfoAggregate) => (
                  <List.Item key={product.productCatalogueId}>
                    <div
                      onClick={() => handleToProduct("/product/productDetail", product)}
                      className="rounded-xl overflow-hidden border border-brand-orange/10 hover:shadow-md transition-all duration-300 cursor-pointer group "
                      style={{
                        background: "linear-gradient(to right, rgba(249, 245, 240, 100), white)",
                      }}
                    >
                      <ProductBlock
                        className="flex flex-col items-center w-full "
                        productName={product?.name || "EMPTY"}
                        product={product}
                        price={product.sellingPrice || 0.0}
                        image={(product?.picture && PUBLIC_BUCKET_URL + product.picture) || noImage.src}
                      />
                    </div>
                  </List.Item>
                )}
              />
            </Row>
          </div>
        )}

        {newProduct.length <= 0 ? null : (
          <div className="sm:mt-5 p-2 mt-2">
            <Row className=" justify-between w-full h-full pb-2">
              <Col className="textSubTitle font-semibold text-labelDark/80">
                {t("Landing.newProduct")}
                <hr
                  className="w-full border-2 border-transparent rounded-full "
                  style={{
                    borderWidth: "2px",
                    borderStyle: "solid",
                    borderImage: "linear-gradient(to right, #FEA6544D, #FEA65480, transparent) 1",
                  }}
                />
              </Col>
              <Col className="flex items-center">
                <button
                  onClick={() => {
                    handleSeeMore("/product/productCategory", "", "");
                    localStorage.setItem("stockType", "NEWPRODUCT");
                  }}
                  className="text-brand-blue text-sm font-medium flex items-center gap-1 transition-transform hover:translate-x-1"
                >
                  <span className="relative after:absolute after:left-0 after:bottom-[-1px] after:w-full after:h-[1px] after:bg-current after:scale-x-0 hover:after:scale-x-100 after:transition-transform after:duration-300">
                    See More
                  </span>
                  <span className="text-lg transition-transform duration-300 group-hover:translate-x-1">→</span>
                </button>
              </Col>
            </Row>
            <Row className="w-full justify-evenly">
              <List
                grid={{
                  gutter: [8, 8],
                  xs: 2,
                  sm: 3,
                  md: 4,
                  lg: 6,
                  xl: 7,
                  xxl: 9,
                }}
                className="overflow-x-hidden w-full"
                dataSource={newProduct}
                renderItem={(product: TradeInfoAggregate) => (
                  <List.Item key={product.productCatalogueId}>
                    <div
                      onClick={() => handleToProduct("/product/productDetail", product)}
                      className="rounded-xl overflow-hidden border border-brand-orange/10 hover:shadow-md transition-all duration-300 cursor-pointer group "
                      style={{
                        background: "linear-gradient(to right, rgba(249, 245, 240, 100), white)",
                      }}
                    >
                      <ProductBlock
                        className="flex flex-col items-center w-full "
                        productName={product?.name || "EMPTY"}
                        product={product}
                        price={product.sellingPrice || 0.0}
                        image={(product?.picture && PUBLIC_BUCKET_URL + product.picture) || noImage.src}
                      />
                    </div>
                  </List.Item>
                )}
              />
            </Row>
          </div>
        )}

        {lowInventoryItems.length <= 0 ? null : (
          <div className=" p-5 product-bar bg-white mt-5">
            <Row className="font-bold justify-between w-full h-full border-b-2 border-black">
              <Col className="text-lg">{t("Landing.lowInventoryItems")}</Col>
              <Col>
                <button
                  className="text-gray-400 text-xs hover:underline"
                  onClick={() => handleSeeMore("/product/productCategory", "lowinventory", "")}
                >
                  {t("Common.seeMore") + " >"}
                </button>
              </Col>
            </Row>
            <Row className="justify-between">
              {/* <Col 
                        onClick={() => handleToProduct('/login')}
                        className="product-col"
                      >  
                        <ProductBlock
                          productName="Mister Potato crisps - 14 x 150g/130g"
                          price={50.00}
                          companyName="Yee Lee Marketing"
                          image={potatocrisps.src}
                        />
              </Col> */}
            </Row>
          </div>
        )}

        {topSellingInMyArea.length <= 0 ? null : (
          <div className=" p-5  bg-white mt-5">
            <Row className="font-bold justify-between w-full h-full border-b-2 border-black">
              <Col className="text-lg">{t("Landing.topSellingInMyArea")}</Col>
              <Col>
                <button
                  className="text-gray-400 text-xs hover:underline"
                  onClick={() => handleSeeMore("/product/productCategory", "topselling", "")}
                >
                  {t("Common.seeMore") + " >"}
                </button>
              </Col>
            </Row>
            <Row>
              {/* <Col 
                onClick={() => handleToProduct('/login')}
                className="product-col"
                >  
                <ProductBlock
                  productName="Mister Potato crisps - 14 x 150g/130g"
                  price={50.00}
                  companyName="Yee Lee Marketing"
                  image={potatocrisps.src}
                />
              </Col> */}
            </Row>
          </div>
        )}
      </div>
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ["common"], null, supportedLocales)),
    },
  };
}

export default Landing;
