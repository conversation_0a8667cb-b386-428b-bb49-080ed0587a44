// Global Color Variable
$lightPurple: #e6e6e6;
$primaryBlue: #0c24ff;
$buttonOrange: #fea654;
$lightGrey: #f5f5f5;

//// BUTTON ////
.button-themeColor:hover,
.css-dev-only-do-not-override-11xg00t
  .ant-btn-default:not(:disabled):not(.ant-btn-disabled):hover {
  color: #fea654 !important;
}

//a is from global
a.ant-btn {
  padding: 8px 16px !important;
  line-height: 30px;
}

.buttonStyle {
  height: 36px;
  min-width: 120px;
  width: auto;
  padding: 8px 24px !important;
  display: flex;
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 12px;
}

.buttonStyle:hover {
  display: flex;
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  transition: all 0.5s ease-in-out;
}

// Primary Button Disabled Style
.ant-btn-primary[disabled],
.ant-btn-primary[disabled]:hover,
.ant-btn-primary[disabled]:focus,
.ant-btn-primary[disabled]:active {
  color: white !important;
  border-color: #d9d9d9;
  background: #f5f5f5;
  text-shadow: none;
  box-shadow: none;
}

.primaryButtonBg {
  background-image: linear-gradient(
    97.69deg,
    #5151e3 0%,
    #8475e0 100%
  ) !important;
  box-shadow: none;
  // transition: 0.6s;
}

.primaryButtonBg:hover {
  background-image: none !important;
  background-color: transparent;
  border: 0px solid #5151e3;
  color: #5151e3;
  transition: all 1.5s ease-in-out;
}

.secondaryButtonBg {
  border: none;
  color: #5151e3;
  background-image: linear-gradient(
    97.69deg,
    rgb(255, 255, 255) 0%,
    rgb(255, 255, 255) 100%
  ) !important;
}

// Disabled button
.disabledButtonBg {
  background-image: linear-gradient(
    97.69deg,
    rgba(12, 36, 255, 0.3) 0%,
    rgba(104, 131, 230, 0.3) 100%
  ) !important;
  border-color: white;
}

// Disabled button changes
.disabledButtonBg .ant-btn-primary[disabled],
.ant-btn-primary[disabled]:hover,
.ant-btn-primary[disabled]:focus,
.ant-btn-primary[disabled]:active {
  color: white;
}

.disabledButtonStyle {
  transition: none;
  border: 2px solid white;
  height: 48px;
  min-width: 120px;
  width: auto;
  padding: 8px 24px;
  display: flex;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  color: white;
}

.disabledButtonStyle:hover {
  border: 2px solid white;
  background-color: linear-gradient(
    97.69deg,
    rgba(12, 36, 255, 0.3) 0%,
    rgba(104, 131, 230, 0.3) 100%
  ) !important;
  cursor: no-drop;
  box-shadow: white !important;
  color: white;
}

.icon-button {
  color: white;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  // height: 36px;
  // width: 36px;
}

.icon-button:hover,
.icon-button:focus {
  background-color: #5151e3;
  border-color: #5151e3;
}

.ant-menu-light .ant-menu-item-selected {
  background-color: rgba(128, 128, 128, 0.3) !important;
  /* Grey with 50% opacity */
  color: black !important;
}

.ant-input-outlined:hover,
.ant-input-outlined:focus-within {
  border-color: $buttonOrange !important;
}

.ant-checkbox-indeterminate .ant-checkbox-inner:after {
  background-color: $buttonOrange;
}

//// FORM ////

// All form item margin btm
.ant-form-item {
  margin-bottom: 0px;
}

.ant-form-item-control-input-content {
  flex: auto;
  max-width: 100%;
  word-break: break-all;
}

.textInput {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 1rem;
  border: 1px solid $lightPurple;
  border-radius: 12px;
  font-size: 12px;
  line-height: 20px;
  height: 59px;
}

.formTextInput {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 16px;
  border: 1px solid $lightPurple;
  border-radius: 12px;
  font-size: 12px;
  line-height: 20px;
  height: 36px;
}

.iconDescriptionButton {
  color: #5151e3;
  height: auto;
  width: 100%;
  background-color: #fff !important;
  border: 1px solid $lightPurple;
  border-radius: 12px;
  display: flex;
  padding: 12px;
}

.iconDescriptionButton-Arrow {
  width: 16.66667%;
  display: flex;
  align-self: center;
  height: auto;
  padding-right: 10px;
  justify-content: flex-end;
  color: #000;
}

//// OTP ////

.otp-group {
  display: flex;
  width: 100%;
  max-width: 360px;
  column-gap: 10px;
}

.otp-input {
  width: 100%;
  height: 60px;
  border: 1px solid #ccc;
  border-radius: 5px;
  text-align: center;
  font-size: 32px;
  font-weight: bold;
  line-height: 1;
  background-color: white;
}

//// ANTD UPLOAD BUTTON ////

.ant-upload-list-item-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

body h1 {
  //  font-family: "Mulish", "sans-serif";
  font-size: 24px;
  font-weight: bold;
}

.ant-upload-wrapper.ant-upload-picture-card-wrapper
  .ant-upload.ant-upload-select {
  width: 112px;
}

.ant-upload-wrapper.ant-upload-picture-card-wrapper
  .ant-upload-list.ant-upload-list-picture-card
  .ant-upload-list-item-container {
  width: 112px;
}

// body p{
//   //  font-family: "Mulish", "sans-serif";
// }
//// MODAL ////

// Design note: The overall container
.modalDesign .ant-modal-content {
  border-radius: 22px;
}

// Design Note: This configures the default button (eg, Cancel) in modal
.modalDesign .ant-btn-default {
  border: transparent !important;
  color: #5151e3 !important;
  width: 143px;
  height: 48px;
  font-size: 16px;
  font-weight: 700;
}

// Design Note: This configures the primary button in modal
.modalDesign .ant-btn-primary {
  background: linear-gradient(97.69deg, #0c24ff 0%, #6883e6 100%);
  border-radius: 12px;
  color: white;
  text-decoration: white;
  width: 143px;
  height: 48px;
  font-size: 16px;
  font-weight: 700;
}

// Design Note: This configures the description of modal
.modalDesign .ant-modal-confirm-body .ant-modal-confirm-content {
  margin-top: 8px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
}

// Design Note: The container for the buttons in modal
.ant-modal-confirm .ant-modal-confirm-btns {
  margin-top: 24px;
  text-align: center !important;
}

// Design Note: This configures the title of the modal
.modalDesign .ant-modal-confirm-body .ant-modal-confirm-title {
  display: block;
  overflow: hidden;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 700;
  font-size: 20px;
  line-height: 1.4;
}

// Design Note: This removes the icon set by ant design for modal
.modalDesign .ant-modal-confirm-warning .ant-modal-confirm-body > .anticon,
.ant-modal-confirm-confirm .ant-modal-confirm-body > .anticon {
  display: none;
}

// Design Note: This removes the margin set by ant design for description of the message
.modalDesign
  .ant-modal-confirm-body
  > .anticon
  + .ant-modal-confirm-title
  + .ant-modal-confirm-content {
  margin-left: 0;
}

.menuModalDesign {
  width: 100%;
  height: auto;
  display: flex;
  align-items: center;
  // padding: 1rem;
}

//// CAROUSEL ////

.ant-carousel .slick-prev,
.ant-carousel .slick-next,
.ant-carousel .slick-prev:hover,
.ant-carousel .slick-next:hover {
  font-size: inherit;
  color: currentColor;
}

.custom-carousel-container {
  border-radius: 10px;
  /* Adjust the radius value as needed */
  overflow: hidden;
  /* Ensures content doesn't overflow the rounded corners */
  position: relative;
  /* Allows positioning of the arrows */
}

.vertical-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  /* Set the height for each vertical slide */
}

.custom-arrow-carousel {
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.arrow-up,
.arrow-down {
  cursor: pointer;
  font-size: 24px;
}

.arrow-up {
  margin-bottom: 10px;
}

.arrow-down {
  margin-top: 10px;
}

//// BAR ////

// .category-bar {
//   margin: 20px;
// }

.category-row {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-evenly;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 5px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  border: 1px solid transparent;
  /* Change this color to the desired border color */
  transition: border-color 0.3s ease;
  padding: 15px;
}

.category-item:hover {
  border-color: rgb(223, 161, 120);
  /* Add a thin border on hover */
}

.borderHover {
  cursor: pointer;
  border: 1px solid transparent;
  transition: border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
  width: 150px !important;
  margin: 5px;
}

.borderHover:hover {
  transform: scale(1.1);
  /* Scale effect on hover */
}

.category-icon-container {
  width: 40px;
  /* Set a fixed width for the icon container */
  height: 50px;
  /* Set a fixed height for the icon container */
  overflow: hidden;
}

.category-icon {
  width: 40px;
  /* Make the image fill its container */
  display: flex;
  height: auto;
  /* Maintain aspect ratio */
}

.category-name {
  min-width: 100px;
  /* Set a fixed width for the category name */
  display: block;
  word-wrap: break-word;
  text-align: center;
}

//// PRODUCT ////

// .product-bar {
//   margin: 20px;
// }

.product-col {
  cursor: pointer;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  margin: 0.5rem;
  border: 1px solid transparent;
  border-radius: 6px;
  // width: 100px;
}

.product-col:hover {
  border: 1px solid $buttonOrange;
  box-shadow: 0 0 5px rgba(223, 161, 120, 0.3);
}

.ant-list-grid .ant-col > .ant-list-item {
  display: block;
  max-width: 100%;
  margin-block-end: 0px;
  padding-block: 0;
  border-block-end: none;
}

//// INPUT ////

.ant-select-single {
  width: 100%;
}

.ant-select-single:hover {
  height: 32px;
}

.singleSelectorInput .ant-select-selector {
  display: flex;
  align-items: center;
  padding-left: 1rem !important;
  min-height: 36px !important;
  border-radius: 12px !important;
  border: 1px solid $lightPurple;
}

.singleSelectorInput .ant-select-selection-search-input {
  height: 100% !important;
  font-size: 12px;
}

.singleSelectorInput .ant-select-selection-placeholder {
  font-size: 12px !important;
  line-height: 20px;
  padding-left: 12px;
}

.singleSelectorInput .ant-select-selection-item {
  font-size: 12px !important;
}

.customSelectDropdown:hover {
  // border: 1px solid #fea654;
  border-radius: 12px;
  border-color: #fea654;
  // background-color: #fea654;
  /* Add any other desired hover styles */
}

.counter.ant-btn {
  font-size: 14px;
  height: 28px;
  width: 24px;
  padding: 0px 6px;
  border-radius: 6px;
}

.numberInput {
  display: flex;
  border-radius: 12px;
  font-size: 12px;
  height: 36px;
  border: 1px $lightPurple solid;
  align-items: center;
}

.numberInput .ant-input-number-input-wrap {
  display: flex;
  width: 100%;
  border-color: $buttonOrange !important;
}

// Design Note: This design directly affects the presets set by ant design for number input
.numberInput .ant-input-number-input {
  display: flex;
  padding: 10px 3px;
  height: 0;
  justify-content: center;
  font-size: 12px;
  text-align: center;
  width: 100%;
  border-color: $buttonOrange !important;
}

.numberInput .ant-input-number-outlined:hover {
  border-color: $buttonOrange !important;
}

.formNumberInput {
  flex: auto !important;
  padding-top: 6px;
  padding-bottom: 6px;
  padding-left: 16px;
  padding-right: 3px;
}

.formNumberInput .ant-form-item {
  flex: none;
  margin-bottom: 0 !important;
}

.checkBoxDesign .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #fea654;
  border-color: #fea654;
}

.checkBoxDesign .ant-checkbox-indeterminate .ant-checkbox-inner::after {
  background-color: #fea654;
}

.fixed-bottom-bar {
  position: sticky;
  // position: fixed;
  bottom: 0;
  // left: 0;
  width: 100%;
  justify-content: end;
  align-items: center;
  // z-index: 10;
}

.app-footer {
  bottom: 0;
  left: 0;
}

// .myCartMainBody {
//   height: calc(100% - 300px);
// }

.radioInput {
  display: flex;
  align-self: center;
  padding-right: 10px;
  width: 16px;
  height: 16px;
  border: none;
  outline: none;
  cursor: pointer;
}

.ant-checkbox-checked .ant-checkbox-inner {
  border-color: $buttonOrange;
}

.ant-radio-checked .ant-radio-inner {
  border-color: $buttonOrange;
  background-color: $buttonOrange;
}

//// STRING ////

.description-Gray {
  display: flex;
  align-self: flex-start;
  color: #666666;
  font-size: 0.75rem;
}

.labelTextStyle {
  color: black;
  text-decoration: none;
  font-weight: bold;
}

.clickableLabelTextStyle:hover {
  text-decoration: underline;
  cursor: pointer;
  color: blue;
}

.clickableLabelTextStyle {
  color: black;
}

//// PANEL ////

.panel {
  border: none;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.sub-panel {
  border-bottom: 1px solid #ccc;
  padding-bottom: 10px;
}

.sub-panel .ant-checkbox-group {
  display: flex;
  flex-direction: column;
}

.panel-container {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  /* Display categories in a column */
  justify-content: flex-start;
  /* Align categories to the left */
}

.panel-item {
  border: none;
  cursor: pointer;
}

.panel-item:hover {
  color: orange;
}

.panel-row {
  display: flex;
  flex-direction: row;
}

.panel-filter button {
  margin-top: 10px;
  padding: 8px 16px;
  border: 1px solid #ccc;
  background-color: #fff;
  cursor: pointer;
}

.highlight-category {
  color: orange;
}

//// GRID ////

.tableRowNameDesign {
  color: #151584;
  font-weight: 400;
  font-size: 12px;
}

.ant-table-column-sorter-up.active {
  color: $buttonOrange !important;
}

.ant-table-column-sorter-down.active {
  color: $buttonOrange !important;
}

//// status color ////

.statusTag {
  display: flex;
  justify-content: center;
  min-width: 72px;
  width: fit-content;
  width: -moz-fit-content;
  padding: 4px 8px;
  border-radius: 5px;
}

.orangeStatusColor {
  color: #d28b00;
}

.orangeStatusBackgroundColor {
  background-color: #fffadf;
}

.orangeStatusBorderColor {
  border: 1px solid #d28b00;
}

.greenStatusColor {
  color: #3f9e43;
}

.greenStatusBackgroundColor {
  background-color: #eefff2;
}

.greenStatusBorderColor {
  border: 1px solid #3f9e43;
}

.redStatusColor {
  color: red;
}

.redStatusBackgroundColor {
  background-color: rgb(255, 232, 236);
}

.redStatusBorderColor {
  border: 1px solid red;
}

.blueStatusBackgroundColor {
  background-color: #cdeff1;
}

.blueStatusBorderColor {
  border: 1px solid #3175d5;
}

.blueStatusColor {
  color: #738eda;
}

.brownStatusColor {
  color: rgb(153, 78, 8);
}

.brownStatusBackgroundColor {
  background-color: rgb(238, 207, 177);
}

.brownStatusBorderColor {
  border: 1px solid rgb(94, 63, 10);
}

.purpleStatusBackgroundColor {
  background-color: #e6dff6;
}

.purpleStatusBorderColor {
  border: 1px solid #ad64c9;
}

.purpleStatusColor {
  color: #bb31f1;
}

//// GRID ////

.grid-container {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  /* Four columns */
  grid-gap: 24px;
  /* Gap between grid items */

  /* Media query for responsiveness */
  @media (max-width: 1200px) {
    grid-template-columns: repeat(3, minmax(0, 1fr));
    /* Three columns for smaller screens */
  }

  @media (max-width: 900px) {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    /* Two columns for even smaller screens */
  }

  @media (max-width: 600px) {
    grid-template-columns: minmax(0, 1fr);
    /* One column for the smallest screens */
  }
}

//// CARD ////

.displayCard .ant-card-body {
  padding: 12px;
}

.largeClickableCard .ant-card .ant-card-body {
  padding: 0 !important;
}

.clickableCardBg {
  border: dashed;
  border-width: 2px;
  border-color: #5151e3;
  width: 120px;
  height: 120px;
  display: flex;
  flex-direction: column;
  text-align: center;
  margin-top: 2px;
  align-items: center;
}

.clickableCardOrangeBg {
  border: dashed;
  border-width: 2px;
  border-color: #f97316;
  width: 120px;
  height: 120px;
  display: flex;
  flex-direction: column;
  text-align: center;
  margin-top: 2px;
  align-items: center;
  color: #f97316;
}

.largeClickableCardBg {
  border: dashed;
  border-width: 2px;
  border-color: #5151e3;
  width: 100%;
  height: 90px;
}

.largeClickableCardOrangeBg {
  border: dashed;
  border-width: 2px;
  border-color: #f97316;
  width: 100%;
  height: 50px;
  color: #f97316;
}

.orangeImage {
  filter: hue-rotate(130deg);
}

.clickableCardImageComponent {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding-bottom: 0.5rem;
}

.clickableCardText {
  font-size: 10px;
  font-weight: bold;
  word-break: break-word;
  // flex-wrap: wrap;
  // display: flex;
  margin-top: 2px;
}

.largeClickableCardText {
  font-size: 12px;
  font-weight: bold;
  word-break: break-word;
}

.dateInput {
  display: flex;
  padding: 1rem;
  border: 1px solid #e6e6e6;
  border-radius: 12px;
  line-height: 20px;
  align-items: center;
  text-align-last: left !important;
  height: 36px !important;
  // min-width: 200px;
}

:where(.css-dev-only-do-not-override-2q8sxy)
  .ant-picker
  .ant-picker-input
  > input {
  font-size: 12px;
}

//// DIVIDER

.bold-horizontal-divider {
  border-top: 1.5px solid lightGrey;
  border-style: dashed;
  width: 100%;
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.semiBold-horizontal-divider {
  border-top: 1px solid black;
  margin-top: 1rem;
  margin-bottom: 1rem;
}

//// TEXT

.discount-color {
  color: red;
}

.cart-text-details {
  color: #718096;
  /* or any other gray color you prefer */
  font-size: 12px;
  width: 100%;
  display: flex;
  padding-top: 4px;
  /* Adjust the value as needed */
}

//// TABLE

.ant-table-thead th {
  text-align: center !important;
}

// .ant-collapse {
//   box-sizing: border-box;
//   margin: 0;
//   padding: 0;
//   color: rgba(0, 0, 0, 0.88);
//   font-size: 14px;
//   line-height: 1.5714285714285714;
//   list-style: none;
//   font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';
//   background-color: rgba(0, 0, 0, 0.02);
//   border: 1px solid transparent;
//   border-bottom: 0;
//   border-radius: 12px;
// }

.ant-collapse .ant-collapse-content > .ant-collapse-content-box {
  padding: 16px 0px;
}

.ant-collapse > .ant-collapse-item > .ant-collapse-header {
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  align-items: flex-start;
  padding: 12px 16px;
  color: #151584;
  line-height: 1.5714285714285714;
  cursor: pointer;
  transition: all 0.3s, visibility 0s;
  background-color: #eaeafd;
}

//// PAGINATION

.pagination-container {
  display: flex;
  justify-content: flex-end;
}

//// IMAGE

.image-container {
  max-width: 165px;
  max-height: 130px;
}

.image-text-container {
  max-width: 100%;
  max-height: 132px;
  // min-width: 160px;
  min-height: 130px;
}

.filterTextInput {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 16px;
  border: 1px solid #f7f7f7;
  border-radius: 12px;
  font-size: 11px;
  line-height: 20px;
  height: 36px;
  flex: -1;
}

.custom-upload .ant-upload-list-item-actions {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ant-select-disabled {
  .ant-select-selector {
    color: rgba(0, 0, 0) !important; // Your desired color for disabled text
  }
}

.ant-input[disabled] {
  color: rgba(
    0,
    0,
    0
  ) !important; // Adjust the color to your desired disabled text color
}

.ant-input-number-disabled {
  .ant-input-number-input {
    color: rgba(
      0,
      0,
      0
    ) !important; // Adjust the color to your desired disabled text color
  }
}

.ant-picker-disabled {
  .ant-picker-input > input {
    color: rgba(
      0,
      0,
      0
    ) !important; // Adjust the color to your desired disabled text color
  }
}

.redButtonBg {
  background-image: linear-gradient(
    97.69deg,
    #fc0000 20%,
    #f73b3b 100%
  ) !important;
  box-shadow: none;
  border: 0px solid;
  // transition: 0.6s;
}

.redButtonBg:hover {
  background-image: none !important;
  background-color: transparent;
  border: 0px solid;
  color: #f72828;
}

.ant-dropdown-menu-item-active {
  background-color: #5151e3 !important;
  /* Make sure to use !important to override Ant Design's default styles */
  color: white !important;
  /* Optional: You can change the text color for better visibility */
}

//// DraggableCarousel ////
.category-carousel .slick-dots {
  bottom: -10px;
  /* You can adjust the value to move the dots down */
}

.category-carousel .slick-slide slick-active {
  width: 0% !important;
}

.category-carousel .slick-dots li button {
  background-color: black !important;
}

.category-carousel .slick-slide div {
  display: flex;
  justify-content: center;
}

//// PAGE BODY WIDTH ////
.page-width {
  width: 85%;
  /* Adjust the width percentage as needed */
  max-width: 100%;
  /* Set the maximum width to 100% */
  margin-left: auto;
  /* Center the element horizontally */
  margin-right: auto;
  /* Center the element horizontally */
}

@media screen and (max-width: 950px) {
  .page-width {
    width: 95%;
    /* Set the width to 100% for smaller screens */
  }
}

/// SELECT ///

.translation-select .ant-select-single {
  height: 36px !important;
}

.translation-select .ant-select-selector {
  background-color: white !important;
  /* Adjust the color to your desired text color */
  color: rgb(81 81 227 / var(--tw-bg-opacity)) !important;
  /* Adjust the background color to your desired color */
  border: solid 2px !important;
  border-color: rgb(81 81 227 / var(--tw-bg-opacity));
  font-weight: bold !important;
  min-width: 70px !important;
  max-width: 70px !important;
}

.translation-select .ant-select-selector .ant-select-selection-item {
  font-weight: bold !important;
  /* If the text is within a child element */
}

.translation-select .ant-select-arrow {
  color: rgb(81 81 227 / var(--tw-bg-opacity)) !important;
  /* Adjust the color to your desired arrow color */
  font-weight: bold !important;
}

.responsive-select .ant-select-selector {
  border-radius: 0%;
  border: 0px !important;
  padding: 0px !important;
  font-weight: lighter !important;
  height: 28px;
}

.responsive-select .ant-select-arrow {
  color: rgb(81 81 227 / var(--tw-bg-opacity)) !important;
  /* Adjust the color to your desired arrow color */
  font-weight: bold !important;
  font-size: 13px !important;
}

.ant-picker-outlined:hover {
  border-color: $buttonOrange !important;
}

.ant-select-selector:hover {
  border-color: $buttonOrange !important;
}

.ant-select-selector:focus {
  border-color: $buttonOrange !important;
}

/// CHECKBOX ///

.ant-checkbox {
  border-color: $buttonOrange !important;
  color: $buttonOrange !important;
  // background-color: $buttonOrange !important;

  &.ant-checkbox-checked .ant-checkbox-inner {
    background-color: $buttonOrange !important;
  }
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner {
  border-color: $buttonOrange !important;
}

.ant-checkbox-input:focus {
  background-color: $buttonOrange !important;
}

.ant-btn-default:hover {
  border-color: $buttonOrange !important;
}

.ant-input-number-outlined:hover {
  border-color: $buttonOrange !important;
}

.ant-checkbox-input {
  border-color: $buttonOrange !important;
  color: $buttonOrange !important;
  background-color: $buttonOrange !important;
}

//// POPOVER ////

.ant-menu-light.ant-menu-root.ant-menu-vertical {
  border-inline-end: none !important;
}

//// Loader ////

.loader {
  background: white;
  // background: radial-gradient(#222, #000);
  bottom: 0;
  left: 0;
  overflow: hidden;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 99999;
}

.loader-inner {
  bottom: 0;
  height: 60px;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
  width: 100px;
}

.loader-line-wrap {
  animation: spin 1000ms linear infinite;
  box-sizing: border-box;
  height: 50px;
  left: 0;
  overflow: hidden;
  position: absolute;
  top: 0;
  transform-origin: 50% 100%;
  width: 100px;
}

.loader-line {
  border: 4px solid transparent;
  border-radius: 100%;
  box-sizing: border-box;
  height: 100px;
  left: 0;
  margin: 0 auto;
  position: absolute;
  right: 0;
  top: 0;
  width: 100px;
}

.loader-line-wrap:nth-child(1) {
  animation-delay: -50ms;
}

.loader-line-wrap:nth-child(1) .loader-line {
  border-color: #5151e3;
  height: 90px;
  width: 90px;
  top: 7px;
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

/// FONT SIZE ///
.textDescription {
  font-size: 12px;
}

.textLabel {
  font-size: 14px;
}

.textNumber {
  font-size: 16px;
}

.textSubTitle {
  font-size: 18px;
}

.textTitle {
  font-size: 20px;
}

.textProductName {
  font-size: 22px;
}

.textPriceNumber {
  font-size: 26px;
}

.image-gallery-icon {
  color: #fff;
  transition: all 0.3s ease-out;
  appearance: none;
  background-color: transparent;
  border: 0;
  cursor: pointer;
  outline: none;
  position: absolute;
  z-index: 4;
  filter: drop-shadow(0 1px 1px #505050);
}

@media (hover: hover) and (pointer: fine) {
  .image-gallery-icon:hover {
    color: #fea654;
  }

  .image-gallery-icon:hover .image-gallery-svg {
    transform: scale(1.1);
  }
}

.image-gallery-icon:focus {
  outline: 2px solid #fea654;
}

.image-gallery-using-mouse .image-gallery-icon:focus {
  outline: none;
}

.image-gallery-fullscreen-button,
.image-gallery-play-button {
  bottom: 0;
  padding: 20px;
}

.image-gallery-fullscreen-button .image-gallery-svg,
.image-gallery-play-button .image-gallery-svg {
  height: 28px;
  width: 28px;
}

@media (max-width: 820px) {
  .image-gallery-fullscreen-button,
  .image-gallery-play-button {
    padding: 15px;
  }

  .image-gallery-fullscreen-button .image-gallery-svg,
  .image-gallery-play-button .image-gallery-svg {
    height: 24px;
    width: 24px;
  }
}

@media (max-width: 480px) {
  .image-gallery-fullscreen-button,
  .image-gallery-play-button {
    padding: 10px;
  }

  .image-gallery-fullscreen-button .image-gallery-svg,
  .image-gallery-play-button .image-gallery-svg {
    height: 16px;
    width: 16px;
  }
}

.image-gallery-fullscreen-button {
  right: 0;
}

.image-gallery-play-button {
  left: 0;
}

.image-gallery-left-nav,
.image-gallery-right-nav {
  padding: 50px 10px;
  top: 50%;
  transform: translateY(-50%);
}

.image-gallery-left-nav .image-gallery-svg,
.image-gallery-right-nav .image-gallery-svg {
  height: 60px;
  width: 40px;
}

@media (max-width: 820px) {
  .image-gallery-left-nav .image-gallery-svg,
  .image-gallery-right-nav .image-gallery-svg {
    height: 72px;
    width: 36px;
  }
}

@media (max-width: 480px) {
  .image-gallery-left-nav .image-gallery-svg,
  .image-gallery-right-nav .image-gallery-svg {
    height: 48px;
    width: 24px;
  }
}

.image-gallery-left-nav[disabled],
.image-gallery-right-nav[disabled] {
  cursor: disabled;
  opacity: 0.6;
  pointer-events: none;
}

.image-gallery-left-nav {
  left: 0;
}

.image-gallery-right-nav {
  right: 0;
}

.image-gallery {
  width: 100%;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  position: relative;
}

.image-gallery.fullscreen-modal {
  background: #000;
  bottom: 0;
  height: 100%;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  width: 100%;
  z-index: 5;
}

.image-gallery.fullscreen-modal .image-gallery-content {
  top: 50%;
  transform: translateY(-50%);
}

.image-gallery-content {
  position: relative;
  line-height: 0;
  top: 0;
}

.image-gallery-content.fullscreen {
  background: #000;
}

.image-gallery-content .image-gallery-slide .image-gallery-image {
  max-width: 450px;
}

.image-gallery-content.image-gallery-thumbnails-left
  .image-gallery-slide
  .image-gallery-image,
.image-gallery-content.image-gallery-thumbnails-right
  .image-gallery-slide
  .image-gallery-image {
  max-width: 450px;
}

@media (max-width: 820px) {
  .image-gallery-content .image-gallery-slide .image-gallery-image {
    max-width: 300px;
  }

  .image-gallery-content.image-gallery-thumbnails-left
    .image-gallery-slide
    .image-gallery-image,
  .image-gallery-content.image-gallery-thumbnails-right
    .image-gallery-slide
    .image-gallery-image {
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .image-gallery-content .image-gallery-slide .image-gallery-image {
    max-width: 200px;
  }

  .image-gallery-content.image-gallery-thumbnails-left
    .image-gallery-slide
    .image-gallery-image,
  .image-gallery-content.image-gallery-thumbnails-right
    .image-gallery-slide
    .image-gallery-image {
    max-width: 200px;
  }
}

@media (max-width: 360px) {
  .image-gallery-content .image-gallery-slide .image-gallery-image {
    max-width: 150px;
  }

  .image-gallery-content.image-gallery-thumbnails-left
    .image-gallery-slide
    .image-gallery-image,
  .image-gallery-content.image-gallery-thumbnails-right
    .image-gallery-slide
    .image-gallery-image {
    max-width: 150px;
  }

  .image-gallery-thumbnail .image-gallery-thumbnail-inner {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-gallery-thumbnail .image-gallery-thumbnail-image {
    vertical-align: middle;
    line-height: 0;
    max-height: 70px;
    object-fit: contain;
    background-color: white;
  }
}

.image-gallery-slide-wrapper {
  background-color: white;
  position: relative;
  text-align: center;
}

.image-gallery-slide-wrapper.image-gallery-thumbnails-left,
.image-gallery-slide-wrapper.image-gallery-thumbnails-right {
  display: inline-block;
  width: calc(100% - 110px);
}

@media (max-width: 820px) {
  .image-gallery-slide-wrapper.image-gallery-thumbnails-left,
  .image-gallery-slide-wrapper.image-gallery-thumbnails-right {
    width: calc(100% - 87px);
  }
}

.image-gallery-slide-wrapper.image-gallery-rtl {
  direction: rtl;
}

.image-gallery-slides {
  line-height: 0;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
  text-align: center;
}

.image-gallery-slide {
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}

.image-gallery-slide.image-gallery-center {
  position: relative;
  justify-content: center;
  display: flex !important;
  background-color: w;
}

.image-gallery-slide .image-gallery-image {
  width: 100%;
  object-fit: contain;
}

.image-gallery-slide .image-gallery-description {
  background: rgba(0, 0, 0, 0.4);
  bottom: 70px;
  color: #fff;
  left: 0;
  line-height: 1;
  padding: 10px 20px;
  position: absolute;
  white-space: normal;
}

@media (max-width: 820px) {
  .image-gallery-slide .image-gallery-description {
    bottom: 45px;
    font-size: 0.8em;
    padding: 8px 15px;
  }
}

.image-gallery-bullets {
  bottom: 20px;
  left: 0;
  margin: 0 auto;
  position: absolute;
  right: 0;
  width: 80%;
  z-index: 4;
}

.image-gallery-bullets .image-gallery-bullets-container {
  margin: 0;
  padding: 0;
  text-align: center;
}

.image-gallery-bullets .image-gallery-bullet {
  appearance: none;
  background-color: transparent;
  border: 1px solid #fff;
  border-radius: 50%;
  box-shadow: 0 2px 2px #1a1a1a;
  cursor: pointer;
  display: inline-block;
  margin: 0 5px;
  outline: none;
  padding: 5px;
  transition: all 0.2s ease-out;
}

@media (max-width: 820px) {
  .image-gallery-bullets .image-gallery-bullet {
    margin: 0 3px;
    padding: 3px;
  }
}

@media (max-width: 480px) {
  .image-gallery-bullets .image-gallery-bullet {
    padding: 2.7px;
  }
}

.image-gallery-bullets .image-gallery-bullet:focus {
  transform: scale(1.2);
  background: #fea654;
  border: 1px solid #fea654;
}

.image-gallery-bullets .image-gallery-bullet.active {
  transform: scale(1.2);
  border: 1px solid #fff;
  background: #fff;
}

@media (hover: hover) and (pointer: fine) {
  .image-gallery-bullets .image-gallery-bullet:hover {
    background: #fea654;
    border: 1px solid #fea654;
  }

  .image-gallery-bullets .image-gallery-bullet.active:hover {
    background: #fea654;
  }
}

.image-gallery-thumbnails-wrapper {
  position: relative;
}

.image-gallery-thumbnails-wrapper.thumbnails-swipe-horizontal {
  touch-action: pan-y;
}

.image-gallery-thumbnails-wrapper.thumbnails-swipe-vertical {
  touch-action: pan-x;
}

.image-gallery-thumbnails-wrapper.thumbnails-wrapper-rtl {
  direction: rtl;
}

.image-gallery-thumbnails-wrapper.image-gallery-thumbnails-left,
.image-gallery-thumbnails-wrapper.image-gallery-thumbnails-right {
  display: inline-block;
  vertical-align: top;
  width: 100px;
}

@media (max-width: 820px) {
  .image-gallery-thumbnails-wrapper.image-gallery-thumbnails-left,
  .image-gallery-thumbnails-wrapper.image-gallery-thumbnails-right {
    width: 81px;
  }
}

.image-gallery-thumbnails-wrapper.image-gallery-thumbnails-left
  .image-gallery-thumbnails,
.image-gallery-thumbnails-wrapper.image-gallery-thumbnails-right
  .image-gallery-thumbnails {
  height: 100%;
  width: 100%;
  left: 0;
  padding: 0;
  position: absolute;
  top: 0;
}

.image-gallery-thumbnails-wrapper.image-gallery-thumbnails-left
  .image-gallery-thumbnails
  .image-gallery-thumbnail,
.image-gallery-thumbnails-wrapper.image-gallery-thumbnails-right
  .image-gallery-thumbnails
  .image-gallery-thumbnail {
  display: block;
  margin-right: 0;
  padding: 0;
}

.image-gallery-thumbnails-wrapper.image-gallery-thumbnails-left
  .image-gallery-thumbnails
  .image-gallery-thumbnail
  + .image-gallery-thumbnail,
.image-gallery-thumbnails-wrapper.image-gallery-thumbnails-right
  .image-gallery-thumbnails
  .image-gallery-thumbnail
  + .image-gallery-thumbnail {
  margin-left: 0;
  margin-top: 2px;
}

.image-gallery-thumbnails-wrapper.image-gallery-thumbnails-left,
.image-gallery-thumbnails-wrapper.image-gallery-thumbnails-right {
  margin: 0 5px;
}

@media (max-width: 820px) {
  .image-gallery-thumbnails-wrapper.image-gallery-thumbnails-left,
  .image-gallery-thumbnails-wrapper.image-gallery-thumbnails-right {
    margin: 0 3px;
  }
}

.image-gallery-thumbnails {
  overflow: hidden;
  padding: 5px 0;
}

@media (max-width: 820px) {
  .image-gallery-thumbnails {
    padding: 3px 0;
  }
}

.image-gallery-thumbnails .image-gallery-thumbnails-container {
  cursor: pointer;
  text-align: center;
  white-space: nowrap;
}

.image-gallery-thumbnail {
  display: inline-block;
  border: 4px solid transparent;
  transition: border 0.3s ease-out;
  width: 100px;
  background: transparent;
  padding: 0;
}

@media (max-width: 820px) {
  .image-gallery-thumbnail {
    border: 3px solid transparent;
    width: 81px;
  }
}

.image-gallery-thumbnail + .image-gallery-thumbnail {
  margin-left: 2px;
}

.image-gallery-thumbnail .image-gallery-thumbnail-inner {
  display: block;
  position: relative;
}

.image-gallery-thumbnail .image-gallery-thumbnail-image {
  vertical-align: middle;
  width: 100%;
  line-height: 0;
}

.image-gallery-thumbnail.active,
.image-gallery-thumbnail:focus {
  outline: none;
  border: 2px solid #fea654;
}

@media (max-width: 820px) {
  .image-gallery-thumbnail.active,
  .image-gallery-thumbnail:focus {
    border: 3px solid #fea654;
  }
}

@media (hover: hover) and (pointer: fine) {
  .image-gallery-thumbnail:hover {
    outline: none;
    border: 2px solid #fea654;
  }
}

@media (hover: hover) and (pointer: fine) and (max-width: 820px) {
  .image-gallery-thumbnail:hover {
    border: 3px solid #fea654;
  }
}

.image-gallery-thumbnail-label {
  box-sizing: border-box;
  color: #fff;
  font-size: 1em;
  left: 0;
  line-height: 1em;
  padding: 5%;
  position: absolute;
  top: 50%;
  text-shadow: 0 2px 2px #1a1a1a;
  transform: translateY(-50%);
  white-space: normal;
  width: 100%;
}

@media (max-width: 820px) {
  .image-gallery-thumbnail-label {
    font-size: 0.8em;
    line-height: 0.8em;
  }
}

.image-gallery-index {
  background: rgba(0, 0, 0, 0.4);
  color: #fff;
  line-height: 1;
  padding: 10px 20px;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 4;
}

@media (max-width: 820px) {
  .image-gallery-index {
    font-size: 0.8em;
    padding: 5px 10px;
  }
}

.search-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 16px;
  border: 1px solid $lightPurple;
  border-radius: 12px;
  font-size: 12px;
  line-height: 20px;
  height: 36px;
  min-width: 55px;
}

.searchInput {
  width: 250px;
}

.searchInput
  .ant-input-search
  .ant-input-group
  .ant-input-affix-wrapper:not(:last-child) {
  border-radius: 12px 0 0 12px !important;
}

.ant-float-btn-primary {
  background-color: #5151e3;
}

:where(.css-dev-only-do-not-override-hozz3u).ant-float-btn-primary
  .ant-float-btn-body {
  background-color: #5151e3;
  transition: background-color 0.2s;
}

.excelButtonBg {
  background-image: linear-gradient(
    97.69deg,
    #2c9c44 100%,
    #2c9c44 100%
  ) !important;
  // background-image: linear-gradient(97.69deg, #00ff00 0%, #2c9c44 100%) !important;
  box-shadow: none;
  border: none;
  // transition: 0.6s;
  // transition: all 5ms ease-in;
}

.excelButtonBg:hover {
  background-image: none !important;
  background-color: transparent;
  border: 0px solid #00ff00;
  color: #00ff00;
  transition: all 0.5s ease-in;
}

.excelButtonBg:disabled {
  background-image: linear-gradient(
    97.69deg,
    #a3d4a8 100%,
    #a3d4a8 100%
  ) !important;
  /* Lighter green shade */
  color: #bfbfbf;
  /* Light gray text color */
  cursor: not-allowed;
  /* Show not-allowed cursor */
  opacity: 0.6;
  /* Optional: to show it's inactive */
}

.disabledExcelButtonBg {
  background-image: linear-gradient(
    97.69deg,
    rgba(6, 64, 6, 0.394) 0%,
    rgba(36, 213, 36, 0.37) 00%
  ) !important;
  border-color: white;
}

.ant-btn-primary:not(:disabled):not(.ant-btn-disabled):hover {
  background: #5151e3;
}

.ant-btn-primary {
  transition: background-color 0s ease-in-out, transform 0.2s ease-in-out !important;
}

.ant-btn-primary:hover {
  background-color: #5151e3 !important; /* Your hover color */
  transform: scale(1.02); /* Slight scaling effect */
  box-shadow: 0px 4px 10px rgba(81, 81, 227, 0.3); /* Custom shadow */
}

@media (max-width: 500px) {
  .mobile-table .ant-table-thead {
    display: none;
  }
}

.ant-steps.ant-steps-label-vertical .ant-steps-item-icon {
  margin-inline-start: 45px;
}

.ant-steps.ant-steps-label-vertical .ant-steps-item-tail {
  margin-inline-start: 75px;
  padding: 16px 30px;
  overflow-x: hidden;
}

.ant-steps.ant-steps-label-vertical
  .ant-steps-item-finish
  .ant-steps-item-tail::after {
  height: 4px;
  background-color: #5151e3 !important;
}

.ant-steps.ant-steps-label-vertical .ant-steps-item-tail::after {
  height: 4px;
  background-color: #e0e0e0 !important;
}

.ant-steps.ant-steps-label-vertical .ant-steps-item-content {
  display: block;
  width: 150px;
  margin-top: 6px;
  text-align: center;
  // margin-left: 12px;
}

@media (max-width: 550px) {
  .ant-steps-item-content {
    padding-left: 18px;
  }
  // .ant-steps.ant-steps-vertical
  //   > .ant-steps-item:not(:last-child)
  //   > .ant-steps-item-container
  //   > .ant-steps-item-tail {
  //   display: none;
  // }
  .ant-steps.ant-steps-vertical > .ant-steps-item .ant-steps-item-title {
    line-height: 16px;
  }
  .ant-steps
    .ant-steps-item-finish
    .ant-steps-item-icon
    > .ant-steps-icon
    .ant-steps-icon-dot {
    background: #5151e3;
  }
}
