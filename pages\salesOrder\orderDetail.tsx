import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import defaultImage from "../../assets/default/emptyImage.png";
import { isValidPhoneNumber } from "react-phone-number-input";
import {
  EnvironmentFilled,
  LeftOutlined,
  RightOutlined,
} from "@ant-design/icons";
import { Form, Col, Row, Collapse } from "antd";
import { constructAddress, TableUI } from "@/components/ui";
import { BackButtonUI, IconDescriptionButtonUI } from "@/components/buttonUI";
import { FormTextInput, SingleDateInput } from "@/components/input";
import {
  CompanyGeneralInfo,
  Outlet,
  OutletShippingAddress,
  Product,
  ProductOrdered,
  ProductUOM,
  SalesOrder,
  UOM,
} from "@/components/type";
import {
  DataSource,
  NumberThousandSeparator,
  PUBLIC_BUCKET_URL,
  encodeParams,
  formateDate,
} from "@/stores/utilize";
import Header, { supportedLocales } from "@/components/header";
import AppFooter from "@/components/footer";
import _ from "lodash";

function Checkout() {
  const router = useRouter();
  const { t } = useTranslation("common");
  const [form] = Form.useForm();

  const [data, setData] = useState<SalesOrder[]>([]);
  const [productData, setProductData] = useState<any[]>([]);
  const [estimatedDeliveredDate, setEstimatedDeliveredDate] = useState("");
  const [outletMap, setOutletMap] = useState(new Map());
  const [companyMap, setCompanyMap] = useState(new Map());
  const [productMap, setProductMap] = useState(new Map());
  const [shippingAddressMap, setShippingAddressMap] = useState(new Map());
  const [uomMap, setUomMap] = useState(new Map());
  const [selectedAddress, setSelectedAddress] = useState<OutletShippingAddress>(
    {}
  );

  const headerItems = [
    {
      label: t("Header.dashboard"),
      route: "/profile/dashboard",
      className: "labelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("SalesOrder.salesOrder"),
      route: "/salesOrder/salesOrderListing",
      className: "labelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("SalesOrder.orderDetail"),
      route: "/salesOrder/SalesOrderListing",
      className: "labelTextStyle",
    },
  ];

  useEffect(() => {
    if (router.isReady) {
      getSalesOrder();
    }
  }, [router.isReady]);

  const getSalesOrder = (
    isClearFilter = false,
    otherViewBranchAccess = false
  ) => {
    // const accessBranches = otherViewBranchAccess === false ? retailerAccess.companyBranchIds : otherCompanyBranchesIds;

    setTimeout(() => {
      let params: any = {
        sort: "createdAt",
        sortOrder: "-1",
        id: router.query.id,
        includeOriginals: "TRUE",
      };

      // if (!isAdmin) {
      //   params.companyBranchIds = accessBranches;
      // }
      // const checkAdminRights = isAdmin ? params : params + "&companyId=" + retailerAccess.companyId;
      // const checkFilterRights = filterSetting && !isClearFilter ? filterSetting : encodeParams(params);
      const dataSource = new DataSource(
        "salesOrders",
        encodeParams(params),
        false
      );

      // !isAdmin && !otherViewBranchAccess ? filterForm.setFieldValue("companyId", retailerAccess.companyId) : null;

      // if ((accessBranches && accessBranches.length > 0) || isAdmin) {
      dataSource
        .load()
        .then((res: any) => {
          if (res && res.items !== null) {
            let data = res.items;
            const objectMap = data.reduce(
              (accumulator: any, current: SalesOrder) => {
                accumulator["outletId"] = accumulator["outletId"] || [];
                if (
                  current.outletId &&
                  !outletMap.has(current.outletId) &&
                  !accumulator["outletId"].includes(current.outletId)
                ) {
                  accumulator["outletId"].push(current.outletId ?? "");
                }

                accumulator["companyId"] = accumulator["companyId"] || [];
                if (
                  current.companyId &&
                  !companyMap.has(current.companyId) &&
                  !accumulator["companyId"].includes(current.companyId)
                ) {
                  accumulator["companyId"].push(current.companyId ?? "");
                }

                accumulator["shippingAddressId"] =
                  accumulator["shippingAddressId"] || [];
                if (
                  current.shippingAddressId &&
                  !shippingAddressMap.has(current.shippingAddressId) &&
                  !accumulator["shippingAddressId"].includes(
                    current.shippingAddressId
                  )
                ) {
                  accumulator["shippingAddressId"].push(
                    current.shippingAddressId ?? ""
                  );
                }

                current.productOrdered?.reduce(
                  (acc: any, product: ProductOrdered) => {
                    accumulator["productId"] = accumulator["productId"] || [];
                    if (
                      product.productId &&
                      !productMap.has(product.productId) &&
                      !accumulator["productId"].includes(product.productId)
                    ) {
                      accumulator["productId"].push(product.productId ?? "");
                    }

                    accumulator["productUOMId"] =
                      accumulator["productUOMId"] || [];
                    if (
                      product.productUOMId &&
                      !uomMap.has(product.productUOMId) &&
                      !accumulator["productUOMId"].includes(
                        product.productUOMId
                      )
                    ) {
                      accumulator["productUOMId"].push(
                        product.productUOMId ?? ""
                      );
                    }

                    return acc;
                  },
                  {}
                );

                current?.originalSalesOrder?.productOrdered?.reduce(
                  (acc: any, product: ProductOrdered) => {
                    accumulator["productId"] = accumulator["productId"] || [];
                    if (
                      product.productId &&
                      !productMap.has(product.productId) &&
                      !accumulator["productId"].includes(product.productId)
                    ) {
                      accumulator["productId"].push(product.productId ?? "");
                    }

                    accumulator["productUOMId"] =
                      accumulator["productUOMId"] || [];
                    if (
                      product.productUOMId &&
                      !uomMap.has(product.productUOMId) &&
                      !accumulator["productUOMId"].includes(
                        product.productUOMId
                      )
                    ) {
                      accumulator["productUOMId"].push(
                        product.productUOMId ?? ""
                      );
                    }

                    return acc;
                  },
                  {}
                );

                return accumulator;
              },
              {}
            );

            const addKeyData = data?.map((item: any, index: number) => {
              // to check whether got value in alternativeShippingAddress
              const manualAddress = _.cloneDeep(
                data?.alternativeShippingAddress
              );
              if (manualAddress && !isEmptyObject(manualAddress)) {
                data.manualAddress = constructAddress(
                  data.alternativeShippingAddress
                );
              } else {
                data.manualAddress = "";
              }
              return {
                ...item,
                key: (index + 1).toString(),
              };
            });

            getCompany(objectMap["companyId"]);
            getOutlets(objectMap["outletId"]);
            getProduct(objectMap["productId"]);
            getUOM(objectMap["productUOMId"]);
            getShippingAddress(objectMap["shippingAddressId"]);
            // getCompanyBranch(objectMap["companyBranchId"]);
            setData(addKeyData);

            let currentProduct = data[0].productOrdered;
            let oriProduct = data[0].originalSalesOrder?.productOrdered || [];
            if (oriProduct.length > currentProduct.length) {
              const filteredProducts = oriProduct
                .filter(
                  (op: any) =>
                    !currentProduct.some(
                      (cp: any) =>
                        cp.productId === op.productId &&
                        cp.productUOMId === op.productUOMId
                    )
                )
                .map((product: ProductOrdered[]) => ({
                  ...product,
                  quantity: 0,
                  discount: 0,
                  total: 0,
                }));
              currentProduct = [...currentProduct, ...filteredProducts];
            }
            setProductData(currentProduct);
            setEstimatedDeliveredDate(
              formateDate(data[0].estimatedDeliveredAt)
            );
            form.setFieldValue("remark", data[0].remarks);
          }
        })
        .catch(() => {
          //* This Part need re-edit*//
        });
      // } else {
      //   setGoodsReturnListingInfoData([]);
      // }
    }, 500);
  };

  const getCompany = async (id: string[] = []) => {
    const dataSource = new DataSource(
      "companies",
      encodeParams({ id: id }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setCompanyMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: CompanyGeneralInfo) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getOutlets = async (id: string[] = []) => {
    let tempProductMap = new Map(outletMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "outlets",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });
        if (res !== null && res.items.length > 0) {
          setOutletMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Outlet) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getProduct = async (id: string[] = []) => {
    let tempProductMap = new Map(productMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "productCatalogues",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setProductMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Product) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Product) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getUOM = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("uoms", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setUomMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: UOM) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: UOM) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getShippingAddress = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "shippingAddresses",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          const temp = res.items[0];
          temp.description = constructAddress(temp);
          setSelectedAddress(temp);
          setShippingAddressMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: OutletShippingAddress) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: OutletShippingAddress) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  // check if data is empty
  function isEmptyObject(obj: any): boolean {
    // Check if an object is empty or contains only empty nested objects/arrays
    return (
      Object.keys(obj).length === 0 ||
      Object.values(obj).every((value) => {
        if (value && value !== "0001-01-01T00:00:00Z") {
          return false;
        }
        return true;
      })
    );
  }

  const totalPriceOfMyCart = data.reduce((total: number, item: SalesOrder) => {
    const totalPrice =
      (item.subTotal ?? 0) -
      (item.subTotalDiscount ?? 0) +
      (item.totalTax ?? 0) +
      (item.shippingFee ?? 0);
    total += totalPrice ?? 0;
    return total;
  }, 0);

  const column = [
    {
      title: t("SalesOrder.product"),
      dataIndex: "productId",
      sorter: (a: any, b: any) => a.productId.localeCompare(b.productId),
      showSorterTooltip: false,
      key: "id",
      render: (_: any, record: ProductOrdered) => {
        const item = productMap.get(record.productId);
        if (item) {
          return (
            <div>
              <Col className="flex items-center w-full tableRowNameDesign">
                <img
                  className="object-contain h-[80px] min-w-[80px] p-2 "
                  src={
                    item.productUOM.find(
                      (item: ProductUOM) =>
                        record.productUOMId === item.productUOMId
                    )?.pictures
                      ? PUBLIC_BUCKET_URL +
                      item.productUOM.find(
                        (item: ProductUOM) =>
                          record.productUOMId === item.productUOMId
                      )?.pictures[1]
                      : defaultImage.src
                  }
                  loading="lazy"
                ></img>
                <div className="flex flex-col w-full">
                  <p className="font-bold text-[14px]">{item.name}&nbsp;</p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("SalesOrder.productCode")}: {item.sku}
                    </span>
                  </p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("SalesOrder.uom")}:{" "}
                      {uomMap.get(record.productUOMId)?.name}
                    </span>
                  </p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("SalesOrder.unitPrice")}:{" "}
                      {NumberThousandSeparator(record.price ?? 0)}
                    </span>
                  </p>
                </div>
              </Col>
            </div>
          );
        } else return null;
      },
    },
    {
      title: t("SalesOrder.unitPrice"),
      dataIndex: "unitPrice",
      key: "unitPrice",
      render: (_: any, record: ProductOrdered) => {
        return (
          <p className="tableRowNameDesign flex justify-end">
            {"RM " + NumberThousandSeparator(record.price ?? 0)}
          </p>
        );
      },
    },
    {
      title: t("SalesOrder.quantity"),
      dataIndex: "quantity",
      key: "quantity",
      render: (_: any, record: ProductOrdered) => {
        return <p className="tableRowNameDesign">{record.quantity}</p>;
      },
    },
    {
      title: t("SalesOrder.uom"),
      dataIndex: "uom",
      key: "uom",
      render: (_: any, record: ProductOrdered) => {
        return (
          <p className="tableRowNameDesign">
            {uomMap.get(record.productUOMId)?.name}
          </p>
        );
      },
    },
    {
      title: t("SalesOrder.discount"),
      dataIndex: "discount",
      key: "discount",
      render: (_: any, record: ProductOrdered) => {
        return (
          <p className="tableRowNameDesign flex justify-end">
            {"RM " + NumberThousandSeparator(record.discount ?? 0)}
          </p>
        );
      },
    },
    {
      title: t("SalesOrder.tax"),
      dataIndex: "tax",
      key: "tax",
      render: (_: any, record: ProductOrdered) => {
        const total =
          (record.price ?? 0) * (record.quantity ?? 0) * (record.taxRate ?? 0);
        return (
          <p className="tableRowNameDesign flex justify-end">
            {"RM " + NumberThousandSeparator(total)}
          </p>
        );
      },
    },
    {
      title: t("SalesOrder.itemSubtotal"),
      dataIndex: "subtotal",
      key: "subtotal",
      render: (_: any, record: ProductOrdered) => {
        const tax =
          (record.price ?? 0) * (record.quantity ?? 0) * (record.taxRate ?? 0);

        const subtotal =
          (record.price ?? 0) * (record.quantity ?? 0) +
          tax -
          (record.discount ?? 0);
        return (
          <p className="tableRowNameDesign text-right font-bold">
            {"RM " + NumberThousandSeparator(subtotal)}
          </p>
        );
      },
    },
  ];

  const showContent = () => {
    return (
      <div>
        <BackButtonUI
          title={t("SalesOrder.orderDetail")}
          buttons={[]}
        ></BackButtonUI>

        <Row className="mt-2 mb-5">
          <IconDescriptionButtonUI
            label={
              data && data[0]?.manualAddress
                ? "Customer CPO Address"
                : selectedAddress && selectedAddress.shippingAddressDescription
                  ? selectedAddress.shippingAddressDescription
                  : "No Address Record"
            }
            // onClick={() => setSelectAddressVisible(true)}
            icon={<EnvironmentFilled />}
            buttonIcon={<RightOutlined />}
            description={
              data && data[0]?.manualAddress
                ? data[0]?.manualAddress
                : selectedAddress && selectedAddress.description
                  ? selectedAddress.description
                  : ""
            }
          />
          {/* <ModalUI
            className="font-bold"
            title={t("Checkout.deliveryAddress")}
            visible={isSelectAddressVisible}
            onCancel={() => setSelectAddressVisible(false)}
            width="60%"
          /> */}
        </Row>
        <Row>
          {/* <Collapse defaultActiveKey={[data?.length ? data[0].id || '' : '']} className="w-full"> */}
          <Collapse defaultActiveKey={["1"]} className="w-full">
            {data.length > 0 ? (
              <Collapse.Panel
                header={
                  <p className="text-textBlue font-bold">
                    {companyMap.get(data[0].companyId)?.name}
                  </p>
                }
                key={data[0]?.key || "1"}
              >
                <div className="w-full ">
                  {/* order items */}
                  <div className="px-4">
                    <p className="mb-4 font-bold text-[20px]">
                      {t("SalesOrder.orderedItems")}
                    </p>
                    <TableUI
                      bordered
                      dataSource={productData}
                      columns={column}
                    />
                    <div className="flex flex-col w-full bg-lightGrey p-3">
                      {/* remark form */}
                      <Row className="justify-between pb-5 ">
                        <Form
                          className="w-full "
                          form={form}
                          layout="horizontal"
                          scrollToFirstError
                        >
                          <Row className="w-full  ">
                            <Row className="w-full flex gap-x-4 ">
                              <Form.Item
                                name={"remark"}
                                className="flex-1"
                                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                                  <p className="text-neutral700 text-[12px]">
                                    {t("SalesOrder.remark")}
                                  </p>
                                }
                              >
                                <FormTextInput
                                  disabled
                                  maxLength={100}
                                  placeholder={""}
                                />
                              </Form.Item>
                              <Col className="w-2/6">
                                <Form.Item
                                  name={"returnDate"}
                                  className="flex-1 "
                                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/
                                >
                                  <SingleDateInput
                                    disabled
                                    placeholder={`Estimate delivery: Received by ${estimatedDeliveredDate}`}
                                    onChange={() => { }}
                                  />
                                </Form.Item>
                              </Col>
                              <Col className="flex items-center">
                                <p className="font-bold text-[16px] pr-2 ">
                                  {"RM " +
                                    NumberThousandSeparator(
                                      data[0].subTotal ?? 0
                                    )}
                                </p>
                              </Col>
                            </Row>
                          </Row>
                        </Form>
                      </Row>
                      {/* order detail */}
                      <div>
                        <p className="font-bold mb-2 text-[16px]">
                          {t("SalesOrder.orderDetail")}
                        </p>
                        <Row className="flex justify-between">
                          <p>{t("SalesOrder.salesOrderNo")}</p>
                          <p className="">{data[0].salesOrderNo}</p>
                        </Row>
                        <Row className="flex justify-between">
                          <p>{t("SalesOrder.subTotal")}</p>
                          <p className="">
                            {"RM " +
                              NumberThousandSeparator(data[0].subTotal ?? 0)}
                          </p>
                        </Row>
                        <Row className="flex justify-between">
                          <p>{t("SalesOrder.totalDiscount")}</p>
                          <p className="discount-color">
                            {"- RM " +
                              NumberThousandSeparator(
                                data[0].subTotalDiscount ?? 0
                              )}
                          </p>
                        </Row>
                        <Row className="flex justify-between">
                          <p>{t("SalesOrder.shippingFee")}</p>
                          <p className="">
                            {"RM " +
                              NumberThousandSeparator(data[0].shippingFee ?? 0)}
                          </p>
                        </Row>
                        <Row className="flex justify-between">
                          <p>{t("SalesOrder.totalTax")}</p>
                          <p className="">
                            {"RM " +
                              NumberThousandSeparator(data[0].totalTax ?? 0)}
                          </p>
                        </Row>
                        <div className="semiBold-horizontal-divider"> </div>
                        <Row className="flex justify-between">
                          <p className="font-bold text-[16px]">
                            {t("SalesOrder.totalAmount")}
                          </p>
                          <p className="font-bold text-[16px]">
                            {"RM " +
                              NumberThousandSeparator(totalPriceOfMyCart)}
                          </p>
                        </Row>
                      </div>
                    </div>
                  </div>
                </div>
              </Collapse.Panel>
            ) : null}
          </Collapse>
        </Row>
      </div>
    );
  };

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => { }} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
      </Content>
      <AppFooter retailerAccessValues={{}} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default Checkout;
