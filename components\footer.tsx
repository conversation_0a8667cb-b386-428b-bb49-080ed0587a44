import { Layout, Row, Col, FloatButton } from "antd";
import { CreditCardOutlined, CustomerServiceOutlined } from "@ant-design/icons";
import FpxIcon from "../assets/sampleImage/fpx-logo.jpg";
import { useEffect, useState } from "react";
import { Configurable<PERSON>ield, Retailer } from "./type";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import { encodeParams } from "@/stores/utilize";
import apiHelper from "@/pages/api/apiHelper";

const { Footer } = Layout;

interface PaymentMethod {
  name: string;
  imageSrc: string;
}

type AppFooterProps = {
  retailerAccessValues: Retailer; // You can replace `any` with a more specific type
};


const AppFooter = ({ retailerAccessValues }: AppFooterProps) => {
  const [retailerAccess, setRetailerAccess] = useState<any>({});
  const [supportContactLink, setSupportContactLink] = useState<string>("");
  const [supportFormLink, setSupportFormLink] = useState<string>("");

  useEffect(() => {
    if (!retailerAccessValues || Object.keys(retailerAccessValues).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }
    } else {
      setRetailerAccess(retailerAccessValues)
    }
  }, []);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0) {
      getConfigureSetting(retailerAccess?.companyId)
    }
  }, [retailerAccess]);

  // get configureSetting  
  const getConfigureSetting = async (companyId: string = "") => {
    let params: any = {
      companyId: companyId,
      name: `RetailerWebsiteSupportLink-${retailerAccess?.companyCode}`
    }

    const response: any = await apiHelper.GET(`configurableFields?${encodeParams(params)}`)
    if (response?.items?.length) {
      const items: ConfigurableField = response.items[0];
      if (items) {
        const [supportFormLink, supportContactLink] = items?.value?.split("|")

        if (supportFormLink) {
          setSupportFormLink(supportFormLink ?? "")
        }

        if (supportContactLink) {
          setSupportContactLink(supportContactLink ?? "")
        }
      }
    }
  }

  // Sample payment methods
  const paymentMethods: PaymentMethod[] = [
    { name: "Payment Method 1", imageSrc: FpxIcon.src },
    // { name: "Payment Method 2", imageSrc: PaymentMethodImage1.src },
    // { name: "Payment Method 3", imageSrc: PaymentMethodImage1.src },
    // { name: "Payment Method 4", imageSrc: PaymentMethodImage1.src },
    // { name: "Payment Method 5", imageSrc: PaymentMethodImage1.src },
    // { name: "Payment Method 6", imageSrc: PaymentMethodImage1.src },
    // { name: "Payment Method 7", imageSrc: PaymentMethodImage1.src },
    // { name: "Payment Method 8", imageSrc: PaymentMethodImage1.src },
    // Add more payment methods as needed
  ];

  return (
    <Footer className="bg-white p-4 shadow-xl mt-auto">
      <div className="w-full sm:w-4/5 sm:mx-auto ">
        <Row className="sm:pb-5">
          <Col className="md:w-1/2 xs:w-full flex-col font-bold">
            <div className="pb-2">
              <CreditCardOutlined className="mr-2" />
              <span className="text-sm">Payment Methods</span>
            </div>
            <div className="flex flex-wrap sm:pl-0 pl-2">
              {/* Display payment method icons */}
              {paymentMethods.map((method, index) => (
                <div key={index} className="w-1/5 mb-2 flex">
                  <img
                    src={method.imageSrc}
                    alt={method.name}
                    className="w-[50px] h-[30px] max-w-xs max-h-24 mx-auto"
                    style={{ margin: "2px 0px" }} // Adjust margin here
                  />
                </div>
              ))}
            </div>
          </Col>
          <Col className="flex-col flex md:w-1/2 xs:w-full">
            <div className="flex items-center font-bold pb-2">
              <CustomerServiceOutlined className="mr-2 text-sm pb-2 pt-1" />
              {/* <a
                onClick={() => {
                  window.open(
                    "https://forms.office.com/r/ssKmDGiQbY",
                    "_blank"
                  );
                }}
                className="hover:underline text-sm"
              > */}
              <a
                onClick={() => {
                  window.open(
                    `${supportFormLink}`,
                    "_blank"
                  );
                }}
                className="hover:underline text-sm"
              >
                Customer Service
              </a>
            </div>
            <div>
              <p className="text-xs sm:pl-0 pl-2">
                For assistance, FAQs, and to submit your request, visit our
                {/* <a href="https://forms.office.com/r/ssKmDGiQbY"> Support Form</a> */}
                <a onClick={() => window.open(
                  `${supportFormLink}`,
                  "_blank"
                )}> <u>Support Form</u></a>
              </p>
            </div>
            <div className="flex justify-end mt-2">
              {/* Add any additional customer service links if needed */}
            </div>
          </Col>
        </Row>
        <Col className="text-xs w-full pt-3 border-t-2 text-gray-400">
          <p>&copy; {new Date().getFullYear()} Zappit Solution Sdn. Bhd</p>
          <p className="text-xs">1242325D. All rights reserved.</p>
        </Col>
      </div>
      <FloatButton
        shape="circle"
        type="primary"
        onClick={() => {
          // window.open("https://wa.me/60122418233", "_blank");
          window.open(`${supportContactLink}`, "_blank");
        }}
        style={{ right: 25 }}
        icon={<CustomerServiceOutlined />}
      />
    </Footer>
  );
};

export default AppFooter;
