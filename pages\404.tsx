import router from "next/router";
import Logo from "../assets/error/404.svg";
import { PrimaryButtonUI } from "../components/buttonUI";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "react-i18next";
import { supportedLocales } from "@/components/header";

export default function custom404() {
  const { t } = useTranslation("common");

  return (
    <div className="flex flex-col justify-center items-center relative gap-8 h-screen  w-full ">
      <div className="relative">
        <Logo />
      </div>
      <div className="flex flex-col space-y-2">
        <p className="text-4xl font-bold text-center text-transparent bg-clip-text bg-gradient-to-br from-red-400 to-yellow-300">
          {t("ErrorPage.wrong")}
        </p>
        <p className="text-base text-center text-gray-500">
          {t("ErrorPage.sorry")}
        </p>
      </div>
      <div className="inline-flex space-x-2 items-center justify-center ">
        <PrimaryButtonUI
          onClick={() => router.push("/login")}
          label={t("Login.default")}
        />
      </div>
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}
