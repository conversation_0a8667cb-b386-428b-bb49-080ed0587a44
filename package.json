{"name": "yltc-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "^2.6.35", "@googlemaps/js-api-loader": "^1.16.2", "@types/file-saver": "^2.0.7", "@types/google.maps": "^3.54.10", "antd": "^5.11.5", "axios": "^1.6.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "file-saver": "^2.0.5", "google-map-react": "^2.2.1", "i18next": "^23.6.0", "libphonenumber-js": "^1.10.49", "lodash": "^4.17.21", "moment": "^2.29.4", "next": "^14.0.1", "next-i18next": "^15.0.0", "nprogress": "^0.2.0", "pdf-lib": "^1.17.1", "react": "^18", "react-dom": "^18", "react-i18next": "^13.3.1", "react-image-gallery": "^1.3.0", "react-infinite-scroll-component": "^6.1.0", "react-phone-number-input": "^3.3.7", "sass": "^1.69.5", "victory": "^36.6.12", "xlsx": "^0.18.5", "zustand": "^4.4.6"}, "devDependencies": {"@iconify/react": "^4.1.1", "@svgr/webpack": "^8.1.0", "@types/crypto-js": "^4.1.3", "@types/google-map-react": "^2.1.10", "@types/lodash": "^4.14.200", "@types/node": "^20", "@types/nprogress": "^0.2.2", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-image-gallery": "^1.2.4", "autoprefixer": "^10.4.16", "eslint": "^8", "eslint-config-next": "14.0.1", "file-loader": "^6.2.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5", "url-loader": "^4.1.1"}}