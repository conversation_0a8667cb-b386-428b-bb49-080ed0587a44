import { Fpx } from "./type";

export const FpxDefaultValues: Fpx = {
    id: '',
    companyId: '',
    userId: '',
    paymentID: '',
    boxID: 'gigbee',  // Default value from the provided data
    userID: '',
    cashierID: '',
    invNo: '',
    currencyID: 'MYR',  // Default value from the provided data
    totalAmt: 0,
    discAmt: 0,
    remark: '',
    buyerBankId: '',
    buyerEmail: '',
    invDetail: [
        {
            sku: 'sample string 1',  // Default value from the provided data
            qty: 1,
        }
    ],
    resultCode: '',
    errorDes: '',
    payPartnerId: 'myclear_fpx',  // Default value from the provided data
    tradeNo: '',
    orderCode: '',
    totalAmount: 0,
    paidAmount: 0,
    discountAmount: 0,
    feeType: 'MYR',  // Default value from the provided data
    paymentTime: '',
    closeTime: '',
    notifyTime: '',
    checkCode: '',
    ResultDetails: {
        TransactionStatus: '',
        BingoResult: '',
        PaymentResultGuid: '',
        CallDate: '',
        MerchantId: '',
        InvoiceNo: '',
        Amount: '',
        McpAmount: '',
        McpFxRate: '',
        McpCurrencyCode: '',
        CurrencyCode: '',
        TransactionDateTime: '',
        AgentCode: '',
        ChannelCode: '',
        ApprovalCode: '',
        ReferenceNo: '',
        TranRef: '',
        Pan: '',
        CardToken: '',
        IssuerCountry: '',
        Eci: '',
        InstallmentPeriod: '',
        InterestType: '',
        InterestRate: '',
        InstallmentMerchantAbsorbRate: '',
        RecurringUniqueID: '',
        FxAmount: '',
        FxRate: '',
        FxCurrencyCode: '',
        UserDefined1: '',
        UserDefined2: '',
        UserDefined3: '',
        UserDefined4: '',
        UserDefined5: '',
        RespCode: '',
        RespDesc: '',
        BoxID: 'gigbee',  // Default value from the provided data
        UserID: '',
        CashierID: '',
        InvNo: '',
        CurrencyID: 'MYR',  // Default value from the provided data
        TotalAmt: '0.00',
        DiscAmt: '0.00',
        PaymentID: '',
    },
    responsedAt: '',
    status: '',  // Default value from the provided data
    createdAt: '',
    updatedAt: ''
};