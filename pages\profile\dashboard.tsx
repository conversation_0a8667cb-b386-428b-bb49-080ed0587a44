import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import { Card, Col, Row } from "antd";
import { VictoryPie } from "victory";
import OrderHistory from "../../assets/logo/orderHistory.svg";
import AutoProposed from "../../assets/logo/autoProposed.svg";
import RecurringOrder from "../../assets/logo/recurringOrder.svg";
import StockTake from "../../assets/logo/stockTake.svg";
import GoodsReturn from "../../assets/logo/goodsReturn.svg";
import InvoiceIcon from "../../assets/logo/invoice.svg";
import Statement from "../../assets/logo/statement.svg";
import CreditNote from "../../assets/logo/creditNote.svg";
import DebitNote from "../../assets/logo/debitNote.svg";
import Payment from "../../assets/logo/payment.svg";
// import Logout from "../../assets/logo/logout.svg";
import CreditLimit from "../../assets/logo/creditLimit.svg";
import Outstanding from "../../assets/logo/outstanding.svg";
import Overdue from "../../assets/logo/overdue.svg";
import Processing from "../../assets/logo/processing.svg";
import JobPosting from "../../assets/logo/jobPosting.svg";
import Header, { supportedLocales } from "../../components/header";
import router from "next/router";
import { ClickableCard } from "@/components/card";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import { Retailer, Invoice } from "@/components/type";
import apiHelper from "../api/apiHelper";
import {
  DataSource,
  NumberThousandSeparator,
  encodeParams,
} from "@/stores/utilize";
import AppFooter from "@/components/footer";
import Loader from "../pageLoader";

function Profile() {
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  // const [outlletData, setOutletData] = useState<Outlet>({});
  const [isTimeWindows, setIsTimeWindows] = useState<Boolean>(false);
  const [creditLimit, setCreditLimit] = useState(0);
  const [outstanding, setOutstanding] = useState(0);
  const [overDue, setOverDue] = useState(0);
  const [totalInvoice, setTotalInvoice] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const { t } = useTranslation("common");
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  useEffect(() => {
    if (Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }
    }
  }, [Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess)?.length > 0) {
      // getSalesOrder();
      getInvoice();
      getInvoiceOutstanding();
      if (retailerAccess?.outletIds?.length) {
        getOutlet();
        // When able to select outlet, this is something that need to change
        apiHelper
          .GET(`outlets?id=${retailerAccess?.outletIds?.[0] ?? ""}`)
          .then((res: any) => {
            if (res.items && res.items.length) {
              // setOutletData(res.items);

              const timeWindowDay =
                res.items[0]?.timeWindowEnabled === "TRUE" ? true : false;
              setIsTimeWindows(timeWindowDay);
              setIsLoading(false);
            }
          });
      } else {
        setIsLoading(false);
      }
    }
  }, [retailerAccess]);

  useEffect(() => {
    // Function to check screen size and update state
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 500); // Define your breakpoint for small screens
    };

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Call handleResize initially to set initial screen size
    handleResize();

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // const getSalesOrder = () => {
  //   let currentOutletId = localStorage.getItem("currentOutletId");
  //   let params: any = {
  //     companyId: retailerAccess.companyId,
  //     companyBranchId: retailerAccess.companyBranchId,
  //     outletId: currentOutletId,
  //   };
  //   const dataSource = new DataSource(
  //     "salesOrders",
  //     encodeParams(params),
  //     false
  //   );
  //   dataSource
  //     .load()
  //     .then((res: any) => {
  //       // setCreditLimit()
  //     })
  //     .catch(() => { });
  // };

  const getInvoice = () => {
    let currentOutletId = localStorage.getItem("currentOutletId");
    // let params: any = {
    //   companyId: retailerAccess.companyId,
    //   companyBranchId: retailerAccess.companyBranchId,
    //   outletId: currentOutletId,
    // };
    const dataSource = new DataSource(
      "invoice/overdue",
      encodeParams({ outletId: currentOutletId }),
      false
    );
    dataSource
      .load()
      .then(async (res: any) => {
        if (res != null) {
          setOverDue(res[0].totalOverdue);
          setTotalInvoice(res[0].invoiceIds.length);
        }
      })
      .catch(() => { });
  };

  const getInvoiceOutstanding = () => {
    let currentOutletId = localStorage.getItem("currentOutletId");
    // let params: any = {
    //   companyId: retailerAccess.companyId,
    //   companyBranchId: retailerAccess.companyBranchId,
    //   outletId: currentOutletId,
    // };
    const dataSource = new DataSource(
      "invoice/outstanding",
      encodeParams({ outletId: currentOutletId }),
      true
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res != null) {
          const data = res[0];
          // let totalAmount = 0;
          // res.forEach((inv: Invoice) => {
          //   const cal =
          //     (inv.netAmount ?? 0) -
          //     (inv.processingAmount ?? 0) -
          //     (inv.paidAmount ?? 0);
          //   totalAmount = totalAmount + cal;
          // });
          setOutstanding(data.totalOverdue);
        }
      })
      .catch(() => { });
  };

  const getOutlet = () => {
    let currentOutletId = localStorage.getItem("currentOutletId");
    // let params: any = {
    //   companyId: retailerAccess.companyId,
    //   companyBranchId: retailerAccess.companyBranchId,
    //   outletId: currentOutletId,
    // };
    const dataSource = new DataSource(
      "outlets",
      encodeParams({ id: currentOutletId }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res != null && res.items.length > 0) {
          setCreditLimit(res.items[0].creditLimit);
          // setOutstanding(res.items[0].outstandingPayment);
        }
        // setCreditLimit()
      })
      .catch(() => { });
  };

  const navigateTo = (path: string) => {
    router.push(path);
  };

  const metricValue = 60;

  const headerItems = [
    {
      label: t("Dashboard.dashboard"),
      route: "/profile/dashboard",
      className: "labelTextStyle",
    },
  ];

  interface RectangleProps {
    percentage: number;
    amount: string;
    color: string;
    status: string;
  }
  const getRectangleStyle = (color: string): React.CSSProperties => ({
    width: "10px", // Set the width based on the percentage
    height: "10px", // Set your desired height
    backgroundColor: `${color}`, // Set the color
  });

  //UI Part
  const StatusRectangle: React.FC<RectangleProps> = ({
    percentage,
    amount,
    color,
    status,
  }) => {
    return (
      <Row className="flex flex-row justify-between">
        <Col className="flex flex-row gap-1 items-center">
          {percentage === 101 ? (
            <>
              <div style={getRectangleStyle(color)}></div>
              <p>{` ${status}`}</p>
            </>
          ) : (
            <>
              {" "}
              <div style={getRectangleStyle(color)}></div>{" "}
              <p>{`${percentage}% ${status}`}</p>
            </>
          )}
        </Col>
        {percentage === 101 ? <p>{`${amount}`}</p> : <p>{`RM ${amount}`}</p>}
      </Row>
    );
  };

  const creditStatus = () => {
    const creditPercent = (outstanding / creditLimit) * 100;

    if (isSmallScreen) {
      return (
        <div className="flex flex-col gap-y-2">
          <Card
            className="rounded-3xl"
            style={{
              boxShadow: "rgba(0, 0, 0, 0.09) 0px 3px 12px",
              background:
                "linear-gradient(to right, rgba(244,247,255,1) 50%, rgba(255,251,246,1) 100%)",
            }}
          >
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <div>
                  <h3 className="text-xl font-bold">Credit Limit</h3>
                  <p className="text-sm opacity-80">Available Balance</p>
                </div>
              </div>
              <p className="text-2xl font-bold">{creditLimit - outstanding}</p>
            </div>
          </Card>
          <Card
            className="rounded-3xl"
            style={{
              boxShadow: "rgba(0, 0, 0, 0.09) 0px 3px 12px",
              background:
                "linear-gradient(to right, rgba(255,247,248,1) 50%, rgba(255,251,246,1) 100%)",
            }}
          >
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <div>
                  <h3 className="text-xl font-bold">Outstanding</h3>
                  <p className="text-sm opacity-80">Available Balance</p>
                </div>
              </div>
              <p className="text-2xl font-bold">{creditLimit - outstanding}</p>
            </div>
          </Card>
          <Card
            className="rounded-3xl"
            style={{
              boxShadow: "rgba(0, 0, 0, 0.09) 0px 3px 12px",
              background:
                "linear-gradient(to right, rgba(255,247,234,1) 50%, rgba(255,251,246,1) 100%)",
            }}
          >
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <div>
                  <h3 className="text-xl font-bold">Invoice Overdue</h3>
                  <p className="text-sm opacity-80">Total</p>
                </div>
              </div>
              <p className="text-2xl font-bold">{totalInvoice}</p>
            </div>
          </Card>
        </div>
      );
    } else
      return (
        <Row className="grid-container">
          <Col>
            <Card
              //   title="Credit Limit"
              className=" rounded-lg"
              style={{
                boxShadow: "rgba(0, 0, 0, 0.09) 0px 3px 12px",
                background:
                  "linear-gradient(to bottom, rgba(244,247,255,1) 0%, rgba(255,251,246,1) 100%)",
              }}
              bordered={false}
            >
              <Row>
                <p className="text-[18px] font-semibold pr-2">
                  {t("Dashboard.creditLimit")}
                </p>
              </Row>
              <Row>
                <p className="text-[18px] font-bold">
                  RM {NumberThousandSeparator(creditLimit)}
                </p>
              </Row>
              <div className="flex items-center justify-center">
                <svg width={180} height={180}>
                  <CreditLimit src={CreditLimit.src} className="" />
                  <VictoryPie
                    // padAngle={0}
                    // used to hide labels
                    standalone={false}
                    cornerRadius={({ datum }) => datum?.y * 5}
                    padAngle={10}
                    labelComponent={<></>}
                    innerRadius={60}
                    width={180}
                    height={180}
                    data={[
                      { key: "", y: creditPercent },
                      { key: "", y: 100 - creditPercent },
                    ]}
                    colorScale={["#1B59F8", "#BFD0FD"]}
                  />
                </svg>
              </div>
              <StatusRectangle
                percentage={parseFloat(creditPercent.toFixed(2))}
                amount={NumberThousandSeparator(outstanding)}
                color="#1B59F8"
                status="Used"
              />
              <StatusRectangle
                percentage={parseFloat((100 - creditPercent).toFixed(2))}
                amount={NumberThousandSeparator(creditLimit - outstanding)}
                color="#BFD0FD"
                status="Unused"
              />
            </Card>
          </Col>
          <Col>
            <Card
              className=" rounded-lg"
              style={{
                boxShadow: "rgba(0, 0, 0, 0.09) 0px 3px 12px",
                background:
                  "linear-gradient(to bottom, rgba(255,247,248,1) 0%, rgba(255,251,246,1) 100%)",
              }}
              bordered={false}
            >
              <Row>
                <p className="text-[18px] font-semibold  pr-2">
                  {t("Dashboard.outstandingAmount")}
                </p>
              </Row>
              <Row>
                <p className="text-[18px] font-bold">
                  RM {NumberThousandSeparator(outstanding)}
                </p>
              </Row>
              <div className="flex items-center justify-center">
                <svg width={180} height={180}>
                  <Outstanding src={Outstanding.src} className="" />
                  <VictoryPie
                    // padAngle={0}
                    // used to hide labels
                    standalone={false}
                    cornerRadius={({ datum }) => datum.y * 5}
                    padAngle={10}
                    labelComponent={<></>}
                    innerRadius={60}
                    width={180}
                    height={180}
                    data={[
                      { key: "", y: creditPercent },
                      { key: "", y: 100 - creditPercent },
                    ]}
                    colorScale={["#EC1B23", "#F9BDBF"]}
                  />
                </svg>
              </div>
              <StatusRectangle
                percentage={parseFloat(creditPercent.toFixed(2))}
                amount={NumberThousandSeparator(outstanding)}
                color="#EC1B23"
                status="Used"
              />
              <StatusRectangle
                percentage={parseFloat((100 - creditPercent).toFixed(2))}
                amount={NumberThousandSeparator(creditLimit - outstanding)}
                color="#F9BDBF"
                status="Unused"
              />
            </Card>
          </Col>
          <Col>
            <Card
              className=" rounded-lg"
              style={{
                boxShadow: "rgba(0, 0, 0, 0.09) 0px 3px 12px",
                background:
                  "linear-gradient(to bottom, rgba(255,247,234,1) 0%, rgba(255,251,246,1) 100%)",
              }}
              bordered={false}
            >
              <Row>
                <p className="text-[18px] font-semibold pr-2">
                  {t("Dashboard.overdueAmount")}
                </p>
              </Row>
              <Row>
                <p className="text-[18px] font-bold">
                  RM {NumberThousandSeparator(overDue)}
                </p>
              </Row>
              <div className="flex items-center justify-center">
                <svg width={180} height={180}>
                  <Overdue src={Overdue.src} className="" />
                  <VictoryPie
                    // padAngle={0}
                    // used to hide labels
                    standalone={false}
                    cornerRadius={({ datum }) => datum.y * 5}
                    padAngle={10}
                    labelComponent={<></>}
                    innerRadius={60}
                    width={180}
                    height={180}
                    data={[
                      { key: "", y: 100 },
                      { key: "", y: 0 },
                    ]}
                    colorScale={["#FEA654", "#FFE7D1"]}
                  />
                </svg>
              </div>
              <StatusRectangle
                percentage={101}
                amount={totalInvoice.toString()}
                color="#FEA654"
                status="Total Invoice Overdue"
              />
              <br></br>
            </Card>
          </Col>
          {/* <Col>
          <Card bordered={false}>
            <Row>
              <p className="text-[18px] font-semibold pr-2">
                Processing Amount
              </p>
            </Row>
            <Row>
              <p className="text-[18px] font-bold">RM3,000.00</p>
            </Row>
            <div className="flex items-center justify-center">
              <svg width={180} height={180}>
                <Processing src={Processing.src} className="" />
                <VictoryPie
                  // padAngle={0}
                  // used to hide labels
                  standalone={false}
                  cornerRadius={({ datum }) => datum.y * 5}
                  padAngle={10}
                  labelComponent={<></>}
                  innerRadius={60}
                  width={180}
                  height={180}
                  data={[
                    { key: "", y: metricValue },
                    { key: "", y: 100 - metricValue },
                  ]}
                  colorScale={["#1DA707", "#B7E5B0"]}
                />
              </svg>
            </div>
            <StatusRectangle
              percentage={60}
              amount="6,000.00"
              color="#1DA707"
              status="Used"
            />
            <StatusRectangle
              percentage={40}
              amount="6,000.00"
              color="#B7E5B0"
              status="Unused"
            />
          </Card>
        </Col> */}
        </Row>
      );
  };

  const order = () => {
    return (
      // <Row gutter={[8, 8]} className="w-full">
      // <div className="flex flex-wrap w-full gap-x-2">
      //   <div className="grid grid-row-1 md:grid-row-3 sm:py-6 ">
      <Row className="flex flex-row gap-4 px-2">
        <ClickableCard
          title={t("Dashboard.salesOrder")}
          imageComponent={
            <OrderHistory
              width="40"
              height="40"
              viewBox="0 0 90 80"
              src={OrderHistory.src}
            />
          }
          onClick={() => navigateTo("/salesOrder/salesOrderListing")}
        />

        <ClickableCard
          title={t("Dashboard.preOrder")}
          imageComponent={
            <OrderHistory
              width="40"
              height="40"
              viewBox="0 0 90 80"
              src={OrderHistory.src}
            />
          }
          onClick={() => navigateTo("/preOrder/preOrderListing")}
        />

        <ClickableCard
          title={t("Dashboard.autoProposedOrder")}
          imageComponent={
            <AutoProposed
              width="40"
              height="40"
              viewBox="0 0 80 80"
              src={AutoProposed.src}
            />
          }
          onClick={() => navigateTo("/autoProposed/autoProposedListing")}
        />
      </Row>
    );
  };

  const inventory = () => {
    return (
      <Row className="gap-4 px-2">
        {/* <ClickableCard
          title={t("Dashboard.stockTake")}
          imageComponent={
            <StockTake
              width="40"
              height="40"
              viewBox="0 0 90 100"
              src={StockTake.src}
            />
          }
          onClick={() => navigateTo("/")}
          comingSoon
        /> */}
        <ClickableCard
          title={t("Dashboard.goodsReturn")}
          imageComponent={
            <GoodsReturn
              width="40"
              height="40"
              viewBox="0 0 90 100"
              src={GoodsReturn.src}
            />
          }
          onClick={() => navigateTo("/goodsReturn/goodsReturnListing")}
        />
      </Row>
    );
  };

  const financial = () => {
    return (
      <>
        <Row className="gap-4 px-2">
          {/* {!isTimeWindows ? ( */}
          <ClickableCard
            title={t("Dashboard.invoice")}
            imageComponent={
              <InvoiceIcon
                width="40"
                height="40"
                viewBox="0 0 75 85"
                src={InvoiceIcon.src}
              />
            }
            onClick={() => navigateTo("/invoice/invoiceListing")}
          />
          {/* ) : null} */}
          {isTimeWindows ? (
            <ClickableCard
              title={t("Dashboard.statement")}
              imageComponent={
                <Statement
                  width="40"
                  height="40"
                  viewBox="0 0 60 85"
                  src={Statement.src}
                />
              }
              onClick={() => navigateTo("/statement/statementListing")}
            />
          ) : null}
          <ClickableCard
            title={t("Dashboard.creditNote")}
            imageComponent={
              <CreditNote
                width="40"
                height="40"
                viewBox="0 0 85 90"
                src={CreditNote.src}
              />
            }
            onClick={() => navigateTo("/creditNote/creditNoteListing")}
          />
          <ClickableCard
            title={t("Dashboard.debitNote")}
            imageComponent={
              <DebitNote
                width="40"
                height="40"
                viewBox="0 0 80 85"
                src={DebitNote.src}
              />
            }
            onClick={() => navigateTo("/debitNote/debitNoteListing")}
          />
          <ClickableCard
            title={t("Dashboard.paymentHistory")}
            imageComponent={
              <Payment
                width="40"
                height="40"
                viewBox="0 0 80 85"
                src={Payment.src}
              />
            }
            onClick={() => navigateTo("/payment/paymentListing")}
          />
        </Row>
      </>
    );
  };

  // const others = () => {
  //   return (
  //     <Row className="gap-4">
  //       <ClickableCard
  //         title={t("Dashboard.jobListing")}
  //         imageComponent={
  //           <JobPosting
  //             width="40"
  //             height="40"
  //             viewBox="0 0 80 90"
  //             src={JobPosting.src}
  //           />
  //         }
  //         onClick={() => navigateTo("/")}
  //         comingSoon
  //       />
  //     </Row>
  //   );
  // };

  if (isLoading) return <Loader />;

  return (
    <div className="flex flex-col min-w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => { }} />
      <Content className="flex flex-col gap-y-5 w-full sm:w-4/5 sm:mx-auto mb-16 sm:pt-0 sm:mb-10 px-2">
        <h1>{t("Dashboard.dashboard")}</h1>
        {creditStatus()}
        <Row className="flex flex-col gap-y-2">
          <h1>{t("Dashboard.order")}</h1>
          {order()}
        </Row>
        <Row className="flex flex-col gap-y-2">
          <h1>{t("Dashboard.inventory")}</h1>
          {inventory()}
        </Row>
        <Row className="flex flex-col gap-y-2">
          <h1>{t("Dashboard.financial")}</h1>
          {financial()}
        </Row>
        {/* <Row className="flex flex-col gap-y-2">
          <h1>{t("Dashboard.others")}</h1>
          {others()}
        </Row> */}
      </Content>
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default Profile;
