import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import defaultImage from "../../assets/default/emptyImage.png";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import {
  But<PERSON>,
  Col,
  Drawer,
  FloatButton,
  Form,
  MenuProps,
  Row,
  Tooltip,
  Upload,
  UploadFile,
  UploadProps,
} from "antd";
import Header, { supportedLocales } from "../../components/header";
import { FormTextInput, SelectInput } from "@/components/input";
import {
  BackButtonUI,
  PrimaryButtonUI,
  SecondaryButtonUI,
} from "@/components/buttonUI";
import { useRouter } from "next/router";
import { ArrowUpOutlined, EyeOutlined, PlusOutlined } from "@ant-design/icons";
import {
  ListingTableUI,
  MessageErrorUI,
  MessageInfoUI,
  MessageSuccessUI,
  statusApproval,
} from "@/components/ui";
import {
  DataSource,
  PicSignedUrl,
  encode<PERSON>ara<PERSON>,
  PUBLIC_BUCKET_URL,
  capitalize,
  formateDateAndTime,
  NumberThousandSeparator,
  setFilterForm,
  getParamsFromLocalStorage,
  setParamsFromLocalStorage,
  SignedUrl,
} from "@/stores/utilize";
import {
  CompanyGeneralInfo,
  CreditNote,
  Outlet,
  Product,
  ProductUOM,
  UOM,
  ProductOrdered,
  SalesOrder,
  CreditNoteProduct,
  Staff,
  Invoice,
  Retailer,
} from "@/components/type";
import { ModalUI } from "@/components/modalUI";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import { creditTypeOption, statusFilterOption1 } from "@/components/config";
import FilterFormComponent from "@/components/filter";
import apiHelper from "../api/apiHelper";
import { PDFDocument } from "pdf-lib";
import { ComponentFilterSelect } from "@/components/filterSelectInput";
import AppFooter from "@/components/footer";
import _ from "lodash";

function CreditNoteListing() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [filterForm] = Form.useForm();
  const [filterModalForm] = Form.useForm();
  const [creditNoteDetailForm] = Form.useForm();
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [showButtonLoader, setShowButtonLoader] = useState(false);
  const [cursor, setCursor] = useState("");
  const [tempCursor, setTempCursor] = useState("");
  const [tableLoading, setTableLoading] = useState(false);
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  const [data, setData] = useState<any[]>([]);
  const [companyMap, setCompanyMap] = useState(new Map());
  const [salesOrderMap, setSalesOrderMap] = useState(new Map());
  const [outletMap, setOutletMap] = useState(new Map());
  const [productMap, setProductMap] = useState(new Map());
  const [uomMap, setUomMap] = useState(new Map());
  const [staffMap, setStaffMap] = useState(new Map());
  const [invoiceMap, setInvoiceMap] = useState(new Map());
  const [creditNoteProductData, setCreditNoteProductData] = useState<
    CreditNoteProduct[]
  >([]);
  // const [productNameOption, setProductNameOption] = useState<SelectOption[]>(
  //   []
  // );
  // const [productSkuOption, setProductSkuOption] = useState<SelectOption[]>([]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  // const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRowData, setSelectedRowData] = useState<any[]>([]);

  const [pdfLoading, setPDFLoading] = useState(false);
  const [allFiles, setAllFiles] = useState<{ [key: string]: any }>({});
  // ======================================================================
  // filter function - state()
  // ======================================================================
  const [showClearFilter, setShowClearFilter] = useState(false);
  const [fuzzySearchFilter, setFuzzySearchFilter] = useState("");
  const [modalFilter, setModalFilter] = useState<any>({});
  const [filterSetting, setFilterSetting] = useState("");
  const [statusValue, setStatusValue] = useState("All");
  const [statusKey, setStatusKey] = useState("ALL");
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [fieldName, setFieldName] = useState("");

  const headerItems = [
    {
      label: t("Header.dashboard"),
      route: "/profile/dashboard",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Header.creditNote"),
      router: "/creditNote/creditNoteListing",
      className: "labelTextStyle",
    },
  ];

  useEffect(() => {
    // Function to check screen size and update state
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 950); // Define your breakpoint for small screens
    };

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Call handleResize initially to set initial screen size
    handleResize();

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollButton(window.pageYOffset > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    if (Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }
    }
  }, [Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0) {
      // getCreditNote();
      // getProductData();

      // Get from localStorage
      const filterParams: any = getParamsFromLocalStorage(
        router.pathname,
        "cnFilter"
      );

      const filterKey: any = {
        fuzzySearch: "",
        creditNoteNo: null,
        type: null,
        // branchName: null,
        // BranchCode: null,
        outletName: null,
        outletCode: null,
        // warehouseName: null,
        // firstName: null,
        // lastName: null,
        // staffCode: null,
        productName: null,
        productSku: null,
        invoiceNo: null,
        // remark: null,
      };

      const clonedFilterKey = { ...filterKey };
      delete clonedFilterKey.fuzzySearch;

      Object.keys(clonedFilterKey).forEach((key) => {
        const capitalizedKey = key
          .split(/(?=[A-Z])|\s+/)
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        clonedFilterKey[capitalizedKey] = clonedFilterKey[key];
        delete clonedFilterKey[key];
      });

      const keysAsString: string = Object.keys(clonedFilterKey).join(", ");
      setFieldName(keysAsString);

      if (filterParams) {
        // Initialize variables to store the values
        // Follow search params' key

        setFilterSetting(filterParams);
        setFilterForm(filterParams, filterKey);
        filterForm.setFieldValue(["fuzzySearch"], filterKey.fuzzySearch);
        filterModalForm.setFieldsValue(filterKey);
        setFuzzySearchFilter(filterKey.fuzzySearch);
        let data = {
          creditNoteNo: filterKey.creditNoteNo || "",
          invoiceNo: filterKey.invoiceNo || "",
          outletCode: filterKey.outletCode || "",
          outletName: filterKey.outletName || "",
          productSku: filterKey.productSku || "",
          productName: filterKey.productName || "",
          type: filterKey.type || "",
        };
        setModalFilter(data);
        setStatusKey(filterKey.status ?? "ALL");
        const filterStatusLabel: any = statusFilterOption1.find(
          (item: any) => item.key === filterKey.status
        )?.label;
        setStatusValue(filterStatusLabel ?? "All");
      } else {
        getCreditNote(true);
      }
    }
  }, [retailerAccess]);

  useEffect(() => {
    const data = {
      fuzzySearch: fuzzySearchFilter || "",
      creditNoteNo: modalFilter.creditNoteNo || "",
      invoiceNo: modalFilter.invoiceNo || "",
      outletCode: modalFilter.outletCode || "",
      outletName: modalFilter.outletName || "",
      productSku: modalFilter.productSku || "",
      productName: modalFilter.productName || "",
      type: modalFilter.type || "",
      status: statusKey === "ALL" ? "" : statusKey,
    };

    const allPropertiesEmpty = Object.values(data).every(
      (value) => value === ""
    );

    if (!allPropertiesEmpty) {
      searchCN(data);
    } else {
      setFilterSetting("");
    }
  }, [fuzzySearchFilter, statusKey, modalFilter]);

  useEffect(() => {
    // Check params whether is same
    const filterParams = getParamsFromLocalStorage(router.pathname, "cnFilter");
    if (filterSetting) {
      getCreditNote(true, false);

      if (filterSetting !== filterParams) {
        setParamsFromLocalStorage(router.pathname, filterSetting, "cnFilter");
      }
      setShowClearFilter(true);
    } else {
      setShowClearFilter(false);
      if (data.length > 0) {
        localStorage.removeItem("cnFilter");
      }
      getCreditNote(true, false);
    }
  }, [filterSetting]);

  // *************************************************************************************
  // *** Scolling Function - useEffect ***
  // *************************************************************************************
  // Check scrolling position
  useEffect(() => {
    if (cursor === "") return;
    const handleScroll = () => {
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      const scrollPosition = window.scrollY;
      if (windowHeight + scrollPosition >= documentHeight) {
        // Stop API calling when cursor is equal to '0'
        if (cursor !== "0") {
          getCreditNote();
        }
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [cursor]);

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  // *************************************************************************************
  // *** Get API ***
  // *************************************************************************************

  let isLoading = false;

  const getCreditNote = (isRefresh = false, isClearFilter = false) => {
    // const accessBranches = otherViewBranchAccess === false ? retailerAccess.companyBranchIds : otherCompanyBranchesIds;
    // if (retailerAccess && Object.keys(retailerAccess).length === 0) {
    //   return;
    // }
    if (isLoading) return;
    setTableLoading(true);
    setShowButtonLoader(true);
    let currentOutletId = localStorage.getItem("currentOutletId");
    isLoading = true;
    setTimeout(() => {
      let params: any = {
        sort: "createdAt",
        sortOrder: "-1",
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        outletId: currentOutletId,
      };

      if (isRefresh === false) {
        params.cursor = cursor;
      }
      // if (!isAdmin) {
      //   params.companyBranchIds = accessBranches;
      // }
      // const checkAdminRights = isAdmin ? params : params + "&companyId=" + retailerAccess.companyId;
      const checkFilterRights =
        filterSetting && !isClearFilter
          ? filterSetting +
            (cursor && cursor !== "0" ? `&cursor=${cursor}` : "")
          : encodeParams(params);
      const dataSource = new DataSource(
        "creditNotes",
        checkFilterRights,
        false
      );

      // !isAdmin && !otherViewBranchAccess ? filterForm.setFieldValue("companyId", retailerAccess.companyId) : null;

      // if ((accessBranches && accessBranches.length > 0) || isAdmin) {
      dataSource
        .load()
        .then((res: any) => {
          if (res && res.items !== null) {
            let data = res.items;
            const objectMap = data.reduce(
              (accumulator: any, current: CreditNote) => {
                accumulator["companyId"] = accumulator["companyId"] || [];
                if (
                  current.companyId &&
                  !companyMap.has(current.companyId) &&
                  !accumulator["companyId"].includes(current.companyId)
                ) {
                  accumulator["companyId"].push(current.companyId ?? "");
                }

                accumulator["outletId"] = accumulator["outletId"] || [];
                if (
                  current.outletId &&
                  !outletMap.has(current.outletId) &&
                  !accumulator["outletId"].includes(current.outletId)
                ) {
                  accumulator["outletId"].push(current.outletId ?? "");
                }

                accumulator["staffId"] = accumulator["staffId"] || [];
                if (
                  current.staffId &&
                  !staffMap.has(current.staffId) &&
                  !accumulator["staffId"].includes(current.staffId)
                ) {
                  accumulator["staffId"].push(current.staffId ?? "");
                }

                accumulator["invoiceId"] = accumulator["invoiceId"] || [];
                if (
                  current.invoiceId &&
                  !invoiceMap.has(current.invoiceId) &&
                  !accumulator["invoiceId"].includes(current.invoiceId)
                ) {
                  accumulator["invoiceId"].push(current.invoiceId ?? "");
                }

                current.creditNoteProducts?.reduce(
                  (acc: any, product: CreditNoteProduct) => {
                    accumulator["productId"] = accumulator["productId"] || [];
                    if (
                      product.productId &&
                      !productMap.has(product.productId) &&
                      !accumulator["productId"].includes(product.productId)
                    ) {
                      accumulator["productId"].push(product.productId ?? "");
                    }

                    accumulator["productUOMId"] =
                      accumulator["productUOMId"] || [];
                    if (
                      product.productUOMId &&
                      !uomMap.has(product.productUOMId) &&
                      !accumulator["productUOMId"].includes(
                        product.productUOMId
                      )
                    ) {
                      accumulator["productUOMId"].push(
                        product.productUOMId ?? ""
                      );
                    }

                    return acc;
                  },
                  {}
                );

                return accumulator;
              },
              {}
            );

            getCompany(objectMap["companyId"]);
            getOutlets(objectMap["outletId"]);
            getSalesOrder(objectMap["salesOrderId"]);
            getProduct(objectMap["productId"]);
            getUOM(objectMap["productUOMId"]);
            getStaffs(objectMap["staffId"]);
            getInvoice(objectMap["creditNoteId"]);
            // getCompanyBranch(objectMap["companyBranchId"]);
            const nextCursor = res.cursor; // Get the cursor from the last item in the response
            if (nextCursor !== cursor || isRefresh) {
              // Avoid duplicates
              if (!isRefresh) {
                // setFullData((prevData) => [...prevData, ...data]);
                setData((prevData) => [...prevData, ...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              } else {
                // setFullData([...data]);
                setData([...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              }
              // cursor = nextCursor;
              setCursor(nextCursor);
              setTempCursor(nextCursor);
            }
          }
          isLoading = false;
        })
        .catch(() => {
          isLoading = false;
          //* This Part need re-edit*//
        });
      // } else {
      //   setGoodsReturnListingInfoData([]);
      // }
    }, 500);
  };

  const getCompany = async (id: string[] = []) => {
    const dataSource = new DataSource(
      "companies",
      encodeParams({ id: id }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setCompanyMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: CompanyGeneralInfo) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getOutlets = async (id: string[] = []) => {
    let tempProductMap = new Map(outletMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "outlets",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });
        if (res !== null && res.items.length > 0) {
          setOutletMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Outlet) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getSalesOrder = async (id: string[] = []) => {
    let tempProductMap = new Map(salesOrderMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "salesOrders",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setSalesOrderMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: SalesOrder) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: SalesOrder) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getProduct = async (id: string[] = []) => {
    let tempProductMap = new Map(productMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "productCatalogues",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setProductMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Product) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Product) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getUOM = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("uoms", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setUomMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: UOM) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: UOM) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getStaffs = async (id: string[] = []) => {
    let tempProductMap = new Map(staffMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "staffs",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setStaffMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Staff) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Staff) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getInvoice = async (id: string[] = []) => {
    let tempProductMap = new Map(invoiceMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      // status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "invoices",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setInvoiceMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Invoice) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Invoice) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  // const getProductData = async () => {
  //   const dataSource = new DataSource(
  //     "productCatalogues",
  //     "status=ACTIVE",
  //     true
  //   );
  //   const res: any = await dataSource.load();

  //   if (res !== null) {
  //     let nameList: SelectOption[] = [];
  //     let skuList: SelectOption[] = [];
  //     res.map((value: any) => {
  //       nameList.push({
  //         value: value.id,
  //         label: value.name,
  //       });
  //       skuList.push({
  //         value: value.id,
  //         label: value.sku,
  //       });
  //     });
  //     setProductNameOption(nameList);
  //     setProductSkuOption(skuList);
  //   }
  // };

  // *************************************************************************************
  // *** Function ***
  // *************************************************************************************
  const props: UploadProps = {
    name: "file",
    multiple: false,
    maxCount: 1,
    showUploadList: {
      showPreviewIcon: false,
    },
    beforeUpload: (file) => {
      if (
        file.type !== "image/png" &&
        file.type !== "image/jpg" &&
        file.type !== "image/jpeg" &&
        file.type !== "application/pdf"
      ) {
        MessageErrorUI(
          `${file.name} is an invalid file format. Please change the file extension to either .pdf, .png, .jpg, .jpeg.`
        );
        return Upload.LIST_IGNORE;
      } else if (file.size > 5242880) {
        MessageErrorUI(
          `${file.name} is too large. Please upload another document that is smaller than 5MB.`
        );
        return Upload.LIST_IGNORE;
      } else {
        return false;
      }
    },
  };

  const generatePDF = async () => {
    setPDFLoading(true);

    // Filter out data that is out of date
    const dateOverdue = new Date("2023-10-01T00:00:00.000Z");

    const filteredData = selectedRowData.filter((item) => {
      const createdAtDate = new Date(item.createdAt);
      return createdAtDate >= dateOverdue;
    });

    if (filteredData.length === 0) {
      MessageInfoUI(t("Common.PDFPrintMessage"));
      setPDFLoading(false);
      return;
    }

    try {
      const pdfDoc = await PDFDocument.create();

      for (const item of filteredData) {
        let donorPdfBytes;

        // if (item.creditNoteDocument !== "") {
        //   const res: any = await PicSignedUrl(item.creditNoteDocument!);
        //   const pdfBytesResponse = await fetch(res);
        //   donorPdfBytes = await pdfBytesResponse.arrayBuffer();
        // } else {
        //   const res: any = await apiHelper.GET("creditNote/pdf?id=" + item.id);
        //   const pdfBytesResponse = await fetch(res.item);
        //   donorPdfBytes = await pdfBytesResponse.arrayBuffer();
        // }

        let url = "creditNote/pdf?id=";
        if (
          item.eInvoiceSubmissionType === "EINVOICE" &&
          item.creditNoteRefInvoices &&
          item.creditNoteRefInvoices.length === 0
        ) {
          url = "creditNote/ePdf?id=";
        } else if (
          item.eInvoiceSubmissionType === "EINVOICE" &&
          item.creditNoteRefInvoices &&
          item.creditNoteRefInvoices.length > 0
        ) {
          url = "creditNote/ePdf/multiInvoice?id=";
        } else if (
          item.creditNoteRefInvoices &&
          item.creditNoteRefInvoices.length > 0
        ) {
          url = "creditNote/pdf/multiInvoice?id=";
        }

        const res: any = await apiHelper.GET(url + item.id);
        const documentSigned: any = await PicSignedUrl(res.item);
        const pdfBytesResponse = await fetch(documentSigned);
        donorPdfBytes = await pdfBytesResponse.arrayBuffer();

        const donorPdfDoc = await PDFDocument.load(donorPdfBytes);

        // Copy all pages from donorPdfDoc to pdfDoc
        const donorPages = await pdfDoc.copyPages(
          donorPdfDoc,
          donorPdfDoc.getPageIndices()
        );
        donorPages.forEach((page: any) => pdfDoc.addPage(page));
      }

      // Convert the merged PDF to a blob
      const mergedPdfBytes = await pdfDoc.save();

      // Create a blob URL and open it in a new tab
      const blob = new Blob([mergedPdfBytes], { type: "application/pdf" });
      const blobUrl = URL.createObjectURL(blob);
      window.open(blobUrl, "_blank");
    } catch (error) {
      console.error(error);
      setPDFLoading(false);
    }

    setPDFLoading(false);
    if (filteredData.length > 0) {
      MessageSuccessUI(filteredData.length + " " + t("PDFPrintMessageSuccess"));
    }
  };

  // *************************************************************************************
  // *** Credit Note Detail Modal ***
  // *************************************************************************************
  const showModal = () => {
    return (
      <div className="w-full">
        <Col>
          <Form
            className="w-full pt-3.5"
            form={creditNoteDetailForm}
            layout="vertical"
            scrollToFirstError
          >
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="creditNoteNo"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.creditNote") + " " + t("Common.no")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={
                    t("Common.eg.") + " " + t("Placeholder.OutletName")
                  }
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="creditNoteDate"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("ssm") + " " + t("Validation.requiredField"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.creditNoteDate")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg") + " " + t("common.email")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>

            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="company"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.company")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.egEg") + " " + t("CreditNote.unitNo")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="invoice"
                className="flex-1"
                // rules={[{ required: true, mess age: t("GPSLocation") + " " + t("Validation.Is.Required") }]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.invoice") + " " + t("Common.no")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg") + " " + t("CreditNote.unitNo")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="type"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("Address1") + " " + t("Validation.Is.Required"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.type")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.11")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="status"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.status")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="grossAmount"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.grossAmount") + " " + t("RM")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="taxAmount"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.taxAmount") + " " + t("RM")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="nettAmount"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.nettAmount") + " " + t("RM")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>
            <Form.Item
              name="document"
              className="flex-1 p-2"
              label={
                <p className="text-neutral700 text-[12px]">
                  {t("CreditNote.supportedDocument")}
                </p>
              }
            >
              <Upload
                {...props}
                disabled
                onPreview={(val: any) => {
                  // only uploaded photo can download and show
                  // new upload wont be able to click
                  if (val.url != undefined) {
                    let link = document.createElement("a");
                    link.target = "_blank";
                    link.href = val.url;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }
                }}
                onChange={(info) => {
                  if (info.fileList.length > 0) {
                    if (
                      info.fileList.at(-1) !== undefined &&
                      info.file.status !== "removed"
                    ) {
                      let file = info.fileList.at(-1);
                      if (file !== undefined) {
                        file.status = "done";
                        let fileObj = file.originFileObj;
                        setAllFiles({
                          ...allFiles,
                          document: fileObj,
                        });
                      }
                    }
                  }
                }}
                onRemove={() => {
                  setAllFiles({
                    ...allFiles,
                    ["document"]: null,
                  });
                }}
                fileList={
                  allFiles["document"] === undefined ||
                  allFiles["document"] === null
                    ? []
                    : [allFiles["document"]]
                }
              >
                <div className="flex items-center gap-x-4 p-2 text-buttonPurple bg-lightPurple font-semibold">
                  <PlusOutlined />
                  <p>{t("CreditNote.supportedDocument")}</p>
                </div>
              </Upload>
            </Form.Item>
            <span className="m-1"></span>
            {creditNoteProductData.length > 0 ? (
              <ListingTableUI
                // EditableCell={EditableCell}
                bordered
                dataSource={creditNoteProductData}
                columns={productColumn}
                // rowClassName="editable-row"
                rowKey={(record: any) => record.id}
                cursor={false}
                // loader={showButtonLoader}
                pagination={false}
                endMessage={""}
              />
            ) : null}
          </Form>
        </Col>
      </div>
    );
  };

  const productColumn = [
    {
      title: t("CreditNote.product"),
      dataIndex: "productId",
      key: "id",
      render: (_: any, record: ProductOrdered) => {
        const item = productMap.get(record.productId);
        if (item) {
          return (
            <div>
              <Col className="flex items-center w-full">
                <img
                  className="object-contain h-[80px] min-w-[80px] p-2"
                  src={
                    item.productUOM.find(
                      (item: ProductUOM) =>
                        record.productUOMId === item.productUOMId
                    )?.pictures
                      ? PUBLIC_BUCKET_URL +
                        item.productUOM.find(
                          (item: ProductUOM) =>
                            record.productUOMId === item.productUOMId
                        )?.pictures[1]
                      : defaultImage.src
                  }
                  loading="lazy"
                ></img>
                <div className="flex flex-col w-full">
                  <p className="font-bold text-[14px]">{item.name}&nbsp;</p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("CreditNote.productCode")}: {item.sku}
                    </span>
                  </p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("CreditNote.uom")}:{" "}
                      {uomMap.get(record.productUOMId)?.name}
                    </span>
                  </p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("CreditNote.unitPrice")}: {record.price}
                    </span>
                  </p>
                </div>
              </Col>
            </div>
          );
        } else return null;
      },
    },
    {
      title: t("CreditNote.invoiceQuantity"),
      dataIndex: "invoiceQuantity",
      sorter: (a: any, b: any) => a.quantity.localeCompare(b.quantity),
      showSorterTooltip: false,
      key: "quantity",
      render: (_: any, record: CreditNoteProduct) => {
        return (
          <p className="tableRowNameDesign">
            {record.quantity + " " + uomMap.get(record.productUOMId)?.name}
          </p>
        );
      },
    },
    {
      title: t("CreditNote.returnQuantity"),
      dataIndex: "returnQuantity",
      sorter: (a: any, b: any) =>
        a.returnQuantity.localeCompare(b.returnQuantity),
      showSorterTooltip: false,
      key: "returnQuantity",
      render: (_: any, record: CreditNoteProduct) => {
        return (
          <p className="tableRowNameDesign">
            {record.returnQuantity +
              " " +
              uomMap.get(record.productUOMId)?.name}
          </p>
        );
      },
    },
    {
      title: t("CreditNote.totalPrice"),
      dataIndex: "totalPrice",
      sorter: (a: any, b: any) => a.totalPrice.localeCompare(b.totalPrice),
      showSorterTooltip: false,
      key: "totalPrice",
      render: (_: any, record: CreditNoteProduct) => {
        const total =
          (record.price ?? 0) * (record.quantity ?? 0) - (record.discount ?? 0);
        return (
          <p className="tableRowNameDesign">{NumberThousandSeparator(total)}</p>
        );
      },
    },
  ];

  // *************************************************************************************
  // *** Listing table ***
  // *************************************************************************************
  const column = [
    // {
    //   title: t("Company"),
    //   dataIndex: "companyId",
    //   key: "companyId",
    //   width: 160,
    //   render: (record: any) => {
    //     return (
    //       <p className="tableRowNameDesign">{companyMap.get(record)?.name}</p>
    //     );
    //   },
    // },

    {
      title: t("CreditNote.creditNote") + " " + t("No"),
      dataIndex: "creditNoteNo",
      sorter: (a: any, b: any) => a.creditNoteNo.localeCompare(b.creditNoteNo),
      showSorterTooltip: false,
      key: "creditNoteNo",
      width: 110,
      render: (_: any, record: any) => {
        return <p className="tableRowNameDesign">{record.creditNoteNo}</p>;
      },
    },
    {
      title: t("CreditNote.creditNoteDate"),
      sorter: (a: any, b: any) => a.createdAt.localeCompare(b.createdAt),
      showSorterTooltip: false,
      key: "createdAt",
      width: 100,
      render: (_: any, record: any) => {
        return (
          <p className="tableRowNameDesign">
            {formateDateAndTime(record.createdAt)}
          </p>
        );
      },
    },

    {
      title: t("CreditNote.creditType"),
      dataIndex: "type",
      sorter: (a: any, b: any) => a.type.localeCompare(b.type),
      showSorterTooltip: false,
      key: "type",
      width: 80,
      render: (row: any) => {
        return (
          <p className="tableRowNameDesign">
            {row === "PRODUCT" ? "Product" : "Non-product"}
          </p>
        );
      },
    },
    //   {
    //     title: t("OutletCode"),
    //     dataIndex: "outletCode",
    //     onFilter: (value: string, record: any) => record.outletCode.indexOf(value) === 0,
    //     sorter: (a: any, b: any) => {
    //       const first = outletMap.get(a.outletId)?.outletCode || "";
    //       const second = outletMap.get(b.outletId)?.outletCode || "";

    //       return first.localeCompare(second);
    //     },
    //     showSorterTooltip: false,
    //     key: "outletCode",
    //     width: 120,
    //     render: (_: any, record: any) => {
    //       return <p className="tableRowNameDesign">{outletMap.get(record.outletId)?.outletCode}</p>;
    //     },
    //   },
    //   {
    //     title: t("Outlet"),
    //     onFilter: (value: string, record: any) => record.outletId.indexOf(value) === 0,
    //     sorter: (a: any, b: any) => {
    //       const first = outletMap.get(a.outletId)?.name || "";
    //       const second = outletMap.get(b.outletId)?.name || "";

    //       return first?.localeCompare(second);
    //     },
    //     showSorterTooltip: false,
    //     key: "outletId",
    //     width: 150,
    //     render: (_: any, record: any) => {
    //       return <p className="tableRowNameDesign">{outletMap.get(record.outletId)?.name}</p>;
    //     },
    //   },

    {
      title: t("CreditNote.nettAmount"),
      sorter: (a: any, b: any) =>
        (a.grossAmount - a.taxAmount)
          .toLocaleString(undefined, {
            maximumFractionDigits: 2,
            minimumFractionDigits: 2,
          })
          .localeCompare(
            (b.grossAmount - b.taxAmount).toLocaleString(undefined, {
              maximumFractionDigits: 2,
              minimumFractionDigits: 2,
            })
          ),
      showSorterTooltip: false,
      key: "name",
      width: 100,
      render: (_: any, record: CreditNote) => {
        return (
          <p className="tableRowNameDesign">
            RM{" "}
            {NumberThousandSeparator(
              (record.grossAmount ?? 0) +
                (record.taxAmount ?? 0) -
                (record.usedAmount ?? 0)
            )}
          </p>
        );
      },
    },
    {
      title: t("CreditNote.usedAmount"),
      sorter: (a: any, b: any) => a.usedAmount - b.usedAmount,
      showSorterTooltip: false,
      key: "usedAmount",
      width: 100,
      render: (_: any, record: any) => {
        return (
          <p className="tableRowNameDesign">
            RM {NumberThousandSeparator(record.usedAmount)}
          </p>
        );
      },
    },
    // {
    //   title: t("StaffCode"),
    //   sorter: (a: any, b: any) => a.staffId?.localeCompare(b.staffId),
    //   showSorterTooltip: false,
    //   key: "staffId",
    //   width: 100,
    //   render: (_: any, record: any) => {
    //     if (staffMap.get(record.staffId)) {
    //       let data = staffMap.get(record.staffId)?.staffCode;
    //       return <p className="tableRowNameDesign">{data}</p>;
    //     }
    //     return null;
    //   },
    // },
    {
      title: t("Common.status"),
      dataIndex: "status",
      key: "status",
      sorter: (a: any, b: any) => a.status.localeCompare(b.status),
      showSorterTooltip: false,
      width: 100,
      render: (_: any, record: any) => {
        return statusApproval(record);
      },
    },
    {
      // Action
      title: t("Common.action"),
      dataIndex: "action",
      key: "action",
      fixed: "right",
      width: 100,
      render: (_: any, record: CreditNote) => {
        const product = record.creditNoteProducts;
        let totals = 0;

        if (product) {
          product.map((item: CreditNoteProduct) => {
            if (item) {
              const subtotal = (item.quantity ?? 0) * (item?.price ?? 0);
              const taxAmount = subtotal * (item.taxRate ?? 0);
              const total = subtotal - (item.discount ?? 0) - taxAmount;
              totals = totals + total;
            }
          });
        }

        // const containsRorC = /[RU]/.test(userAccess?.policies?.["goodsReturn"] || "");
        // if ((containsRorC || isAdmin || isCompanyAdmin) && record.status !== "UNVERIFIED") {
        return (
          <div className="flex">
            <Button
              type="link"
              onClick={async () => {
                setIsModalOpen(true);
                // setModalData([record]);
                setCreditNoteProductData(record.creditNoteProducts ?? []);

                if (record.creditNoteDocument) {
                  let docs: { [key: string]: any } = {};
                  const data = record.creditNoteDocument;
                  await SignedUrl(record.creditNoteDocument).then(
                    (res: any) => {
                      //split and get the file name only from API value.
                      let fileName = data.split("/");
                      let preview: UploadFile = {
                        uid: data,
                        name: fileName.at(-1)!,
                        url: res,
                        fileName: fileName.at(-1),
                      };
                      creditNoteDetailForm.setFieldValue("document", preview);
                      docs.doDocument = preview;
                    }
                  );
                  setAllFiles(docs);
                }
                creditNoteDetailForm.setFieldsValue({
                  creditNoteNo: record.creditNoteNo,
                  creditNoteDate: formateDateAndTime(record.createdAt),
                  company: capitalize(companyMap.get(record.companyId)?.name),
                  invoiceNo: invoiceMap.get(record.invoiceId)?.invoiceNo,
                  type: record.type,
                  grossAmount: NumberThousandSeparator(record.grossAmount ?? 0),
                  taxAmount: NumberThousandSeparator(record.taxAmount ?? 0),
                  nettAmount: NumberThousandSeparator(
                    (record.grossAmount ?? 0) + (record.taxAmount ?? 0)
                  ),
                });
              }}
              className="flex items-center  text-xs ml-0 p-2"
            >
              <Tooltip title={t("Common.viewMore")}>
                <EyeOutlined style={{ color: "green" }} />
              </Tooltip>
            </Button>
          </div>
        );
        // }
        // return null;
      },
    },
  ];

  const rowSelection = {
    onChange: (selectedRowKeys: string[], selectedRows: []) => {
      setSelectedRowData(selectedRows);
      // setSelectedRowKeys(selectedRowKeys);
    },

    // getCheckboxProps: (record: { disabled: any; status: any }) => {
    // if (selectedRowData && selectedRowData.length > 0) {
    //   if (regexPattern.test(selectedRowData[0]?.status)) {
    //     return {
    //       disabled: !regexPattern.test(record.status),
    //     };
    //   }
    // }
    // return {
    //   disabled: !regexPattern.test(record.status),
    // };
    // },
  };
  const buttons = [
    {
      label: t("CreditNote.printCreditNote"),
      loading: pdfLoading,
      onClick: generatePDF,
      disabled: !(selectedRowData.length > 0),
    },
  ];

  // *************************************************************************************
  // *** Filter Modal ***
  // *************************************************************************************

  const filterFormOnfinish = (values: any) => {
    setModalFilter(values);
  };

  const searchCN = (values: any) => {
    //convert to empty string when no value is entered as default value is undefined.
    for (const key in values) {
      if (values[key] === undefined) {
        values[key] = "";
      }
    }

    // return true or false
    let isAnyKeyFilled = Object.keys(values).some(
      (key) => values[key] !== "" && values[key] !== undefined
    );
    let currentOutletId = localStorage.getItem("currentOutletId");
    const params =
      encodeParams({
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        outletId: currentOutletId,
        fuzzySearch: values.fuzzySearch,
        creditNoteNo: values.creditNoteNo || "",
        invoiceNo: values.invoiceNo || "",
        type: values.type || "",
        // outletId:
        //   values.outletCode && values.outletName
        //     ? [values.outletCode, values.outletName]
        //     : values.outletCode || values.outletName || "",
        productId:
          values.productSku && values.productName
            ? [values.productSku, values.productName]
            : values.productSku || values.productName || "",
        status: values.status,
      }) + "&sort=createdAt&sortOrder=-1";

    if (isAnyKeyFilled) {
      setCursor("0");
      setFilterSetting(params);
    }
  };

  const handleStatusMenuClick: MenuProps["onClick"] = ({ key }) => {
    const items = statusFilterOption1;
    setStatusKey(key);
    // Access the label property of the selected item
    const selectedLabel = items.find(
      (menuItem: any) => menuItem.key === key
    )?.label;
    if (selectedLabel) {
      setStatusValue(selectedLabel);
    }
  };

  const filterModal = () => {
    return (
      <div className="w-full">
        <Form
          onFinish={filterFormOnfinish}
          form={filterModalForm}
          className="w-full"
          layout="vertical"
        >
          <h1 className="font-bold text-base pb-4 hidden sm:flex">
            {t("Filter")}
          </h1>
          {/* First Row of Filter Input */}
          <Row className="filterBlockForm flex-col space-y-3">
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="creditNoteNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("CreditNote.creditNote") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={
                    t("Common.eg") +
                    " " +
                    t("CreditNote.creditNote") +
                    " " +
                    t("Common.no")
                  }
                  maxLength={30}
                />
              </Form.Item>
              <Form.Item
                name="type"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("CreditNote.creditType") +
                      "?"}
                  </p>
                }
              >
                <SelectInput
                  placeholder={
                    t("Common.eg") + " " + t("CreditNote.creditType")
                  }
                  options={creditTypeOption}
                />
              </Form.Item>
            </Row>
            {/* <Row className="flex flex-row gap-x-4">
              <Form.Item
                name="outletCode"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") + " " + t("OutletCode") + "?"}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={t("Common.eg") + " " + t("Placeholder.OutletCode")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="outletName"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") + " " + t("outletName") + "?"}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={t("Common.eg") + " " + t("Placeholder.OutletName")}
                  maxLength={100}
                />
              </Form.Item>
            </Row> */}
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="productSku"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("CreditNote.productSKU") +
                      "?"}
                  </p>
                }
              >
                <ComponentFilterSelect
                  placeholder={
                    t("Common.eg") + " " + t("CreditNote.productSKU")
                  }
                  dbName={"productCatalogues"}
                  customParams={{ status: "ACTIVE" }}
                  displayExpr={"sku"}
                  valueExpr={"id"}
                  userAccess={retailerAccess}
                />
              </Form.Item>
              <Form.Item
                name="productName"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("CreditNote.productName") +
                      "?"}
                  </p>
                }
              >
                <ComponentFilterSelect
                  placeholder={
                    t("Common.eg") + " " + t("CreditNote.productName")
                  }
                  dbName={"productCatalogues"}
                  customParams={{ status: "ACTIVE" }}
                  displayExpr={"name"}
                  valueExpr={"id"}
                  userAccess={retailerAccess}
                />
              </Form.Item>
            </Row>
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="invoiceNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("CreditNote.invoice") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={
                    t("Common.eg") +
                    " " +
                    t("CreditNote.invoice") +
                    " " +
                    t("Common.no")
                  }
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item name="" className="mb-0 flex-1">
                {/* <RangePickerInput /> */}
              </Form.Item>
            </Row>
          </Row>
          <Row className="flex pt-8 justify-between">
            <PrimaryButtonUI
              label={t("Common.resetAll")}
              onClick={() => {
                filterModalForm.resetFields();
                // setParamsFromLocalStorage(router.pathname, `status=${statusKey}`, "productFilter");
              }}
            />
            {/* <Row> */}
            <SecondaryButtonUI
              label={t("Common.cancel")}
              htmlType="reset"
              onClick={() => {
                setModalFilter({});
                setIsFilterModalOpen(false);
              }}
            />
            <PrimaryButtonUI
              label={t("Common.applyFilter")}
              htmlType="submit"
              onClick={() => {
                setIsFilterModalOpen(false);
              }}
            />
            {/* </Row> */}
          </Row>
        </Form>
      </div>
    );
  };

  const showContent = () => {
    return (
      <div>
        <BackButtonUI
          title={t("CreditNote.creditNote")}
          buttons={buttons}
        ></BackButtonUI>
        {/* <SearchBar buttons={buttons} /> */}
        <Row className="mb-4 w-full">
          <FilterFormComponent
            filterForm={filterForm}
            onDebouncedChange={(value) => {
              if (value === "") {
                filterForm.resetFields();
                setStatusKey("ALL");
                setStatusValue("All");
                setFuzzySearchFilter("");
                setModalFilter({});
                filterModalForm.resetFields();
                // setData([...fullData]);
                setShowClearFilter(false);
                setCursor(tempCursor);
                setFilterSetting("");
                localStorage.removeItem("cnFilter");
              } else {
                filterModalForm.resetFields();
                setModalFilter({});
                setFuzzySearchFilter(value);
              }
            }}
            fieldName={fieldName}
            clearButtonOnChange={() => {
              filterForm.resetFields();
              setStatusKey("ALL");
              setStatusValue("All");
              setFuzzySearchFilter("");
              setModalFilter({});
              filterModalForm.resetFields();
              // setData([...fullData]);
              setShowClearFilter(false);
              setCursor(tempCursor);
              setFilterSetting("");
              localStorage.removeItem("cnFilter");
            }}
            filterModalButtonOnClick={() => {
              setIsFilterModalOpen(true);
              filterForm.resetFields();
              setFuzzySearchFilter("");
            }}
            modalFilterValue={modalFilter}
            option={statusFilterOption1}
            handleStatusMenuClick={handleStatusMenuClick}
            clearFilterDisable={showClearFilter === true ? false : true}
            statusValue={statusValue}
            debounceValue={fuzzySearchFilter}
          ></FilterFormComponent>
        </Row>
        <ListingTableUI
          // EditableCell={EditableCell}
          bordered
          dataSource={data}
          columns={column}
          // rowClassName="editable-row"
          rowKey={(record: any) => record.id}
          cursor={cursor}
          loader={showButtonLoader}
          loading={tableLoading}
          pagination={false}
          rowSelection={rowSelection}
        />
      </div>
    );
  };

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => {}} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
      </Content>
      <Row className="justify-center w-full pt-4">
        {showScrollButton && (
          <FloatButton
            shape="circle"
            type="primary"
            onClick={() => {
              handleScrollToTop();
            }}
            style={{ right: 25, bottom: 120 }}
            icon={
              <ArrowUpOutlined
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              />
            }
          />
        )}
      </Row>
      <ModalUI
        title={t("CreditNote.creditNote") + " " + t("CreditNote.details")}
        width="80%"
        visible={isModalOpen}
        // onOk={handleOk}
        onCancel={() => setIsModalOpen(false)}
        content={showModal()}
      ></ModalUI>
      {isSmallScreen ? (
        <Drawer
          title="Filter"
          placement="bottom"
          closable={false}
          onClose={() => setIsFilterModalOpen(false)}
          open={isFilterModalOpen}
          height="80vh"
          className="rounded-t-lg"
        >
          {filterModal()}
        </Drawer>
      ) : (
        <ModalUI
          // title={"More Filter"}
          width="70%"
          className={"modalFilterBody"}
          visible={isFilterModalOpen}
          onOk={() => setIsFilterModalOpen(false)}
          onCancel={() => setIsFilterModalOpen(false)}
          content={filterModal()}
          title={""}
        ></ModalUI>
      )}
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default CreditNoteListing;
