import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import Header, { supportedLocales } from "../../components/header";
import { BackButtonUI, PrimaryButtonUI } from "../../components/buttonUI";
import {
  Col,
  DatePicker,
  Divider,
  Form,
  Modal,
  Row,
  Upload,
  UploadFile,
  UploadProps,
} from "antd";
import { ListingTableUI, MessageErrorUI } from "../../components/ui";
import {
  CreditNoteDocument,
  DebitNoteDocument,
  InvoiceDocument,
  Outlet,
  Payment,
  PaymentDetail,
  Retailer,
  SelectOption,
  StatementList,
} from "@/components/type";
import {
  DataSource,
  NumberThousandSeparator,
  PicSignedUrl,
  encodeParams,
  formateDate,
} from "@/stores/utilize";
import { FormTextInput, NumberInput, SelectInput } from "@/components/input";
import FpxIcon from "../../assets/icon/FPX.svg";
import PaymentIcon from "../../assets/logo/payment.svg";
import CreditNoteIcon from "../../assets/logo/creditNote.svg";
import { ClickableCard } from "@/components/card";
import { ModalUI } from "@/components/modalUI";
import { paymentMethodOption } from "@/components/config";
import { PlusOutlined } from "@ant-design/icons";
import { RcFile, UploadChangeParam } from "antd/es/upload";
import apiHelper from "../api/apiHelper";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import moment from "moment";
import Decimal from "decimal.js";
import AppFooter from "@/components/footer";
import _ from "lodash";

function ViewPayment() {
  const router = useRouter();
  const { t } = useTranslation("common");
  const [form] = Form.useForm();
  const [fpxPaymentForm] = Form.useForm();
  const [cashPaymentForm] = Form.useForm();
  const [chequePaymentForm] = Form.useForm();
  const [onlineTransferForm] = Form.useForm();
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  // ======================================================================
  // General Data - state()
  // ======================================================================
  const [data, setData] = useState<Payment>({});
  const [fullData, setFullData] = useState<StatementList[]>([]);
  const [paymentAmount, setPaymentAmount] = useState<PaymentAmount>({
    invoiceTotal: 0,
    debitNoteTotal: 0,
    creditNoteTotal: 0,
    totalPayment: 0,
    finalAmount: 0,
  });
  const [invoiceMap, setInvoiceMap] = useState(new Map());
  const [statementMap, setStatementMap] = useState(new Map());
  const [creditNoteMap, setCreditNoteMap] = useState(new Map());
  const [debitNoteMap, setDebitNoteMap] = useState(new Map());
  const [outletMap, setOutletMap] = useState(new Map());

  const [paymentData, setPaymentData] = useState<{
    [key: string]: PaymentDetail;
  }>({});

  const [isDnModalOpen, setIsDnModalOpen] = useState(false);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [bankListOption, setBankListOption] = useState<SelectOption[]>([]);
  const [paymentType, setPaymentType] = useState<string>("");
  // ======================================================================
  // Picture - state()
  // ======================================================================
  const [allFiles, setAllFiles] = useState<any[]>([]);
  const [previewImage, setPreviewImage] = useState<UploadFile[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewTitle, setPreviewTitle] = useState("");
  const [previewImageDisplay, setPreviewImageDisplay] = useState("");

  const headerItems = [
    {
      label: t("Header.home"),
      route: "/landing",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Header.dashboard"),
      route: "/profile/dashboard",
      className: "labelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Header.paymentHistory"),
      route: "/payment/paymentListing",
      className: "labelTextStyle",
    },
  ];

  interface PaymentAmount {
    invoiceTotal: number;
    debitNoteTotal: number;
    creditNoteTotal: number;
    totalPayment: number;
    finalAmount: number;
  }

  // ======================================================================
  // UseEffect - Handler
  // ======================================================================

  useEffect(() => {
    if (router.isReady && Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }
    }
  }, [Object.keys(useRetailerStore.getState()).length, router.isReady]);

  useEffect(() => {
    if (Object.keys(retailerAccess).length > 0) {
      getPaymentData();
    }
    getBankListOption();
  }, [retailerAccess]);

  useEffect(() => {
    if (isPaymentModalOpen === false) {
      setAllFiles([]);
      setPreviewImage([]);
    }
  }, [isPaymentModalOpen]);

  // *************************************************************************************
  // *** GET API ***
  // *************************************************************************************

  const getPaymentData = () => {
    setIsLoading(true);
    apiHelper
      .GET("payments?id=" + router.query.id)
      ?.then((res: any) => {
        if (res.items !== null) {
          const data: Payment = res.items[0];
          setData(data);
          if (data?.invoiceDocuments && data?.invoiceDocuments?.length > 0) {
            const invoiceData: InvoiceDocument[] = data.invoiceDocuments ?? [];
            const objectMap = invoiceData.reduce(
              (accumulator: any, current: InvoiceDocument) => {
                accumulator["invoiceId"] = accumulator["invoiceId"] || [];
                if (
                  current.invoiceId &&
                  !invoiceMap.has(current.invoiceId) &&
                  !accumulator["invoiceId"].includes(current.invoiceId)
                ) {
                  accumulator["invoiceId"].push(current.invoiceId ?? "");
                }

                accumulator["statementId"] = accumulator["statementId"] || [];
                if (
                  current.statementId &&
                  !statementMap.has(current.statementId) &&
                  !accumulator["statementId"].includes(current.statementId)
                ) {
                  accumulator["statementId"].push(current.statementId ?? "");
                }
                return accumulator;
              },
              {}
            );
            getInvoice(objectMap["invoiceId"]);
            getStatement(objectMap["statementId"]);
          }

          if (
            data?.debitNoteDocuments &&
            data?.debitNoteDocuments?.length > 0
          ) {
            const debitNoteDocument: DebitNoteDocument[] =
              data.debitNoteDocuments ?? [];
            const objectMap = debitNoteDocument.reduce(
              (accumulator: any, current: DebitNoteDocument) => {
                accumulator["debitNoteId"] = accumulator["debitNoteId"] || [];
                if (
                  current.debitNoteId &&
                  !debitNoteMap.has(current.debitNoteId) &&
                  !accumulator["debitNoteId"].includes(current.debitNoteId)
                ) {
                  accumulator["debitNoteId"].push(current.debitNoteId ?? "");
                }
                return accumulator;
              },
              {}
            );
            getDebitNote(objectMap["debitNoteId"]);
          }

          if (
            data?.creditNoteDocuments &&
            data?.creditNoteDocuments?.length > 0
          ) {
            const creditNoteData: CreditNoteDocument[] =
              data.creditNoteDocuments ?? [];
            const objectMap = creditNoteData.reduce(
              (accumulator: any, current: CreditNoteDocument) => {
                accumulator["creditNoteId"] = accumulator["creditNoteId"] || [];
                if (
                  current.creditNoteId &&
                  !creditNoteMap.has(current.creditNoteId) &&
                  !accumulator["creditNoteId"].includes(current.creditNoteId)
                ) {
                  accumulator["creditNoteId"].push(current.creditNoteId ?? "");
                }
                return accumulator;
              },
              {}
            );
            getCreditNote(objectMap["creditNoteId"]);
          }

          let invoiceTotal = 0;
          if (data?.invoiceDocuments && data?.invoiceDocuments?.length > 0) {
            data.invoiceDocuments.forEach((item: InvoiceDocument) => {
              invoiceTotal += item.amount ?? 0;
            });
          }

          let debitNoteTotal = 0;
          if (
            data?.debitNoteDocuments &&
            data?.debitNoteDocuments?.length > 0
          ) {
            data.debitNoteDocuments.forEach((item: DebitNoteDocument) => {
              debitNoteTotal += item.amount ?? 0;
            });
          }

          let creditNoteTotal = 0;
          if (
            data?.creditNoteDocuments &&
            data?.creditNoteDocuments?.length > 0
          ) {
            data.creditNoteDocuments.forEach((item: CreditNoteDocument) => {
              creditNoteTotal += item.amount ?? 0;
            });
          }

          let totalPayment = 0;
          if (data?.paymentDetails && data?.paymentDetails?.length > 0) {
            data.paymentDetails.forEach((item: PaymentDetail) => {
              totalPayment += item.amount ?? 0;
            });
          }

          (invoiceTotal = parseFloat(invoiceTotal.toFixed(2))),
            (debitNoteTotal = parseFloat(debitNoteTotal.toFixed(2))),
            (creditNoteTotal = parseFloat(creditNoteTotal.toFixed(2))),
            (totalPayment = parseFloat(totalPayment.toFixed(2)));

          const subTotalAmount: PaymentAmount = {
            invoiceTotal: invoiceTotal,
            debitNoteTotal: debitNoteTotal,
            creditNoteTotal: creditNoteTotal,
            totalPayment: totalPayment,
            finalAmount:
              invoiceTotal + debitNoteTotal - creditNoteTotal - totalPayment,
          };
          setPaymentAmount(subTotalAmount);

          const updatedPaymentData: { [key: string]: PaymentDetail } = {};
          if (data?.paymentDetails && data?.paymentDetails?.length > 0) {
            data.paymentDetails.forEach(async (paymentItem: any) => {
              paymentItem.referenceDateMoment =
                paymentItem.referenceDate &&
                  paymentItem.referenceDate !== "0001-01-01T00:00:00Z"
                  ? moment(paymentItem.referenceDate)
                  : moment(data?.createdAt ?? "");

              const imageObj: Record<string, any[]> = {};
              if (paymentItem?.receipt?.length) {
                await Promise?.all(
                  paymentItem.receipt?.map(async (pic: any) => {
                    if (pic) {
                      const res: string = (await PicSignedUrl(pic)) as string;
                      const file = {
                        uid: `${pic}`,
                        name: `${pic}`,
                        url: res,
                        thumbUrl: res,
                      };
                      if (!imageObj[paymentItem.type]) {
                        imageObj[paymentItem.type] = [];
                      }

                      imageObj[paymentItem.type].push(file);
                    }
                  })
                );
              }

              paymentItem.receiptImage = imageObj[paymentItem.type];
              updatedPaymentData[paymentItem.type || ""] = paymentItem;
              setPaymentData(updatedPaymentData);
            });
          }

          getOutlet(data.outletId ?? "");
          setIsLoading(false);
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
        setIsLoading(false);
      });
  };

  const getInvoice = async (ids: string[] = []) => {
    if (ids.length === 0) return;
    const dataSource = new DataSource(
      "invoices",
      encodeParams({ id: ids }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setInvoiceMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getStatement = async (ids: string[] = []) => {
    if (ids.length === 0) return;
    const dataSource = new DataSource(
      "statements",
      encodeParams({ id: ids }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setStatementMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getCreditNote = async (ids: string[] = []) => {
    if (ids.length === 0) return;
    const dataSource = new DataSource(
      "creditNotes",
      encodeParams({ id: ids }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setCreditNoteMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getDebitNote = async (ids: string[] = []) => {
    if (ids.length === 0) return;
    const dataSource = new DataSource(
      "debitNotes",
      encodeParams({ id: ids }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setDebitNoteMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getOutlet = async (id: string) => {
    if (!id) return;
    const dataSource = new DataSource(
      "outlets",
      encodeParams({ id: id }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setOutletMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getBankListOption = () => {
    apiHelper.GET("kit/bankList").then((res: any) => {
      let bankList: SelectOption[] = [];
      if (res && res.length) {
        res.forEach((bankName: string) => {
          bankList.push({
            value: bankName,
            label: bankName,
          });
        });
        setBankListOption(bankList);
      }
    });
  };

  // *************************************************************************************
  // *** Function ***
  // *************************************************************************************

  const handleImageDisplay = (type: string) => {
    if (
      paymentData[type] &&
      "receiptImage" in paymentData[type] &&
      paymentData[type].receiptImage
    ) {
      setAllFiles(paymentData[type]?.receiptImage ?? []);
      setPreviewImage(paymentData[type]?.receiptImage ?? []);
    }
  };

  // const handleFPXPayment = async () => {
  //     try {
  //         const paymentData = {
  //             PaymentID: "800",
  //             BoxID: "001",
  //             UserID: "USER01",
  //             CashierID: "1",
  //             InvNo: "TESTORDER1",
  //             CurrencyID: "MYR",
  //             TotalAmt: 15.5,
  //             DiscAmt: 0,
  //             Remark: "Test Order",
  //             BuyerBankId: "",
  //             BuyerEmail: "",
  //             invDetail: [
  //                 {
  //                     SKU: "9001001",
  //                     Qty: 1,
  //                 },
  //                 // Add more items if needed
  //             ],
  //         };

  //         const response = await fetch(paymentApiEndpoint, {
  //             method: "POST",
  //             headers: {
  //                 "Content-Type": "application/json",
  //                 // Add any other necessary headers
  //             },
  //             body: JSON.stringify(paymentData),
  //         });
  //         console.log("response", response);
  //         if (response.ok) {
  //             const responseData = await response.json();
  //             console.log("Payment response:", responseData);

  //             if (responseData.Status === "1" && responseData.Data) {
  //                 // Redirect the user to the FPX payment processing URL for successful payment
  //                 window.location.href = responseData.Data;
  //             } else {
  //                 console.error("Payment failed:", responseData.Message);
  //                 // Handle other status conditions or failure cases
  //             }
  //         } else {
  //             console.error("Payment failed:", response.statusText);
  //             // Handle error response
  //         }
  //     } catch (error) {
  //         console.error("Payment failed:", error);
  //         // Handle fetch error
  //     }
  // };

  // *************************************************************************************
  // *** Images Props and function ***
  // *************************************************************************************

  //images upload
  const getBase64 = (file: RcFile): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }

    setPreviewImageDisplay(file.url || (file.preview as string));
    setPreviewOpen(true);
    setPreviewTitle(
      file.name || file.url!.substring(file.url!.lastIndexOf("/") + 1)
    );
  };

  const handleChange = (info: UploadChangeParam<UploadFile<any>>) => {
    if (info.fileList.length > 0) {
      if (info.fileList.at(-1) !== undefined) {
        let pic: any[] = allFiles !== null ? allFiles : [];
        let preview = previewImage !== null ? previewImage : [];
        let file = info.fileList.at(-1);
        if (file !== undefined) {
          let uid = file.uid;
          let checkExist = pic.filter((item: any) => item.uid === uid);
          if (checkExist.length === 0) {
            file.status = "done";
            pic.push(file.originFileObj);
            preview.push(file);
            setPreviewImage([...preview]);
            setAllFiles([...pic]);
          }
        }
      }
    }
  };

  const handleRemove = (info: UploadFile<any>) => {
    let pic = allFiles.filter((item: any) => item.uid !== info.uid);
    let preview = previewImage.filter((item: any) => item.uid !== info.uid);
    setPreviewImage(preview);
    setAllFiles(pic);
  };

  const handleBeforeUpload = (file: {
    type: string;
    name: any;
    size: number;
  }) => {
    if (
      file.type !== "image/png" &&
      file.type !== "image/jpg" &&
      file.type !== "image/jpeg" &&
      file.type !== "application/pdf"
    ) {
      MessageErrorUI(
        `${file.name} is an invalid file format. Please change the file extension to either .pdf, .png, .jpg, .jpeg.`
      );
      return Upload.LIST_IGNORE;
    } else if (file.size > 5242880) {
      MessageErrorUI(
        `${file.name} is too large. Please upload another document that is smaller than 5MB.`
      );
      return Upload.LIST_IGNORE;
    } else {
      return false;
    }
  };

  //Upload props
  const props: UploadProps = {
    name: "file",
    multiple: true,
    maxCount: 8,
    listType: "picture-card",
    fileList: previewImage,
    onPreview: handlePreview,
    accept: "image/png, image/jpeg, image/jpg",
    showUploadList: {
      showPreviewIcon: true,
    },
    beforeUpload: handleBeforeUpload,
    onChange: handleChange,
    onRemove: handleRemove,
  };

  // *************************************************************************************
  // *** Column ***
  // *************************************************************************************
  // Invoice Column
  const invColumn = [
    statementMap.size === 0
      ? null
      : {
        title: t("Payment.statement") + " " + t("Common.no"),
        dataIndex: "statementId",
        key: "statementId",
        render: (record: string) => {
          return (
            <p className="tableRowNameDesign">
              {statementMap.get(record)?.statementNo}
            </p>
          );
        },
      },
    {
      title: t("Payment.invoice") + " " + t("Common.no"),
      dataIndex: "invoiceId",
      key: "invoiceId",
      render: (_: any, record: InvoiceDocument) => {
        return (
          <p className="tableRowNameDesign">
            {invoiceMap.get(record.invoiceId)?.invoiceNo}
          </p>
        );
      },
    },
    {
      title: t("Payment.invoiceDate"),
      dataIndex: "invoiceDate",
      key: "invoiceDate",
      render: (_: any, record: InvoiceDocument) => {
        const date = invoiceMap.get(record.invoiceId)?.invoiceDate ?? "";
        const formattedDate = date ? formateDate(date) : "";
        return <p className="tableRowNameDesign">{formattedDate}</p>;
      },
    },
    {
      title: t("Payment.totalProduct"),
      dataIndex: "totalProduct",
      key: "totalProduct",
      render: (_: any, record: InvoiceDocument) => {
        const products =
          invoiceMap.get(record.invoiceId)?.invoiceProducts ?? [];
        return <p className="tableRowNameDesign">{products.length}</p>;
      },
    },
    {
      title: t("Payment.invoiceAmount"),
      dataIndex: "invoiceAmount",
      key: "invoiceAmount",
      render: (_: any, record: InvoiceDocument) => {
        const netAmount = invoiceMap.get(record.invoiceId)?.netAmount;
        return (
          <p className="tableRowNameDesign">
            {"RM " + NumberThousandSeparator(netAmount)}
          </p>
        );
      },
    },
    {
      title: t("Payment.remainingAmount"),
      dataIndex: "remainingAmount",
      key: "remainingAmount",
      render: (_: any, record: InvoiceDocument) => {
        const netAmount = invoiceMap.get(record.invoiceId)?.netAmount;
        const paidAmount = invoiceMap.get(record.invoiceId)?.paidAmount;
        const remainingAmount = netAmount - paidAmount;
        return (
          <p className="tableRowNameDesign">
            {"RM " + NumberThousandSeparator(remainingAmount)}
          </p>
        );
      },
    },
    {
      width: 120,
      title: t("Payment.amountToPaid"),
      dataIndex: "toPaidAmount",
      key: "toPaidAmount",
      render: (_: any, record: InvoiceDocument) => {
        return (
          <p className="tableRowNameDesign">
            {"RM " + NumberThousandSeparator(record.amount ?? 0)}
          </p>
        );
      },
    },
  ].filter(Boolean);

  // Debit Note Column
  const dnColumn = [
    {
      title: t("Payment.debitNote") + " " + t("Common.no"),
      dataIndex: "debitNoteNo",
      key: "debitNoteNo",
      render: (record: string) => {
        return (
          <p className="tableRowNameDesign">
            {record}
          </p>
        );
      },
    },
    {
      title: t("Payment.debitNoteDate"),
      dataIndex: "debitNoteId",
      key: "debitNoteDate",
      render: (record: string) => {
        const date = debitNoteMap.get(record)?.debitNoteDate ?? "";
        const formattedDate = date ? formateDate(date) : "";

        return (
          <p className="tableRowNameDesign">
            {formattedDate}
          </p>
        );
      },
    },
    {
      title: t("Payment.type"),
      dataIndex: "debitNoteId",
      key: "type",
      render: (record: string) => {
        const type = debitNoteMap.get(record)?.type ?? "";
        const formattedText = type && type === "PRODUCT" ? "Product" : type === "NONPRODUCT" ? "Non-Product" : "Purchase Return";

        return (
          <p className="tableRowNameDesign">
            {formattedText}
          </p>
        );
      },
    },
    {
      title: t("Payment.netAmount"),
      dataIndex: "debitNoteId",
      key: "debitNoteNetAmount",
      render: (record: string) => {
        const grossAmount = debitNoteMap.get(record)?.grossAmount ?? "";

        return (
          <p className="tableRowNameDesign">
            {grossAmount}
          </p>
        );
      },
    },
    {
      title: t("Payment.remainingAmount"),
      dataIndex: "debitNoteId",
      key: "debitNoteOutstandingAmount",
      render: (record: string) => {
        const debitNote = debitNoteMap.get(record) ?? {};

        let grossAmount: Decimal = new Decimal(debitNote?.grossAmount ?? 0);
        let paidAmount: Decimal = new Decimal(debitNote?.paidAmount ?? 0);
        let processingAmount: Decimal = new Decimal(debitNote?.processingAmount ?? 0);

        const remainingAmount = grossAmount.minus(paidAmount).minus(processingAmount)

        return (
          <p className="tableRowNameDesign">
            {parseFloat(remainingAmount.toFixed(2))}
          </p>
        );
      },
    },
    !isDnModalOpen && {
      title: t("Payment.amountToPaid"),
      dataIndex: "amount",
      key: "amount",
      render: (value: any) => {
        return (
          <p className="tableRowNameDesign">
            {parseFloat(value.toFixed(2))}
          </p>
        );
        // const handleChange = debounce((value: any) => {
        //     const hasDn = selectedRowDnKeys.includes(record.id);
        //     if (!hasDn) {
        //         setSelectedRowDnKeys([...selectedRowDnKeys, record.id]);
        //         setSelectedRowDnData([...selectedRowDnData, record]);
        //     }
        //     if (value) {
        //         const tempData = cloneDeep(data);
        //         const selectedDnIndex = tempData.reMapDebitNote.findIndex(
        //             (val: any) => val.id === record.id
        //         );
        //         const selectedDn = tempData.reMapDebitNote.filter(
        //             (val: any) => val.id === record.id
        //         );
        //         if (selectedDnIndex !== -1) {
        //             selectedDn.map((dn: any) => {
        //                 dn.amount = value;
        //             });
        //         }
        //         tempData.reMapDebitNote[selectedDnIndex] = selectedDn[0];
        //         setData(tempData);
        //     }
        // }, 300);
        // return (
        //     <Form.Item
        //         name={"amount" + record.id}
        //         rules={[
        //             {
        //                 required: true,
        //                 message: `Please Input Amount!`,
        //             },
        //             () => ({
        //                 validator(_, value) {
        //                     const remainingAmount = record.remainingAmount; // Adjust this based on your actual data structure
        //                     if (value <= remainingAmount) {
        //                         return Promise.resolve();
        //                     }
        //                     return Promise.reject(
        //                         "Amount must not exceed the remaining amount"
        //                     );
        //                 },
        //             }),
        //         ]}
        //     >
        //         <FormNumberInput
        //             className="flex w-full"
        //             placeholder="Amount"
        //             onChange={handleChange}
        //         />
        //     </Form.Item>
        // );
      },
    },
  ].filter(Boolean);

  const cnColumn = [
    {
      title: t("Payment.creditNote") + " " + t("Common.no"),
      dataIndex: "creditNoteNo",
      key: "creditNoteNo",
      render: (record: string) => {
        return (
          <p className="tableRowNameDesign">
            {record}
          </p>
        );
      },
    },
    {
      title: t("Payment.creditNoteDate"),
      dataIndex: "creditNoteId",
      key: "creditNoteDate",
      render: (record: string) => {
        const date = creditNoteMap.get(record)?.creditNoteDate ?? "";
        const formattedDate = date ? formateDate(date) : "";

        return (
          <p className="tableRowNameDesign">
            {formattedDate}
          </p>
        );
      },
    },
    {
      title: t("Payment.type"),
      dataIndex: "creditNoteId",
      key: "type",
      render: (record: string) => {
        const type = creditNoteMap.get(record)?.type ?? "";
        const formattedText = type && type === "PRODUCT" ? "Product" : type === "NONPRODUCT" ? "Non-Product" : "Purchase Return";

        return (
          <p className="tableRowNameDesign">
            {formattedText}
          </p>
        );
      },
    },
    {
      title: t("Payment.invoice") + " " + t("Common.no"),
      dataIndex: "creditNoteId",
      key: "invoiceNo",
      render: (_: any, record: any) => {
        const name = creditNoteMap.get(record.creditNoteId)?.invoiceNo;
        return name ? <p className="tableRowNameDesign text-center">{name}</p> : <p className="tableRowNameDesign text-center">—</p>;
      },
    },
    {
      title: t("Payment.netAmount"),
      dataIndex: "creditNoteId",
      key: "netAmount",
      render: (record: string) => {
        const grossAmount = creditNoteMap.get(record)?.grossAmount ?? "";

        return (
          <p className="tableRowNameDesign">
            {grossAmount}
          </p>
        );
      },
    },
    {
      title: t("Payment.remainingAmount"),
      dataIndex: "creditNoteId",
      key: "remainingAmount",
      render: (record: string) => {
        const creditNote = creditNoteMap.get(record) ?? {};

        let grossAmount: Decimal = new Decimal(creditNote?.grossAmount ?? 0);
        let usedAmount: Decimal = new Decimal(creditNote?.usedAmount ?? 0);
        let processingAmount: Decimal = new Decimal(creditNote?.processingAmount ?? 0);

        const remainingAmount = grossAmount.minus(usedAmount).minus(processingAmount)

        return (
          <p className="tableRowNameDesign">
            {parseFloat(remainingAmount.toFixed(2))}
          </p>
        );
      },
    },
    {
      title: t("Payment.amountToPaid"),
      dataIndex: "amount",
      key: "amount",
      render: (value: any) => {
        return (
          <p className="tableRowNameDesign">
            {parseFloat(value.toFixed(2))}
          </p>
        );

        // const handleChange = debounce((value: any) => {
        //     const hasCn = selectedRowCnKeys.includes(record.id);
        //     if (!hasCn) {
        //         setSelectedRowCnKeys([...selectedRowCnKeys, record.id]);
        //         setSelectedRowCnData([...selectedRowCnData, record]);
        //     }
        //     if (value) {
        //         const tempData = cloneDeep(data);
        //         const selectedCnIndex = tempData.reMapCreditNote.findIndex(
        //             (val: any) => val.id === record.id
        //         );
        //         const selectedCn = tempData.reMapCreditNote.filter(
        //             (val: any) => val.id === record.id
        //         );
        //         if (selectedCnIndex !== -1) {
        //             selectedCn.map((cn: any) => {
        //                 cn.amount = value;
        //             });
        //         }
        //         tempData.reMapCreditNote[selectedCnIndex] = selectedCn[0];
        //         setData(tempData);
        //     }
        // }, 300);
        // return (
        //     <Form.Item
        //         name={"amount" + record.id}
        //         rules={[
        //             {
        //                 required: true,
        //                 message: `Please Input Amount!`,
        //             },
        //             () => ({
        //                 validator(_, value) {
        //                     const remainingAmount = record.remainingAmount; // Adjust this based on your actual data structure
        //                     if (value <= remainingAmount) {
        //                         return Promise.resolve();
        //                     }
        //                     return Promise.reject(
        //                         "Amount must not exceed the remaining amount"
        //                     );
        //                 },
        //             }),
        //         ]}
        //     >
        //         <FormNumberInput
        //             className="flex w-full"
        //             placeholder="Amount"
        //             onChange={handleChange}
        //         />
        //     </Form.Item>
        // );
      },
    },
  ];

  const handleModalClose = () => {
    setIsPaymentModalOpen(false);
  };

  const handleCancel = () => setPreviewOpen(false);

  const paymentModal = () => {
    const formSelected =
      paymentType === "FPX"
        ? fpxPaymentForm
        : paymentType === "CASH"
          ? cashPaymentForm
          : paymentType === "CHEQUE"
            ? chequePaymentForm
            : onlineTransferForm;
    return (
      <ModalUI
        title={
          paymentMethodOption.find((item) => item.value === paymentType)
            ?.label + " Modal"
        }
        width="60%"
        visible={isPaymentModalOpen}
        onOk={handleModalClose}
        onCancel={handleModalClose}
        destroyOnClose={true}
        content={
          <Form
            disabled
            onFinish={() => { }}
            className="w-full "
            form={formSelected}
            layout="vertical"
            scrollToFirstError
          >
            <div className="flex flex-col bg-white w-full rounded-[12px] p-2 space-y-6">
              <Col className="w-full">
                <Form.Item
                  name="type"
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Payment.type")}
                    </p>
                  }
                >
                  <SelectInput options={paymentMethodOption} />
                </Form.Item>
                {paymentType !== "CASH" ? (
                  <Form.Item
                    className="mb-1"
                    name="bankName"
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Payment.bankName")}
                      </p>
                    }
                  >
                    <SelectInput options={bankListOption} />
                  </Form.Item>
                ) : null}
                <Form.Item
                  className="mb-1"
                  name="amount"
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Payment.paymentAmount")}
                    </p>
                  }
                >
                  <NumberInput
                    min={0}
                    max={*********}
                    value={0.0}
                    defaultValue={0.0}
                    precision={5}
                  />
                </Form.Item>
                <Form.Item
                  className="mb-1"
                  name="referenceNo"
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Payment.referenceNo")}
                    </p>
                  }
                >
                  <FormTextInput
                    placeholder={t("Payment.referenceNo")}
                    maxLength={50}
                  />
                </Form.Item>
                <Form.Item
                  className="mb-1"
                  name="referenceDateMoment"
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Payment.referenceDate")}
                    </p>
                  }
                >
                  <DatePicker className="rounded-lg min-w-[120px] w-full" />
                </Form.Item>
                <Form.Item
                  className="mb-1"
                  name="receiptImage"
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Payment.receipt")}
                    </p>
                  }
                >
                  <Upload {...props} className="custom-upload">
                    <div>
                      <PlusOutlined />
                      <p>{t("Payment.upload")}</p>
                    </div>
                  </Upload>
                  <Modal
                    open={previewOpen}
                    title={previewTitle}
                    footer={null}
                    onCancel={handleCancel}
                  >
                    <img
                      alt="example"
                      style={{ width: "100%" }}
                      src={previewImageDisplay}
                    />
                  </Modal>
                </Form.Item>
              </Col>
            </div>
          </Form>
        }
      ></ModalUI>
    );
  };

  // *************************************************************************************
  // *** Filter Modal ***
  // *************************************************************************************

  const showContent = () => {
    const keys = Object.keys(paymentData);
    const formattedKeys = keys
      .map(
        (key) => paymentMethodOption.find((item) => item.value === key)?.label
      )
      .join(", ");

    let totalPaymentMethodAmount = 0;
    Object.keys(paymentData).forEach((key) => {
      totalPaymentMethodAmount += paymentData[key]?.amount ?? 0;
    });

    let paid: Decimal = new Decimal(0);
    paid = new Decimal(paymentAmount.totalPayment).minus(
      new Decimal(totalPaymentMethodAmount)
    );

    return (
      <div>
        <Row className="w-full flex items-center pb-5">
          <h1 className="font-bold pt-4 pb-4 text-[22px]">
            {t("Payment.paymentDetail") + " (" + data?.receiptNo + ")"}
          </h1>
        </Row>
        <Row className="pb-8">
          <div>
            <p className="text-xl font-bold mb-3"> Invoice</p>
          </div>
          <Form form={form} className="w-full">
            <ListingTableUI
              dataSource={data.invoiceDocuments ?? []}
              columns={invColumn}
              rowKey="id"
              subtotal={
                paymentAmount.invoiceTotal !== 0
                  ? NumberThousandSeparator(paymentAmount.invoiceTotal)
                  : "0.00"
              }
              endMessage={""}
            />
          </Form>
        </Row>
        <Row className="pb-8">
          <Row className="flex flex-row justify-between w-full">
            <p className="text-xl font-bold mb-3">Debit Note</p>
          </Row>
          <Form form={form} className="w-full">
            <ListingTableUI
              dataSource={data.debitNoteDocuments ?? []}
              columns={dnColumn}
              rowKey="id"
              subtotal={
                paymentAmount.debitNoteTotal !== 0
                  ? NumberThousandSeparator(paymentAmount.debitNoteTotal)
                  : "0.00"
              }
              endMessage={""}
            />
          </Form>
        </Row>
        <Row className="pb-8">
          <Row className="flex flex-row justify-between w-full">
            <p className="text-xl font-bold mb-3">Credit Note</p>
          </Row>
          <Form form={form} className="w-full">
            <ListingTableUI
              dataSource={data.creditNoteDocuments ?? []}
              columns={cnColumn}
              rowKey="id"
              subtotal={
                paymentAmount.creditNoteTotal !== 0
                  ? NumberThousandSeparator(paymentAmount.creditNoteTotal)
                  : "0.00"
              }
              endMessage={""}
            />
          </Form>
        </Row>
        <Row className="flex bg-white pb-8">
          <div className="flex-1">
            <div className="text-xl font-bold p-3">Payment Summary</div>
            <div className="flex flex-row justify-between p-5">
              <Row>Total Invoice</Row>
              <Row>
                RM {NumberThousandSeparator(paymentAmount.invoiceTotal)}
              </Row>
            </div>
            <div className="flex flex-row justify-between p-5">
              <Row>Total Debit Note</Row>
              <Row>
                RM {NumberThousandSeparator(paymentAmount.debitNoteTotal)}
              </Row>
            </div>
            <div className="flex flex-row justify-between p-5">
              <Row>Total Credit Note</Row>
              <Row className="text-red-600">
                - RM {NumberThousandSeparator(paymentAmount.creditNoteTotal)}
              </Row>
            </div>
            <div className="flex flex-row justify-between p-5">
              <Row>
                Total Payment {formattedKeys ? `(${formattedKeys})` : null}{" "}
              </Row>
              <Row className="text-red-600">
                - RM {NumberThousandSeparator(paymentAmount.totalPayment)}
              </Row>
            </div>

            <Divider type="horizontal" className="mb-4" />
            <div className="font-bold p-3 pb-8 flex flex-row justify-between">
              <Row className="text-xl">Total Amount</Row>
              <Row className="text-xl">
                RM {NumberThousandSeparator(paymentAmount.finalAmount)}
              </Row>
            </div>
          </div>
        </Row>
        <Row className="flex flex-row items-center pt-8 justify-between">
          <div>
            <div className="text-xl font-bold flex flex-row pr-5">
              Payment Method
            </div>
            <div className="gap-4 flex flex-wrap pt-2">
              <ClickableCard
                isClicked={paymentData["FPX"] ? true : false}
                title={t("Payment.FPXPayment")}
                imageComponent={
                  <FpxIcon
                    width="40"
                    height="50"
                    viewBox="0 0 90 80"
                    src={FpxIcon.src}
                  />
                }
                onClick={() => {
                  handleImageDisplay("FPX");
                  setIsPaymentModalOpen(true);
                  setPaymentType("FPX");
                  fpxPaymentForm.setFieldsValue(paymentData["FPX"]);
                }}
              />
              <ClickableCard
                isClicked={paymentData["CASH"] ? true : false}
                title={t("Payment.cash")}
                imageComponent={
                  <CreditNoteIcon
                    width="40"
                    height="50"
                    viewBox="0 0 90 80"
                    src={CreditNoteIcon.src}
                  />
                }
                onClick={() => {
                  handleImageDisplay("CASH");
                  setIsPaymentModalOpen(true);
                  setPaymentType("CASH");
                  cashPaymentForm.setFieldsValue(paymentData["CASH"]);
                }}
              />
              <ClickableCard
                isClicked={paymentData["CHEQUE"] ? true : false}
                title={t("Payment.cheque")}
                imageComponent={
                  <PaymentIcon
                    width="40"
                    height="50"
                    viewBox="0 0 90 80"
                    src={PaymentIcon.src}
                  />
                }
                onClick={() => {
                  handleImageDisplay("CHEQUE");
                  setIsPaymentModalOpen(true);
                  setPaymentType("CHEQUE");
                  chequePaymentForm.setFieldsValue(paymentData["CHEQUE"]);
                }}
              />
              <ClickableCard
                isClicked={paymentData["ONLINETRANSFER"] ? true : false}
                title={t("Payment.onlineTransfer")}
                imageComponent={
                  <PaymentIcon
                    width="40"
                    height="50"
                    viewBox="0 0 90 80"
                    src={PaymentIcon.src}
                  />
                }
                onClick={() => {
                  handleImageDisplay("ONLINETRANSFER");
                  setIsPaymentModalOpen(true);
                  setPaymentType("ONLINETRANSFER");
                  onlineTransferForm.setFieldsValue(
                    paymentData["ONLINETRANSFER"]
                  );
                }}
              />
            </div>
          </div>
        </Row>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-bgOrange min-w-full">
      <Header items={headerItems} hasSearch={false} values={() => { }} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
      </Content>
      {paymentModal()}
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default ViewPayment;
