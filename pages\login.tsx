import React, { useState, useEffect } from "react";
import { isValidPhoneNumber } from "react-phone-number-input";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { Form, Row } from "antd";
import { useRouter } from "next/router";
import {
  CheckboxInput,
  PasswordInput,
  PhoneNumberInput,
} from "../components/input";
import { MessageErrorUI, MessageSuccessUI } from "../components/ui";
import { PrimaryButtonUI } from "../components/buttonUI";
import { clearUserInfoAuth, getOutletData, getRetailerData } from "../stores/authContext";
import hashPassword from "../stores/hashPassword";
import apiHelper from "../pages/api/apiHelper";
import Logo from "../assets/logo/Neuroforce.svg";
import { DataSource, encodeParams } from "@/stores/utilize";
import useRetailerStore from "@/stores/store";
import { supportedLocales } from "@/components/header";

function RetailerLogin() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [checked, setChecked] = useState(false);
  const [form] = Form.useForm();
  const [buttonDisable, setButtonDisable] = useState(true);
  const [phoneNumber, setPhoneNumber] = useState("");
  const [password, setPassword] = useState("");
  //If logged in, directly log in to landing page
  useEffect(() => {
    if (
      localStorage.getItem("accessToken") &&
      localStorage.getItem("remember") === "true"
    ) {
      sessionStorage.setItem(
        "accessToken",
        localStorage.getItem("accessToken") || ""
      );
      router.push("/landing");
    } else {
      localStorage.removeItem("accessToken");
      localStorage.removeItem("remember");
      // sessionStorage.clear();
    }
  }, []);

  //Check input field
  useEffect(() => {
    if (phoneNumber) {
      if (isValidPhoneNumber(phoneNumber) && password.length > 5) {
        setButtonDisable(false);
      } else {
        setButtonDisable(true);
      }
    } else {
    }
  }, [phoneNumber, password]);

  //get outlet latest data
  const getOutletByToken = async (ids: string | string[] = []) => {
    try {
      const dataSource = new DataSource(
        "outlets",
        encodeParams({ id: ids }),
        false
      );

      const res: any = await dataSource.load();
      if (res.items !== null && res.items.length > 0) {
        const data = res.items[0];
        useRetailerStore.getState().setCurrentOutlet(data);
        localStorage.setItem("currentOutletId", data?.id);
        const outletNameData = await getOutletData()
        useRetailerStore.setState({ outletNameData: outletNameData })
        useRetailerStore.getState().setCurrentOutlet(data);

        let outletSelectOption = res.items.map((item: any) => {
          return {
            key: item.id,
            label: (
              <span onClick={() => setCurrentOutlet(item.id)}>{item.name}</span>
            ),
          };
        });
        useRetailerStore.getState().setOutletSelection(outletSelectOption);
      }
    } catch (error) {
      console.error("Error fetching outlets:", error);
    }
  };

  const getOutletById = async (ids: string | string[] = []) => {
    try {
      const dataSource = new DataSource(
        "outlets",
        encodeParams({ id: ids }),
        false
      );

      const res: any = await dataSource.load();
      if (res.items !== null && res.items.length > 0) {
        const data = res.items[0];
        useRetailerStore.getState().setCurrentOutlet(data);
      }
    } catch (error) {
      console.error("Error fetching outlets:", error);
    }
  };

  const setCurrentOutlet = async (outletId: string) => {
    localStorage.setItem("currentOutletId", outletId);
    let data: any = await getOutletById(outletId);
    router.reload();

    return data || {};
  };

  const login = async (values: any) => {
    //This is userInfo info clearing
    clearUserInfoAuth();

    //Get the contact data value and remove "+"
    let data = {
      contact: values.contact.substring(1),
      password: await hashPassword.hashPassword(values.password),
    };

    apiHelper
      .POST(
        // "retailer/troubleshootingAccess",
        "retailer/login",
        data,
        {
          // .POST("retailer/login", data, {
          "Content-Type": "multipart/form-data",
        },
        "v2"
      )
      ?.then((res: any) => {
        localStorage.setItem("accessToken", res.item.accessToken);
        // localStorage.setItem("accessToken", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiI2NGFjNDAxMmZkMTE2NDAwODc1ZjJiMDEiLCJleHAiOiIyMDI0LTAyLTIxVDE0OjMzOjU4KzA4OjAwIiwiaXNzIjoiYWRzZGdlcndlZmt1dWpoIiwic2NvcGVzIjoiVVNFUiIsInN1YiI6IjY0YWM0MDEyZmQxMTY0MDA4NzVmMmIwMSJ9.SpGimNoJRw0-7Q9ewFaYnBMOTm8kLT5T88MmBOG6Pks");
        if (checked) {
          localStorage.setItem("remember", "true");
        } else {
          localStorage.setItem("remember", "false");
        }
        MessageSuccessUI(t("Login.loginSuccessful"));

        getRetailerData().then((res: any) => {
          if (res !== undefined || res !== null) {
            if (res?.outletIds?.length > 0) {
              getOutletByToken(res?.outletIds);
            }

            setTimeout(() => {
              router.push("/landing");
            }, 1000);
          }
        });
      })
      .catch(() => {
        MessageErrorUI(t("Login.wrongContactAndPassword"));
      });
  };

  //temporary to bypass login
  const navigateTo = (path: string) => {
    router.push(path);
  };

  return (
    <div className="relative w-full h-screen flex">
      <div className="relative w-1/3 xs:hidden mobile:block h-screen bg-loginBg bg-cover" />
      <div className="absolute mobile:hidden xs:block bg-responsiveLoginBg bg-cover inset-0 flex justify-start items-center" />
      <div className="xl:w-2/3 flex flex-col justify-center items-center relative xs:w-full ">
        <div className="mb-3">
          <Logo src={Logo.src} className="h-24 w-48 mb-8" />
        </div>
        <div className="shadow-md mobile:w-[400px] xs:w-[320px] xs:py-6 xs:px-6 flex flex-col px-9 py-10 bg-white rounded-xl">
          <p className="font-bold text-darkBlue text-3xl">{t("Login.title")}</p>
          <div className="w-full mt-4">
            <Form
              form={form}
              name="loginForm"
              initialValues={{ remember: true }}
              onFinish={login}
            // onFinish={() => navigateTo("/landing")}
            >
              <Row className="gap-y-1 flex-col">
                <Form.Item
                  name="contact"
                  rules={[
                    {
                      required: true,
                      message: t("Login.forgotPassword.contactNumber"),
                    },
                    {
                      validator(_, value) {
                        if (value && !isValidPhoneNumber(value)) {
                          return Promise.reject(
                            new Error(t("Validation.phoneFormat"))
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <PhoneNumberInput
                    onChange={(value) => setPhoneNumber(value)}
                  ></PhoneNumberInput>
                </Form.Item>
                <Form.Item
                  name="password"
                  rules={[
                    { required: true, message: t("Login.enterPassword") },
                    {
                      validator(_, value) {
                        if (value && value.length < 6) {
                          return Promise.reject(
                            new Error(t("Login.inputValidPassword"))
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <PasswordInput
                    maxLength={20}
                    placeholder={t("PlaceHolder.password")}
                    onChange={(value) => setPassword(value.target.value)}
                  />
                </Form.Item>
              </Row>
              <div className="justify-between flex mt-2">
                <Form.Item name="remember" noStyle></Form.Item>
                <CheckboxInput
                  checked={checked}
                  defaultChecked={false}
                  disabled={false}
                  label="Remember Me"
                  onChange={(val) => setChecked(val.target.checked)}
                ></CheckboxInput>
                <a
                  className="text-primaryBlue hover:text-primaryBlue focus:text-primaryBlue"
                  onClick={() => {
                    router.push("/forgotPassword");
                  }}
                >
                  {t("Login.forgotPassword.title")}
                </a>
              </div>
              <div className="flex justify-center mt-4">
                <PrimaryButtonUI
                  label="Log In"
                  disabled={buttonDisable}
                  htmlType={"submit"}
                  loading={false}
                  className="w-full"
                />
              </div>

              <div className="flex-row mx-2" style={{ opacity: 0.8 }}>
                <a
                  className="text-primaryBlue hover:text-primaryBlue focus:text-primaryBlue"
                  onClick={() => {
                    router.push("/firstTimeLogin");
                  }}
                >
                  {t("Login.signUp.firstTimeLogin")}
                </a>
                <div className="flex">
                  <p>{t("Login.signUp.dontHaveAccount?")}</p>
                  <a
                    className="text-primaryBlue hover:text-primaryBlue focus:text-primaryBlue"
                    onClick={() => {
                      router.push("/signUp");
                    }}
                  >
                    {t("Login.signUp.title")}
                  </a>
                </div>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default RetailerLogin;
