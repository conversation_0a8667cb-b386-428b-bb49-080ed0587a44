import React, { useState } from "react";
import {
  Row,
  Form,
  Tooltip,
  Button,
  Badge,
  FormInstance,
  Dropdown,
  MenuProps,
} from "antd";
import {
  SearchOutlined,
  QuestionCircleOutlined,
  FilterFilled,
  CloseOutlined,
} from "@ant-design/icons";
import { DebounceFilterTextInput } from "./input";
import { ClearFilterButton, PrimaryButtonUI } from "./buttonUI";

interface FilterFormComponentProps {
  isShowFuzzySearchInput?: boolean;
  filterForm: FormInstance;
  onDebouncedChange?: (item: any) => void;
  fieldName?: string;
  clearButtonOnChange: () => void;
  filterModalButtonOnClick: (item: any) => void;
  modalFilterValue: object;
  option: any;
  handleStatusMenuClick: (item: any) => void;
  clearFilterDisable: boolean;
  statusValue: string;
  debounceValue?: string;
}

const FilterFormComponent: React.FC<FilterFormComponentProps> = ({
  isShowFuzzySearchInput = true,
  filterForm,
  onDebouncedChange,
  fieldName,
  clearButtonOnChange,
  filterModalButtonOnClick,
  modalFilterValue,
  option,
  handleStatusMenuClick,
  clearFilterDisable,
  statusValue,
  debounceValue,
}) => {
  // const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  //   const [statusValue, setStatusValue] = useState("All");

  return (
    <Form form={filterForm} className="w-full pt-1 ">
      <Row
        className={`filterBlockForm ${
          isShowFuzzySearchInput ? "justify-between flex w-full" : "justify-end"
        }`}
      >
        {isShowFuzzySearchInput ? (
          <div className="hidden sm:flex gap-x-4 w-[450px] pb-[5px]">
            <Form.Item name="fuzzySearch" className="w-[90%] h-full">
              <DebounceFilterTextInput
                prefix={<SearchOutlined />}
                placeholder={"Search"}
                maxLength={50}
                debounceTime={500}
                value={debounceValue}
                onDebouncedChange={onDebouncedChange}
              />
            </Form.Item>
            <Tooltip
              placement="right"
              title={`This Field search by ${fieldName}`}
            >
              <QuestionCircleOutlined className="text-gray-400 w-[10%]" />
            </Tooltip>
          </div>
        ) : null}
        <Row className="items-center gap-x-2 md:justify-normal justify-end w-full md:w-auto ">
          <div className="hidden sm:flex">
            <ClearFilterButton
              disabled={clearFilterDisable}
              onClick={clearButtonOnChange}
            />
          </div>
          <Button
            type="text"
            className="bg-white rounded-lg flex items-center text-[#7B7BEA] font-bold hover:shadow hover:text-brightBlue300"
            onClick={filterModalButtonOnClick}
          >
            <FilterFilled className="text-[#9494ce]" /> Filter{" "}
            {Object.values(modalFilterValue).filter(
              (value) => value !== undefined && value !== "" && value !== null
            ).length > 0 ? (
              <Badge
                className=" text-xs pl-1"
                count={
                  Object.values(modalFilterValue).filter(
                    (value) =>
                      value !== undefined && value !== "" && value !== null
                  ).length
                }
                color="#7B7BEA"
              />
            ) : null}
          </Button>
          <Dropdown
            className="px-4 py-1 rounded-lg bg-buttonPurple text-white font-bold hover:text-white"
            menu={{
              items: option,
              onClick: handleStatusMenuClick,
            }}
          >
            <a
              onClick={(e) => {
                e.preventDefault();
              }}
            >
              {/* <DownOutlined className="pr-1" /> Status:  */}
              {statusValue}
            </a>
          </Dropdown>
        </Row>
        {/* <Row className="items-center gap-x-2 justify-end">
          {buttons.map((button, index) => (
            <PrimaryButtonUI
              key={index}
              className="w-fit"
              disabled={button.disabled}
              onClick={button.onClick}
              label={button.label}
            ></PrimaryButtonUI>
          ))}
        </Row> */}
      </Row>
    </Form>
  );
};

export default FilterFormComponent;
