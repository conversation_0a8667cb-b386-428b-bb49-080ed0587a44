import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import UserLogin from "./login";
import { supportedLocales } from "@/components/header";

function Main() {
  return <UserLogin />;
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'], null, supportedLocales)),
    },
  };
}

export default Main;
