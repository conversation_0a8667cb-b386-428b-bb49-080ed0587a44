const { i18n } = require("./next-i18next.config");

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  basePath: '/retailer',
  assetPrefix: '/retailer/',
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });

    return config;
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  swcMinify: true,
  i18n,
  generateBuildId: async () => {
    const date = new Date().toISOString().split('T')[0]; 
    return `production-${date}`;
  },
};

module.exports = nextConfig;
