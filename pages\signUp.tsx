import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, Form, Row, Col, DatePicker, Button } from "antd";
import {
  PhoneNumberInput,
  FormTextInput,
  SelectInput,
  NumberInput,
  RadioButtonInput,
} from "../components/input";
import { MessageErrorUI, MessageSuccessUI } from "../components/ui";
import {
  PrimaryButtonUI,
  BackButtonUI,
  ForgotPasswordPreviousButtonUI,
} from "../components/buttonUI";
import apiHelper from "./api/apiHelper";
import OtpInput from "../components/otpInput";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { LeftOutlined, PlusOutlined, UploadOutlined } from "@ant-design/icons";
import { isValidPhoneNumber } from "react-phone-number-input";
import { isSpecialCharacter, isValidEmail } from "../pages/api/checkHelper";
import { Address, SelectOption, googleMap } from "@/components/type";
import {
  RcFile,
  UploadChangeParam,
  UploadFile,
  UploadProps,
} from "antd/lib/upload/interface";
import { Upload } from "antd/lib";
import GoogleMap from "@/components/googleMap";
import {
  BusinessEntity,
  CurrencyName,
  eInoviceStateSelector,
  eInvoiceCountryCodeSelector,
  eInvoiceGeneralTIN,
  stateAbbrevationOptionMyr,
  trueFalseOption,
} from "@/components/config";
import { supportedLocales } from "@/components/header";
import TaxIncomeNumberChecker from "@/components/tinNumberCheckerComponent";

const { Countdown } = Statistic;

function SignUp() {
  const [signUpForm] = Form.useForm();
  const { t } = useTranslation("common");
  const [deadline, setDeadline] = useState(0);
  const [phoneNumber, setPhoneNumber] = useState({});
  const [tab, setTab] = useState(0);
  const [otp, setOtp] = useState("");
  const [expired, setExpired] = useState(false);
  const router = useRouter();
  const [otpAttempt, setOtpAttempt] = useState(2);
  const [uploadedFile, setUploadedFile] = useState<any>(null);
  const [companyOption, setCompanyOption] = useState<SelectOption[]>([
    { value: "65853bfb35627f5fde4130e3", label: "YLTC Sdn Bhd" },
    // { value: "661e351a2b0ba1817991f996", label: "YLB" },
    // { value: "649cf9b1066b7b1153881048", label: "Yee Lee Marketing" },
  ]);
  const [allFiles, setAllFiles] = useState<{ [key: string]: any }>({});

  const [isMalaysia, setIsMalaysia] = useState(false);
  // *************************************************************************************
  // *** Google Map function ***
  // *************************************************************************************
  const [pinpointedCoordinates, setPinpointedCoordinates] = useState<googleMap>(
    {}
  );
  const [lat, setLat] = useState(0); // Initialize with a default value
  const [lng, setLng] = useState(0); // Initialize with a default value
  const { RangePicker } = DatePicker;

  const [businessRegistrationNumber, setBusinessRegistrationNumber] = useState("");
  const [requiredEInvoice, setrequiredEInvoice] = useState("FALSE");

  const handleLatChange = (value: number) => {
    setLat(value);
  };

  const handleLngChange = (value: number) => {
    setLng(value);
  };

  useEffect(() => {
    // getCompanyOption();
    signUpForm.setFieldsValue({ companyId: "65853bfb35627f5fde4130e3" });
    // signUpForm.setFieldsValue({ companyId: "661e351a2b0ba1817991f996" });
    // signUpForm.setFieldsValue({ companyId: "649cf9b1066b7b1153881048" });
  }, [router.isReady]);

  //Get Company Date
  // const getCompanyOption = () => {
  //   apiHelper
  //     .GET("companies" + "?id=649cf9b1066b7b1153881048")
  //     ?.then((res: any) => {
  //       if (res !== null) {
  //         let supplierList: SelectOption[] = [];
  //         res.items.map((value: any) => {
  //           supplierList.push({
  //             value: value.id,
  //             label: value.name,
  //           });
  //         });
  //         setCompanyOption(supplierList);
  //         signUpForm.setFieldsValue({ companyId: "649cf9b1066b7b1153881048" });
  //       }
  //     })
  //     .catch(() => {});
  // };

  //verify Phone number on the first steps
  const getOTP = (): void => {
    signUpForm
      .validateFields()
      .then(() => {
        let data = {
          contact: signUpForm.getFieldValue("contact").substring(1),
          firstName: signUpForm.getFieldValue("firstName"),
          lastName: signUpForm.getFieldValue("lastName"),
        };
        // let formData = new FormData();
        // formData.append("contact", values.contact.substring(1));
        // formData.append("firstName", values.firstName.substring(1));
        // formData.append("lastName", values.lastName.substring(1));

        apiHelper
          .POST("registeredRetailer/verifyOTP", data, "", "v2")
          ?.then(() => {
            MessageSuccessUI(t("Login.signUp.otpSent"));
            setDeadline(Date.now() + 300000);
            setTab(1);
          })
          .catch((res) => {
            // console.log("res: ", error);
            if (res.response.data.error.message) {
              MessageErrorUI(res.response.data.error.message);
            } else MessageErrorUI(t("Login.signUp.failSendOtp"));
            // MessageErrorUI(t("Login.signUp.failSendOtp"));
          });
      })
      .catch((err) => {
        MessageErrorUI(t("Login.signUp.notFillRequiredFields"));
      });
  };

  // validate OTP is match or not . if match go to next step to set password
  const validateOTP = (otpVal: string) => {
    let data = {
      otp: otpVal,
    };
    apiHelper
      .POST("validateOTP", data, "", "v1")
      ?.then((res: any) => {
        submitRetailerData(res);
        // localStorage.setItem("accessToken", res.item.accessToken);
        // setTab(2);
      })
      .catch(() => {
        if (otpAttempt > 0) {
          MessageErrorUI(t("Login.signUp.otpIncorrect"));
          setOtpAttempt(otpAttempt - 1);
        } else {
          MessageErrorUI(t("Login.signUp.tryAgainLater"));
          router.push("/login");
        }
      });
  };

  //resend OTP when user make the request.
  const resendOTP = () => {
    setExpired(false);
    getOTP();
  };

  //images upload
  const getBase64 = (file: RcFile): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }
  };

  const handleChange = (
    info: UploadChangeParam<UploadFile<any>>,
    value: string
  ) => {
    if (info.fileList.length > 0) {
      if (
        info.fileList.at(-1) !== undefined &&
        info.file.status !== "removed"
      ) {
        let file = info.fileList.at(-1);
        if (file !== undefined) {
          file.status = "done";
          let fileObj = file.originFileObj;
          setAllFiles({
            ...allFiles,
            [value]: fileObj,
          });
        }
      }
    }
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div className="mt-2 min-w">{t("Login.signUp.uploadPic")}</div>
    </div>
  );

  const handleBeforeUpload = (file: {
    type: string;
    name: any;
    size: number;
  }) => {
    if (
      file.type !== "image/png" &&
      file.type !== "image/jpg" &&
      file.type !== "image/jpeg"
    ) {
      MessageErrorUI(
        `${file.name} is an invalid file format. Please change the file extension to either .png, .jpg, .jpeg.`
      );
      return Upload.LIST_IGNORE;
    } else if (file.size > 5242880) {
      MessageErrorUI(
        `${file.name} is too large. Please upload another document that is smaller than 5MB.`
      );
      return Upload.LIST_IGNORE;
    } else {
      return false;
    }
  };

  //Upload props
  const props: UploadProps = {
    name: "file",
    maxCount: 1,
    listType: "picture-card",
    onPreview: handlePreview,
    accept: "image/png, image/jpeg, image/jpg",
    showUploadList: {
      showPreviewIcon: false,
    },
    beforeUpload: handleBeforeUpload,
  };

  const ssmDocumentProps: UploadProps = {
    maxCount: 1,
    name: "file",
    beforeUpload: (file) => {
      if (
        file.type !== "image/png" &&
        file.type !== "image/jpg" &&
        file.type !== "image/jpeg" &&
        file.type !== "application/pdf"
      ) {
        MessageErrorUI(
          `${file.name} is an invalid file format. Please change the file extension to either .pdf, .png, .jpg, .jpeg.`
        );
        return Upload.LIST_IGNORE;
      } else if (file.size > 5242880) {
        MessageErrorUI(
          `${file.name} is too large. Please upload another document that is smaller than 5MB.`
        );
        return Upload.LIST_IGNORE;
      } else {
        return false;
      }
    },

    onChange(info) {
      if (info.fileList.length > 0) {
        if (
          info.fileList.at(-1) !== undefined &&
          info.file.status !== "removed"
        ) {
          let file = info.fileList.at(-1);
          if (file !== undefined) {
            file.status = "done";
            const uploadedFile = file.originFileObj;
            setUploadedFile(uploadedFile);
          }
        }
      }
    },
  };

  const submitRetailerData = async (res: any) => {
    let form = new FormData();
    form.append("firstName", signUpForm.getFieldValue("firstName"));
    form.append("lastName", signUpForm.getFieldValue("lastName"));
    form.append("email", signUpForm.getFieldValue("email"));
    let contact = signUpForm.getFieldValue("contact");
    contact = contact?.substring(1);
    form.append("contact", contact);
    form.append("ic", signUpForm.getFieldValue("ic"));
    form.append("companyId", signUpForm.getFieldValue("companyId"));
    form.append("ssmNo", signUpForm.getFieldValue("ssmNo"));
    let date = signUpForm.getFieldValue("date");
    let startDate = "";
    if (date?.[0]) {
      startDate = date?.[0]?.format("YYYY-MM-DDT00:00:00") + "Z";
    }
    let endDate = "";
    if (date?.[1]) {
      endDate = date?.[1]?.format("YYYY-MM-DDT00:00:00") + "Z";
    }
    form.append("startDate", startDate);
    form.append("endDate", endDate);
    form.append("outletName", signUpForm.getFieldValue("outletName"));
    form.append("businessType", signUpForm.getFieldValue("businessType"));
    form.append("latitude", signUpForm.getFieldValue("latitude"));
    form.append("longitude", signUpForm.getFieldValue("longitude"));
    form.append("unitNo", signUpForm.getFieldValue("unitNo") || "");
    form.append("address1", signUpForm.getFieldValue("address1") || "");
    form.append("address2", signUpForm.getFieldValue("address2") || "");
    form.append("country", signUpForm.getFieldValue("country") || "");
    form.append("state", signUpForm.getFieldValue("state") || "");
    form.append("city", signUpForm.getFieldValue("city") || "");
    form.append("postalCode", signUpForm.getFieldValue("postalCode"));
    form.append(
      "billContactPerson",
      signUpForm.getFieldValue("billContactPerson")
    );
    form.append(
      "billMobilePhone",
      signUpForm.getFieldValue("billMobilePhone")?.slice(1)
    );
    form.append(
      "billOfficePhone",
      signUpForm.getFieldValue("billOfficePhone")?.slice(1)
    );
    form.append("billEmail", signUpForm.getFieldValue("billEmail") || "");
    form.append("bankName", signUpForm.getFieldValue("bankName") || "");
    form.append("accountNo", signUpForm.getFieldValue("accountNo") || "");
    form.append("swiftCode", signUpForm.getFieldValue("swiftCode") || "");

    // E-Invoice required
    const stateFormData = signUpForm.getFieldValue("state") || ""
    const state = eInoviceStateSelector.find(item => item.value.toLocaleLowerCase() === stateFormData.toLocaleLowerCase());
    const countryFormData = signUpForm.getFieldValue("country") || ""
    const country = eInvoiceCountryCodeSelector.find(item => 
        (item.value.toLocaleLowerCase() === countryFormData.toLocaleLowerCase()) || (item.label.toLocaleLowerCase() === countryFormData.toLocaleLowerCase()));
    form.append("businessRegistrationNumber", signUpForm.getFieldValue("businessRegistrationNumber") || "");
    form.append("taxIdentificationNumber", signUpForm.getFieldValue("taxIdentificationNumber") || "");
    form.append("generalTIN", signUpForm.getFieldValue("generalTIN") || "");
    form.append("requiredEInvoice", signUpForm.getFieldValue("requiredEInvoice") || "");
    form.append("stateCode", state?.code || "");
    form.append("countryCode", country?.value || "");

    let picForm = new FormData();
    if (uploadedFile) {
      await picForm.append("file", uploadedFile);
    }
    Object.keys(allFiles).map((item) => picForm.append("file", allFiles[item]));

    apiHelper
      .POST(
        "uploadFile",
        picForm,
        {
          "Content-Type": "multipart/form-data",
        },
        "v1"
      )
      ?.then((res: any) => {
        Object.keys(allFiles).map((item) =>
          form.append(item, res.item[allFiles[item].name])
        );
        if (uploadedFile) {
          form.append("ssmDocument", res.item[uploadedFile?.name]);
        }

        apiHelper
          .POST(
            "registeredRetailer",
            form,
            { "Content-Type": "multipart/form-data" },
            "v2"
          )
          ?.then(() => {
            localStorage.setItem("accessToken", res.item.accessToken);
            setTab(2);
            // getOTP();
            // setTab(1);
            // MessageSuccessUI(t("Login.signUp.SignUpSuccess"));
            // router.push("/login");
          })
          ?.catch((err) => {
            MessageErrorUI(err.response.data.error.message);
            // MessageErrorUI(t("Login.signUp.SignUpUnSuccess"));
          });
      })
      ?.catch(() => {
        //* This Part need re-edit*//
      });
    // }
  };

  // const stepComponents = () => {
  //   return (
  //     <div className="py-2 px-4 bg-white h-auto stepBreakPoint:flex hidden">
  //       <Steps
  //         items={[
  //           {
  //             title: "Login",
  //             status: "finish",
  //             icon: <UserOutlined />,
  //           },
  //           {
  //             title: "Verification",
  //             status: "finish",
  //             icon: <SolutionOutlined />,
  //           },
  //           {
  //             title: "Pay",
  //             status: "process",
  //             icon: <LoadingOutlined />,
  //           },
  //           {
  //             title: "Done",
  //             status: "wait",
  //             icon: <SmileOutlined />,
  //           },
  //         ]}
  //       />
  //     </div>
  //   );
  // };

  const getAddressDetails = (
    lat: number,
    lng: number
  ): Promise<Address | null> => {
    return new Promise((resolve) => {
      const latLng = new google.maps.LatLng(lat, lng) as google.maps.LatLng;

      const geocoder = new google.maps.Geocoder();
      geocoder.geocode({ location: latLng }, (results: any, status) => {
        if (status === google.maps.GeocoderStatus.OK && results.length > 0) {
          const addressComponents = results[0].address_components;

          const addressDetails: Address = {
            unitNo: "-",
            address1: "-",
            address2: "-",
            city: "-",
            state: "-",
            country: "-",
            postalCode: "00000",
          };

          addressComponents.forEach((component: any) => {
            const types = component.types;
            if (types.includes("street_number")) {
              addressDetails.unitNo = component.long_name;
            } else if (types.includes("route")) {
              addressDetails.address1 = `${component.long_name}`;
              // address.address1 = `${address.address1} ${component.long_name}`;
            } else if (types.includes("sublocality")) {
              addressDetails.address2 = component.long_name;
            } else if (types.includes("locality")) {
              addressDetails.city = component.long_name;
            } else if (types.includes("administrative_area_level_1")) {
              addressDetails.state = component.short_name;
            } else if (types.includes("country")) {
              addressDetails.country = component.long_name;
            } else if (types.includes("postal_code")) {
              addressDetails.postalCode = component.long_name;
            }
          });

          resolve(addressDetails);
        } else {
          // console.error("Geocoder failed due to: " + status);
          resolve(null); // Pass null to indicate an error or no results
        }
      });
    });
  };

  const handleLocationPinpointed = async (
    lat: number,
    lng: number,
    address: string,
    type: string
  ) => {
    if (type === "general") {
      // Update the state with the pinpointed coordinates
      const pinpointedLocationObject = {
        lat,
        lng,
        address,
        text: "Pinpointed Location",
      };
      setPinpointedCoordinates(pinpointedLocationObject);
      signUpForm.setFieldValue(
        "longitude",
        pinpointedLocationObject.lng?.toFixed(4)
      );
      signUpForm.setFieldValue(
        "latitude",
        pinpointedLocationObject.lat?.toFixed(4)
      );

      const addressComponents = address.split(", ");
      if (addressComponents.length >= 2) {
        // Destructure the address components
        const [
          shippingAddress1,
          shippingAddress2,
          fullShippingCity,
          shippingState,
        ] = addressComponents;
        // Split the city into postal code and city (if necessary)
        const shippingCityComponents = fullShippingCity?.split(" ");
        let shippingPostalCode = "";
        let shippingCity = "";

        if (shippingCityComponents?.length >= 2) {
          shippingPostalCode = shippingCityComponents[0];
          shippingCity = shippingCityComponents.slice(1).join(" ");
        } else {
          // If there's no space-separated postal code and city, assume the whole string is the city
          shippingCity = fullShippingCity;
        }

        // Update the state with the pinpointed coordinates
        const pinpointedLocationObject = {
          lat,
          lng,
          address,
          text: "Pinpointed Location",
        };
        setPinpointedCoordinates(pinpointedLocationObject);

        // Call the getAddressDetails function to get the full address
        let addressByGeoCode: Address | null = null;

        try {
          addressByGeoCode = await getAddressDetails(lat, lng);
        } catch (error) {
          console.error("Error getting address details:", error);
        }

        // Set the values in the form fields
        signUpForm.setFieldValue(
          "address1",
          addressByGeoCode?.address1 === "-"
            ? shippingAddress1
            : (addressByGeoCode?.unitNo ?? "") +
                " " +
                addressByGeoCode?.address1
        );
        signUpForm.setFieldValue(
          "address2",
          addressByGeoCode?.address2 === "-"
            ? shippingAddress2
            : addressByGeoCode?.address2
        );

        // Extract the country (assuming it's the last component of the address)
        const shippingCountry = addressComponents[addressComponents.length - 1];

        // Set the country in the form field
        signUpForm.setFieldValue(
          "country",
          addressByGeoCode?.country === "-"
            ? shippingCountry
            : addressByGeoCode?.country
        );

        signUpForm.setFieldValue(
          "state",
          addressByGeoCode?.state === "-"
            ? shippingState
            : addressByGeoCode?.state
        );
        signUpForm.setFieldValue(
          "city",
          addressByGeoCode?.city === "-" ? shippingCity : addressByGeoCode?.city
        );
        signUpForm.setFieldValue(
          "postalCode",
          addressByGeoCode?.postalCode === "-"
            ? shippingPostalCode
            : addressByGeoCode?.postalCode
        );
      }
    }
  };

  //main content
  const showContent = () => {
    if (tab == 0) {
      return (
        <div className="md:w-[70%] w-[90%] drop-shadow-sm flex flex-col border-2 md:px-9 px-2 py-10 bg-white rounded-xl">
          <ForgotPasswordPreviousButtonUI
            label="Back"
            onClick={() => router.push("/login")}
            icon={<LeftOutlined />}
          ></ForgotPasswordPreviousButtonUI>
          <p className="text-3xl font-bold mt-6">
            {t("Login.signUp.createNew")}
          </p>
          <div className="w-full mt-4">
            <Form
              // onFinish={getOTP}
              // onFinish={() => {
              //   getOTP();
              // }}
              layout="vertical"
              scrollToFirstError
              form={signUpForm}
            >
              <Row className="w-full flex flex-col gap-y-2">
                <Form.Item
                  label={t("Login.signUp.Company")}
                  name="companyId"
                  className="flex-1"
                  rules={[
                    {
                      required: true,
                      message: t("Login.signUp.CompanyRequirement"),
                    },
                  ]}
                >
                  <SelectInput
                    // defaultValue={"649cf9b1066b7b1153881048"}
                    placeholder={""}
                    options={companyOption}
                    disabled
                  />
                </Form.Item>
                <Row className="flex md:flex-row flex-col gap-x-4">
                  <Form.Item
                    label={t("Login.signUp.firstName")}
                    name="firstName"
                    className="flex-1"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Login.signUp.firstName") +
                          " " +
                          t("Validation.requiredField"),
                      },
                      {
                        validator(_, value) {
                          if (value && isSpecialCharacter(value)) {
                            return Promise.reject(
                              new Error(t("Validation.specialCharacter"))
                            );
                          } else if (/[0-9]/.test(value)) {
                            return Promise.reject(
                              new Error(t("Validation.numberCharacter"))
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <FormTextInput
                      maxLength={50}
                      placeholder={t("PlaceHolder.enterFirstName")}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("Login.signUp.lastName")}
                    name="lastName"
                    className="flex-1"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Login.signUp.lastName") +
                          " " +
                          t("Validation.requiredField"),
                      },
                      {
                        validator(_, value) {
                          if (value && isSpecialCharacter(value)) {
                            return Promise.reject(
                              new Error(t("Validation.specialCharacter"))
                            );
                          } else if (/[0-9]/.test(value)) {
                            return Promise.reject(
                              new Error(t("Validation.numberCharacter"))
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <FormTextInput
                      maxLength={50}
                      placeholder={t("PlaceHolder.enterLastName")}
                    />
                  </Form.Item>
                </Row>

                <Row className="flex md:flex-row flex-col gap-x-4">
                  <Form.Item
                    label={t("Login.signUp.email")}
                    className="flex-1"
                    name="email"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Common.email") +
                          " " +
                          t("Validation.requiredField"),
                      },
                      {
                        validator(_, value) {
                          if (value && isValidEmail(value)) {
                            return Promise.reject(
                              new Error(t("Validation.invalidEmail"))
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <FormTextInput
                      placeholder={
                        t("Common.eg") + " " + t("PlaceHolder.email")
                      }
                      maxLength={100}
                      onChange={() => {}}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("Login.signUp.contactNumber")}
                    name="contact"
                    className="flex-1"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Login.signUp.contactNumber") +
                          " " +
                          t("Validation.requiredField"),
                      },
                      {
                        validator(_, value) {
                          if (value && !isValidPhoneNumber(value)) {
                            return Promise.reject(
                              new Error(t("Validation.phoneFormat"))
                            );
                          }
                          // else {
                          //   apiHelper
                          //     .GET("retailers?contact=", value, "", "v2")
                          //     ?.then((res: any) => {
                          //       return Promise.reject(
                          //         new Error(t("contactNumberExist"))
                          //       );
                          //     });
                          // }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <PhoneNumberInput
                      onChange={(val: string) => {
                        setPhoneNumber({ phoneNumber: val });
                      }}
                    ></PhoneNumberInput>
                  </Form.Item>
                </Row>

                <Row className="flex md:flex-row flex-col gap-x-4">
                  <Form.Item
                    label={t("Login.signUp.ssm")}
                    name="ssmNo"
                    className="flex-1"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Login.signUp.ssm") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                  >
                    <FormTextInput
                      maxLength={50}
                      placeholder={t("PlaceHolder.enterSSMNo")}
                    />
                  </Form.Item>
                  <Form.Item
                    name="ssmDocument"
                    label={t("Login.signUp.ssmDoc")}
                    rules={[
                      {
                        required: true,
                        message:
                          t("Login.signUp.ssmDoc") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                    className="flex-1"
                  >
                    <Upload {...ssmDocumentProps}>
                      <div className="flex items-center gap-x-4 p-2 text-buttonPurple bg-lightPurple font-semibold">
                        <PlusOutlined />
                        <p>{t("Login.forgotPassword.uploadDoc")}</p>
                      </div>
                    </Upload>
                  </Form.Item>
                </Row>
                <Form.Item
                  name="date"
                  className="flex-1"
                  rules={[
                    {
                      required: true,
                      message:
                        t("Login.signUp.ssmExpireDate") +
                        " " +
                        t("Validation.requiredField"),
                    },
                  ]}
                  label={<p className="">{t("Login.signUp.ssmExpireDate")}</p>}
                >
                  <RangePicker className="dateInput" />
                </Form.Item>
                <Row className="flex md:flex-row flex-col gap-x-4">
                  <Form.Item
                    label={t("Login.signUp.icNumber")}
                    className="flex-1"
                    name="ic"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Login.signUp.icNumber") +
                          " " +
                          t("Validation.requiredField"),
                      },
                      {
                        validator(_, value) {
                          if (value && isSpecialCharacter(value)) {
                            return Promise.reject(
                              new Error(t("Validation.specialCharacter"))
                            );
                          } else if (value && /[a-z]/.test(value)) {
                            return Promise.reject(
                              new Error(t("Validation.alphabetCharacter"))
                            );
                          } else if (value && value.length < 12) {
                            return Promise.reject(
                              new Error(t("Validation.icLength"))
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <FormTextInput
                      placeholder={
                        t("Common.eg") + " " + t("PlaceHolder.EnterICNumber")
                      }
                      maxLength={12}
                      onChange={() => {}}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("Login.signUp.outletName")}
                    className="flex-1"
                    name="outletName"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Login.signUp.outletName") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                  >
                    <FormTextInput
                      placeholder={
                        t("Common.eg") + " " + t("PlaceHolder.outletName")
                      }
                      maxLength={200}
                      onChange={() => {}}
                    />
                  </Form.Item>
                </Row>

                <Row className="flex md:flex-row flex-col gap-x-4 ">
                  <Form.Item
                    name="icFrontPicture"
                    className="flex-1"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Login.signUp.icFront") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Login.signUp.icFront")}
                      </p>
                    }
                  >
                    <Upload
                      {...props}
                      key={"icFrontPicture"}
                      onChange={(info) => {
                        handleChange(info, "icFrontPicture");
                      }}
                      onRemove={() => {
                        setAllFiles({
                          ...allFiles,
                          ["icFrontPicture"]: null,
                        });
                      }}
                    >
                      {allFiles.hasOwnProperty("icFrontPicture") &&
                      allFiles["icFrontPicture"] !== null
                        ? null
                        : uploadButton}
                    </Upload>
                  </Form.Item>
                  <Form.Item
                    name={"icBackPicture"}
                    className="flex-1"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Login.signUp.icBack") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Login.signUp.icBack")}
                      </p>
                    }
                  >
                    <Upload
                      {...props}
                      key={"icBackPicture"}
                      onChange={(info) => handleChange(info, "icBackPicture")}
                      onRemove={() => {
                        setAllFiles({
                          ...allFiles,
                          ["icBackPicture"]: null,
                        });
                      }}
                    >
                      {allFiles.hasOwnProperty("icBackPicture") &&
                      allFiles["icBackPicture"] !== null
                        ? null
                        : uploadButton}
                    </Upload>
                  </Form.Item>
                </Row>
                <Form.Item
                  name={"outletFrontPicture"}
                  className=""
                  rules={[
                    {
                      required: true,
                      message:
                        t("Login.signUp.outletFrontPicture") +
                        " " +
                        t("Validation.requiredField"),
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Login.signUp.outletFrontPicture")}
                    </p>
                  }
                >
                  <Upload
                    {...props}
                    key={"outletFrontPicture"}
                    onChange={(info) =>
                      handleChange(info, "outletFrontPicture")
                    }
                    onRemove={() => {
                      setAllFiles({
                        ...allFiles,
                        ["outletFrontPicture"]: null,
                      });
                    }}
                  >
                    {allFiles.hasOwnProperty("outletFrontPicture") &&
                    allFiles["outletFrontPicture"] !== null
                      ? null
                      : uploadButton}
                  </Upload>
                </Form.Item>

                <Form.Item
                  name="businessType"
                  className="flex-1"
                  rules={[
                    {
                      required: true,
                      message:
                        t("Login.signUp.businessType") +
                        " " +
                        t("Validation.requiredField"),
                    },
                  ]}
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("Login.signUp.businessType")}
                    </p>
                  }
                >
                  <SelectInput
                    placeholder={
                      t("Common.eg") + " " + t("PlaceHolder.soleProprietorship")
                    }
                    options={BusinessEntity}
                    onChange={() => {}}
                  />
                </Form.Item>

                
                <Row className="flex flex-grow items-center">
                 <Form.Item
                  name="businessRegistrationNumber"
                  className="flex-1"
                  rules={[
                    { required: true, message: t("Setting.businessRegistrationNumber") + " " + t("Validation.requiredField") },
                    {
                      validator(_, value) {
                        if (value && isSpecialCharacter(value)) {
                          return Promise.reject(new Error(t("Validation.specialCharacter")));
                        }
                        else return Promise.resolve();
                      },
                    },
                  ]}
                  label={<p className="text-neutral700 text-[12px]">{t("businessRegistrationNumber")}</p>}>
                  <FormTextInput placeholder={t("businessRegistrationNumber")} maxLength={50} onChange={(value: { target: { value: React.SetStateAction<string> } }) => setBusinessRegistrationNumber(value.target.value)} />
                </Form.Item>
                <TaxIncomeNumberChecker
                    isRequired={true}
                    disabled={!businessRegistrationNumber} 
                    value={signUpForm.getFieldValue("taxIdentificationNumber")}
                    checkTINValue={{
                      registeredType: "BRN",
                      registerSchemeID: businessRegistrationNumber
                    }}/>
                </Row>

                <Row className="flex flex-row gap-x-4">
                  <Form.Item name="generalTIN" className="flex-1" rules={[{ required: true, message: t("setting.eInvoiceGeneralTin") + " " + t("Validation.requiredField") }]} label={<p className="text-neutral700 text-[12px]">{t("setting.eInvoiceGeneralTin")}</p>}>
                    <SelectInput placeholder={t("Eg") + " " + ""} options={eInvoiceGeneralTIN} onChange={() => {}} />
                  </Form.Item>
                </Row>

                <Form.Item name="requiredEInvoice" label={<p className="text-neutral700 text-[12px]">{t("Setting.requiredEInvoice")}</p>}>
                  <RadioButtonInput
                    defaultValue={requiredEInvoice}
                    onChange={(value: { target: { value: React.SetStateAction<string> } }) => {
                      setrequiredEInvoice(value.target.value);
                    }}
                    option={trueFalseOption}
                  />
                </Form.Item>

                <Row className="flex flex-row gap-x-4 pt-2">
                  <Form.Item
                    name="gpsLocation"
                    className="flex-1"
                    // rules={[{ required: true, mess age: t("GPSLocation") + " " + t("Validation.requiredField"), }]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Login.signUp.outletLocation")}
                      </p>
                    }
                  >
                    <GoogleMap
                      newLat={lat}
                      newLng={lng}
                      onLocationPinpointed={(lat, lng, address) =>
                        handleLocationPinpointed(lat, lng, address, "general")
                      }
                      pointLat={0}
                      pointLng={0}
                    />
                    {/* <FormAddressInput maxLength={100} placeholder={""} setAddress={handleAddressInputChange("GPS")} /> */}
                  </Form.Item>
                </Row>

                <Row className="flex md:flex-row flex-col gap-x-4">
                  <Form.Item
                    name="latitude"
                    className="flex-1"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Login.signUp.latitude") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Login.signUp.latitude")}
                      </p>
                    }
                    initialValue={pinpointedCoordinates.lat}
                  >
                    <NumberInput
                      placeholder={t("Login.signUp.latitude")}
                      value={lat}
                      precision={4}
                      onChange={handleLatChange}
                      disabled
                      max={10}
                    />
                  </Form.Item>

                  <Form.Item
                    name="longitude"
                    className="flex-1"
                    rules={[
                      {
                        required: true,
                        message:
                          t("Login.signUp.longitude") +
                          " " +
                          t("Validation.requiredField"),
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Login.signUp.longitude")}
                      </p>
                    }
                    initialValue={pinpointedCoordinates.lng}
                  >
                    <NumberInput
                      placeholder={t("Login.signUp.longitude")}
                      value={lng}
                      precision={4}
                      onChange={handleLngChange}
                      disabled
                    />
                  </Form.Item>
                </Row>

                <h1 className="font-bold text-xl  pt-4">
                  {t("Login.signUp.outletAddress")}
                </h1>
                <Col className="flex flex-col bg-white w-full rounded-[12px] gap-y-2">
                  <Col className="w-full">
                    <Row className="flex flex-row gap-x-4">
                      <Form.Item
                        name="unitNo"
                        className="flex-1"
                        label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Login.signUp.unitNo")}
                          </p>
                        }
                      >
                        <FormTextInput
                          placeholder={
                            t("Common.eg") + " " + t("Login.signUp.unitNo")
                          }
                          maxLength={100}
                        />
                      </Form.Item>
                    </Row>

                    <Form.Item
                      name="address1"
                      className="flex-1"
                      rules={[
                        {
                          required: true,
                          message:
                            t("Login.signUp.address1") +
                            " " +
                            t("Validation.requiredField"),
                        },
                      ]}
                      label={
                        <p className="text-neutral700 text-[12px]">
                          {t("Login.signUp.address1")}
                        </p>
                      }
                    >
                      <FormTextInput
                        placeholder={t("Login.signUp.address1")}
                        maxLength={100}
                      />
                    </Form.Item>
                    <Form.Item
                      name="address2"
                      className="flex-1"
                      /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                        <p className="text-neutral700 text-[12px]">
                          {t("Login.signUp.address2")}
                        </p>
                      }
                    >
                      <FormTextInput
                        placeholder={t("Login.signUp.address2")}
                        maxLength={100}
                      />
                    </Form.Item>
                    <Row className="flex md:flex-row flex-col gap-x-4">
                      <Form.Item
                        name="country"
                        className="flex-1"
                        rules={[
                          {
                            required: true,
                            message:
                              t("Login.signUp.country") +
                              " " +
                              t("Validation.requiredField"),
                          },
                        ]}
                        label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Login.signUp.country")}
                          </p>
                        }
                      >
                        <SelectInput
                          options={eInvoiceCountryCodeSelector}
                          placeholder={t("Login.signUp.country")}
                          onChange={(val) => {
                            val === "Malaysia"
                              ? setIsMalaysia(true)
                              : setIsMalaysia(false);
                            signUpForm.resetFields(["billState"]);
                          }}
                        />
                      </Form.Item>
                      <Form.Item
                        name="state"
                        className="flex-1"
                        rules={[
                          {
                            required: true,
                            message:
                              t("Login.signUp.state") +
                              " " +
                              t("Validation.requiredField"),
                          },
                        ]}
                        label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Login.signUp.state")}
                          </p>
                        }
                      >
                        {isMalaysia ? (
                          <SelectInput
                            options={eInoviceStateSelector}
                            placeholder={t("Login.signUp.state")}
                          />
                        ) : (
                          <FormTextInput
                            defaultValue={""}
                            placeholder={t("Login.signUp.state")}
                            maxLength={50}
                          />
                        )}
                        {/* disabled={stateList.length !== 0 ? false : true} /> */}
                      </Form.Item>
                    </Row>
                    <Row className="flex md:flex-row flex-col gap-x-4">
                      <Form.Item
                        name="city"
                        className="flex-1"
                        rules={[
                          {
                            required: true,
                            message:
                              t("Login.signUp.city") +
                              " " +
                              t("Validation.requiredField"),
                          },
                        ]}
                        label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Login.signUp.city")}
                          </p>
                        }
                      >
                        <FormTextInput
                          defaultValue={""}
                          placeholder={t("Login.signUp.city")}
                          maxLength={100}
                        />
                        {/* disabled={cityList.length !== 0 ? false : true} /> */}
                      </Form.Item>
                      <Form.Item
                        name="postalCode"
                        className="flex-1"
                        rules={[
                          {
                            required: true,
                            message:
                              t("Login.signUp.postalCode") +
                              " " +
                              t("Validation.requiredField"),
                          },
                          {
                            validator(_, value) {
                              if (value && isSpecialCharacter(value)) {
                                return Promise.reject(
                                  new Error(t("Special.Character.Error"))
                                );
                              }
                              if (value && /[a-z]/.test(value)) {
                                return Promise.reject(
                                  new Error(t("Alphabet.Character.Error"))
                                );
                              } else return Promise.resolve();
                            },
                          },
                        ]}
                        label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Login.signUp.postalCode")}
                          </p>
                        }
                      >
                        <FormTextInput
                          placeholder={"53550"}
                          maxLength={5}
                          // onChange={(value: any) => setPostalCode(value)}
                        />
                      </Form.Item>
                    </Row>
                    <Form.Item
                      name="billContactPerson"
                      rules={[
                        {
                          required: true,
                          message:
                            t("Login.signUp.billContactPerson") +
                            " " +
                            t("Validation.requiredField"),
                        },
                        {
                          validator(_, value) {
                            if (value && isSpecialCharacter(value)) {
                              return Promise.reject(
                                new Error(t("Validation.specialCharacter"))
                              );
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                      label={
                        <p className="text-neutral700 text-[12px]">
                          {t("Login.signUp.billContactPerson")}
                        </p>
                      }
                    >
                      <FormTextInput
                        placeholder={t("Login.signUp.billContactPerson")}
                        maxLength={100}
                      />
                    </Form.Item>
                    <Row className="flex md:flex-row flex-col gap-x-4">
                      <Form.Item
                        name="billMobilePhone"
                        className="flex-1"
                        rules={[
                          {
                            required: true,
                            message:
                              t("Login.signUp.billMobilePhone") +
                              " " +
                              t("Validation.requiredField"),
                          },
                          {
                            validator(_, value) {
                              if (value && !isValidPhoneNumber(value)) {
                                return Promise.reject(
                                  new Error(t("Validation.phoneFormat"))
                                );
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                        label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Login.signUp.billMobilePhone")}
                          </p>
                        }
                      >
                        <PhoneNumberInput onChange={() => {}} />
                      </Form.Item>
                      <Form.Item
                        name="billOfficePhone"
                        className="flex-1"
                        rules={[
                          // {
                          //   required: true,
                          //   message:
                          //     t("Login.signUp.billOfficePhone") +
                          //     " " +
                          //     t("Validation.requiredField"),
                          // },
                          {
                            validator(_, value) {
                              if (value && !isValidPhoneNumber(value)) {
                                return Promise.reject(
                                  new Error(t("Validation.phoneFormat"))
                                );
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                        label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Login.signUp.billOfficePhone")}
                          </p>
                        }
                      >
                        <PhoneNumberInput onChange={() => {}} />
                      </Form.Item>
                    </Row>
                    <Form.Item
                      name="billEmail"
                      rules={[
                        {
                          validator(_, value) {
                            if (value && isValidEmail(value)) {
                              return Promise.reject(
                                new Error(t("Validation.invalidEmail"))
                              );
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                      label={
                        <p className="text-neutral700 text-[12px]">
                          {t("Login.signUp.billEmail")}
                        </p>
                      }
                    >
                      <FormTextInput
                        placeholder={t("Login.signUp.billEmail")}
                        maxLength={100}
                      />
                    </Form.Item>
                  </Col>
                </Col>
                <h1 className="font-bold text-xl pt-4">
                  {t("Virtual Account Details")}
                </h1>
                <Col className="flex flex-col bg-white w-full rounded-[12px] space-y-2">
                  <Row className="flex md:flex-row flex-col gap-x-4">
                    <Form.Item
                      name="bankName"
                      className="flex-1"
                      label={
                        <p className="text-neutral700 text-[12px]">
                          {t("Login.signUp.bankName")}{" "}
                        </p>
                      }
                    >
                      <FormTextInput
                        placeholder={
                          t("Common.eg") + " " + t("Login.signUp.bankName")
                        }
                        maxLength={100}
                        onChange={() => {}}
                      />
                    </Form.Item>
                    <Form.Item
                      name="accountNo"
                      className="flex-1"
                      rules={[
                        {
                          validator(_, value) {
                            if (value && isSpecialCharacter(value)) {
                              return Promise.reject(
                                new Error(t("Validation.Special.Character"))
                              );
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                      label={
                        <p className="text-neutral700 text-[12px]">
                          {t("Login.signUp.accountNo")}
                        </p>
                      }
                    >
                      <FormTextInput
                        placeholder={
                          t("Common.eg") + " " + t("Login.signUp.accountNo")
                        }
                        maxLength={100}
                        onChange={() => {}}
                      />
                    </Form.Item>
                  </Row>
                  <Form.Item
                    name="swiftCode"
                    rules={[
                      {
                        validator(_, value) {
                          if (value && isSpecialCharacter(value)) {
                            return Promise.reject(
                              new Error(t("Validation.Special.Character"))
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                    label={
                      <p className="text-neutral700 text-[12px]">
                        {t("Login.signUp.swiftCode")}{" "}
                      </p>
                    }
                  >
                    <FormTextInput
                      placeholder={
                        t("Common.eg") + " " + t("Login.signUp.swiftCode")
                      }
                      maxLength={100}
                      onChange={() => {}}
                    />
                  </Form.Item>
                </Col>
                <Row className="flex justify-center mt-4">
                  <PrimaryButtonUI
                    label={t("Login.verifiedPhoneNumber")}
                    // disabled={false}
                    htmlType={"submit"}
                    onClick={() => getOTP() }
                    // loading={false}
                    className="w-full"
                  />
                </Row>
                {/* <Row className="flex-row justify-start mt-4 gap-x-2">
                <PrimaryButtonUI
                  label={t("Common.cancel")}
                  // disabled={false}
                  // htmlType={"submit"}
                  onClick={() => router.push("/login")}
                  // loading={false}
                  // className="w-full"
                />
                <PrimaryButtonUI
                  label={t("Login.signUp.resetFields")}
                  // disabled={false}
                  // htmlType={"submit"}
                  onClick={() => {
                    signUpForm.resetFields();
                    signUpForm.setFieldsValue({
                       companyId: retailerAccess.companyId,
                    });
                  }}
                  // loading={false}
                  // className="w-full"
                />
              </Row> */}
                {/* <Row className="flex justify-center mt-4">
                <PrimaryButtonUI
                  label={t("Next")}
                  disabled={false}
                  htmlType={"submit"}
                  loading={false}
                  className="w-full"
                />
              </Row> */}
              </Row>
            </Form>
          </div>
        </div>
      );
    } else if (tab == 1) {
      const onChange = (value: string) => setOtp(value);
      return (
        <div className="drop-shadow-sm w-400px flex flex-col border-2 px-9 py-10 bg-white rounded-xl">
          <p className="text-3xl font-bold ">
            {t("Login.forgotPassword.enterCode")}
          </p>
          <p className="font-normal	text-base mt-6 text-[#666666]">
            {t("Login.forgotPassword.enterOTPVerified")}
          </p>
          <div className="w-full mt-4 ">
            <OtpInput
              value={otp}
              onChange={onChange}
              valueLength={6}
            ></OtpInput>
            <Countdown
              value={deadline}
              format="mm:ss"
              className="mt-4 timerFontColor text-xl text-center"
              onFinish={() => setExpired(true)}
            />
          </div>
          <p className="font-normal	text-base mt-6 text-[#666666]">
            {t("Login.forgotPassword.didntReceive")}
            <span>
              {expired == true ? (
                <a
                  className="text-primaryBlue"
                  onClick={() => {
                    resendOTP();
                  }}
                >
                  {" " + t("Login.forgotPassword.resendCode")}
                </a>
              ) : (
                <a className="cursor-not-allowed text-[#dddddd] hover:text-[#dddddd]">
                  {" "}
                  {t("Login.forgotPassword.resendCode")}
                </a>
              )}
            </span>
          </p>
          <div className="flex justify-center mt-4">
            <PrimaryButtonUI
              label={t("Submit")}
              disabled={false}
              htmlType={"submit"}
              loading={false}
              className="w-full"
              onClick={() => validateOTP(otp)}
            />
          </div>
        </div>
      );
    } else if (tab == 2) {
      return (
        <div className="drop-shadow-sm flex flex-col border-2 px-9 py-10 max-w-400px bg-white rounded-xl">
          <p className="text-3xl font-bold ">
            {t("Login.forgotPassword.successfulVerificationPhoneNumber")}
          </p>
          <p className="font-normal	text-base mt-6 text-[#666666]">
            {t("Login.forgotPassword.successfulVerificationPhoneNumberContact")}
          </p>
          <div className="flex justify-center mt-3">
            <PrimaryButtonUI
              label={t("Login.returnLoginPage")}
              onClick={() => router.push("/login")}
              htmlType="submit"
              className="w-full"
            ></PrimaryButtonUI>
          </div>
        </div>
      );

      // return (
      //   <div className="drop-shadow-sm flex flex-col border-2 px-9 py-10 max-w-400px bg-white rounded-xl">
      //     <p className="text-3xl font-bold ">
      //       {t("Login.forgotPassword.enterPassword")}
      //     </p>
      //     <p className="font-normal	text-base mt-6 text-[#666666]">
      //       {t("Login.forgotPassword.successfulVerification")}
      //     </p>
      //     <Form className="mt-4 w-full">
      //       <Row className="gap-y-2 flex-col">
      //         <Form.Item
      //           // label={t("Login.signUp.password")}
      //           name="password"
      //           // className="flex-1"
      //           hasFeedback
      //           rules={[
      //             {
      //               required: true,
      //               message: t("Login.signUp.enterPassword"),
      //             },
      //             {
      //               validator(_, value) {
      //                 if (value && value.length < 6) {
      //                   return Promise.reject(
      //                     new Error(t("Login.signUp.inputValidPassword"))
      //                   );
      //                 }
      //                 if (value && isValidPasswordPattern(value) == false) {
      //                   return Promise.reject(
      //                     new Error(t("Login.signUp.passwordRequirement"))
      //                   );
      //                 }
      //                 return Promise.resolve();
      //               },
      //             },
      //           ]}
      //         >
      //           <PasswordInput
      //             maxLength={50}
      //             placeholder={t("Login.signUp.password")}
      //           ></PasswordInput>
      //         </Form.Item>
      //         <Form.Item
      //           // label={t("Login.signUp.confirmPassword")}
      //           name="confirmPassword"
      //           // className="flex-1"
      //           hasFeedback
      //           rules={[
      //             {
      //               required: true,
      //               message: t("Login.signUp.confirmPassword"),
      //             },
      //             ({ getFieldValue }) => ({
      //               validator(_, value) {
      //                 if (value && value.length < 6) {
      //                   return Promise.reject(
      //                     new Error(t("Login.signUp.inputValidPassword"))
      //                   );
      //                 } else if (
      //                   !value ||
      //                   getFieldValue("password") !== value
      //                 ) {
      //                   return Promise.reject(
      //                     new Error(t("Login.signUp.passwordNotMatch"))
      //                   );
      //                 }
      //                 if (value && isValidPasswordPattern(value) == false) {
      //                   return Promise.reject(
      //                     new Error(t("Login.signUp.passwordRequirement"))
      //                   );
      //                 }
      //                 return Promise.resolve();
      //               },
      //             }),
      //           ]}
      //         >
      //           <PasswordInput
      //             maxLength={50}
      //             placeholder={t("Login.signUp.confirmPassword")}
      //           ></PasswordInput>
      //         </Form.Item>
      //       </Row>
      //       <div className="flex justify-center mt-3">
      //         <PrimaryButtonUI
      //           label={t("Login.forgotPassword.done")}
      //           htmlType="submit"
      //           className="w-full"
      //         ></PrimaryButtonUI>
      //       </div>
      //     </Form>
      //   </div>
      // );
    }
  };

  return (
    <div className="w-full min-h-screen flex">
      {/* <div className="w-1/4 min-h-screen bg-loginBg " /> */}
      <div className="w-full flex flex-col justify-center items-center relative md:p-4 p-2">
        {/* {stepComponents()} */}
        {showContent()}
      </div>
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default SignUp;
