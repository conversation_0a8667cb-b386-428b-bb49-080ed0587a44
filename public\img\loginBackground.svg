<svg width="765" height="1024" viewBox="0 0 765 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="765" height="1024" fill="url(#paint0_linear_1470_50359)"/>
<mask id="mask0_1470_50359" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="765" height="1024">
<rect width="765" height="1024" fill="url(#paint1_linear_1470_50359)"/>
</mask>
<g mask="url(#mask0_1470_50359)">
<g style="mix-blend-mode:multiply" opacity="0.5" filter="url(#filter0_d_1470_50359)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M-400.313 483.184C-305.922 263.056 -85.803 121.999 156.442 149.279C461.516 183.635 686.157 471.961 658.192 793.273C650.926 876.762 627.32 954.264 591.004 1022.65C511.561 714.569 258.707 473.132 -59.2689 437.323C-179.337 423.802 -295.354 441.101 -400.313 483.184Z" fill="url(#paint2_linear_1470_50359)"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter1_d_1470_50359)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M-543.093 78.0121C-398.241 -372.536 67.2613 -608.382 496.635 -448.766C926.009 -289.15 1156.66 205.486 1011.81 656.034C961.49 812.542 872.48 943.143 759.733 1040.26C639.052 507.021 151.409 143.636 -370.993 216.131C-444.755 226.367 -515.485 244.776 -582.485 270.383C-576.505 206.092 -563.54 141.613 -543.093 78.0121Z" fill="url(#paint3_linear_1470_50359)"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter2_d_1470_50359)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M-112.348 400.368C-129.479 143.527 53.8372 -76.1881 297.101 -90.3798C540.365 -104.572 751.457 92.1345 768.589 348.975C774.54 438.195 756.303 522.934 719.769 596.135C555.349 356.076 243.258 281.192 -0.261489 431.729C-34.6467 452.985 -65.9278 477.661 -93.953 505.119C-103.646 471.683 -109.93 436.625 -112.348 400.368Z" fill="url(#paint4_linear_1470_50359)"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter3_d_1470_50359)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M-188.29 386.809C-163.931 318.996 -126.438 255.326 -75.7162 200.092C126.268 -19.8584 461.542 -22.735 673.139 193.667C792.326 315.56 846.851 481.025 835.646 641.61C964.15 401.555 911.555 89.1741 699.28 -93.077C463.083 -295.867 118.631 -260.752 -70.0753 -14.6443C-159.904 102.509 -198.248 246.356 -188.29 386.809Z" fill="url(#paint5_linear_1470_50359)"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter4_d_1470_50359)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M-0.724393 622.294C14.5003 551.443 43.2493 482.797 86.1764 420.604C257.109 172.957 588.118 121.413 825.506 305.477C959.216 409.152 1034.71 565.462 1044.59 726.475C1140.27 469.548 1047.51 167.137 813.912 17.0711C553.986 -149.909 218.113 -65.0334 63.7183 206.647C-9.77995 335.978 -28.9031 484.328 -0.724393 622.294Z" fill="url(#paint6_linear_1470_50359)"/>
</g>
</g>
<defs>
<filter id="filter0_d_1470_50359" x="-445.312" y="117.915" width="1122.86" height="938.734" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-14" dy="3"/>
<feGaussianBlur stdDeviation="15.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1470_50359"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1470_50359" result="shape"/>
</filter>
<filter id="filter1_d_1470_50359" x="-627.485" y="-526.683" width="1699.56" height="1600.94" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-14" dy="3"/>
<feGaussianBlur stdDeviation="15.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1470_50359"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1470_50359" result="shape"/>
</filter>
<filter id="filter2_d_1470_50359" x="-158.457" y="-119.102" width="945.155" height="749.237" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-14" dy="3"/>
<feGaussianBlur stdDeviation="15.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1470_50359"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1470_50359" result="shape"/>
</filter>
<filter id="filter3_d_1470_50359" x="-234.827" y="-252.883" width="1156.85" height="928.493" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-14" dy="3"/>
<feGaussianBlur stdDeviation="15.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1470_50359"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1470_50359" result="shape"/>
</filter>
<filter id="filter4_d_1470_50359" x="-58.1338" y="-96.4962" width="1156.98" height="856.971" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-14" dy="3"/>
<feGaussianBlur stdDeviation="15.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1470_50359"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1470_50359" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1470_50359" x1="-172.5" y1="218.5" x2="656.946" y2="119.421" gradientUnits="userSpaceOnUse">
<stop stop-color="#2353FF"/>
<stop offset="1" stop-color="#FD6828"/>
</linearGradient>
<linearGradient id="paint1_linear_1470_50359" x1="0" y1="0" x2="859.497" y2="86.648" gradientUnits="userSpaceOnUse">
<stop stop-color="#2353FF"/>
<stop offset="1" stop-color="#FD6828"/>
</linearGradient>
<linearGradient id="paint2_linear_1470_50359" x1="-1307.98" y1="-1224.25" x2="-1557.4" y2="430.674" gradientUnits="userSpaceOnUse">
<stop stop-color="#2353FF"/>
<stop offset="1" stop-color="#FD6828"/>
</linearGradient>
<linearGradient id="paint3_linear_1470_50359" x1="-1077.63" y1="-538.946" x2="-322.845" y2="770.591" gradientUnits="userSpaceOnUse">
<stop stop-color="#2353FF"/>
<stop offset="1" stop-color="#FD6828"/>
</linearGradient>
<linearGradient id="paint4_linear_1470_50359" x1="-2247.88" y1="-609.869" x2="-1052.56" y2="1481.66" gradientUnits="userSpaceOnUse">
<stop stop-color="#2353FF"/>
<stop offset="1" stop-color="#FD6828"/>
</linearGradient>
<linearGradient id="paint5_linear_1470_50359" x1="-959.822" y1="-976.963" x2="-669.17" y2="585.344" gradientUnits="userSpaceOnUse">
<stop stop-color="#2353FF"/>
<stop offset="0.865628" stop-color="#FD6828"/>
</linearGradient>
<linearGradient id="paint6_linear_1470_50359" x1="-2328.42" y1="-669.357" x2="-1401.89" y2="1378.92" gradientUnits="userSpaceOnUse">
<stop stop-color="#2353FF"/>
<stop offset="0.996417" stop-color="#FD6828"/>
</linearGradient>
</defs>
</svg>
