import { <PERSON><PERSON>, <PERSON>, Row } from "antd";
import { useEffect, useState } from "react";
import { FormTextInput, NumberInput } from "./input";
import { CloseOutlined } from "@ant-design/icons";
import { debounce } from "lodash";

interface PrimaryButtonProps {
  icon?: React.ReactNode;
  label?: string;
  disabled?: boolean;
  htmlType?: "button" | "submit" | "reset" | undefined;
  loading?: boolean;
  onClick?: any;
  className?: string;
  buttonColor?: string;
  buttonType?: string;
  htmlPath?: string;
}

interface SecondaryButtonProps {
  label: string;
  disabled?: boolean;
  htmlType?: "button" | "submit" | "reset" | undefined;
  loading?: boolean;
  onClick?: () => void;
  className?: string;
  icon?: React.ReactNode;
}

interface BackButtonProps {
  disabled?: boolean;
  onClick?: () => void;
  title?: string;
  buttons: {
    label: string;
    onClick: () => void;
    disabled?: boolean;
    buttonColor?: string;
    loading?: boolean;
  }[];
}

interface IconDescriptionButtonProps {
  label: string;
  disabled?: boolean;
  icon: React.ReactNode;
  buttonIcon: React.ReactNode;
  description?: string;
  onClick?: () => void;
}

interface IconButtonProps {
  icon?: React.ReactNode;
  onClick?: () => void;
}

interface PreviousButtonProps {
  label: string;
  disabled?: boolean;
  icon?: React.ReactNode;
  onClick?: () => void;
}

interface CounterComponentProps {
  onCountChange: (count: number) => void;
  uom?: string;
  className?: string;
  defaultValue?: number;
  disabled?: boolean;
  max?: number;
  min?: number;
  minimumDisable?: boolean;
  multiplication?: number;
  minimumOrder?: number;
}

export interface IconLabelButtonProps {
  label?: string;
  disabled?: boolean;
  icon?: React.ReactNode;
  onClick?: any;
  className?: string;
}

interface SearchBarProps {
  // firstButtonLabel?: string;
  // secondButtonLabel?: string;
  // thirdButtonLabel?: string;
  // disabledButton?: string;

  buttons: { label: string; onClick: () => void; disabled?: boolean }[];
}

interface ClearFilterProps {
  disabled?: boolean;
  onClick?: () => void;
}

export const PrimaryButtonUI: React.FC<PrimaryButtonProps> = ({
  label,
  disabled,
  htmlType,
  loading,
  onClick,
  icon,
  className,
  buttonColor,
  htmlPath,
}) => {
  if (buttonColor !== undefined) {
    let inputClassName = !disabled
      ? `buttonStyle ${buttonColor}`
      : "disabledButtonBg buttonStyle ";
    return (
      <Button
        icon={icon}
        disabled={disabled}
        htmlType={htmlType}
        loading={loading}
        type="primary"
        href={htmlPath}
        onClick={onClick}
        className={inputClassName}
      >
        {label}
      </Button>
    );
  } else {
    let inputClassName = disabled
      ? `disabledButtonBg buttonStyle ${className}`
      : `buttonStyle primaryButtonBg shadow-md  ${className}`;
    return (
      <Button
        icon={icon}
        disabled={disabled}
        htmlType={htmlType}
        loading={loading}
        type="primary"
        href={htmlPath}
        onClick={onClick}
        className={inputClassName}
        style={{
          boxShadow: "rgba(0, 0, 0, 0.15) 2.4px 2.4px 2.2px",
          transition: "background-color 0.3s ease-in-out",
        }}
      >
        {label}
      </Button>
    );
  }
};

export const BackButtonUI: React.FC<BackButtonProps> = ({
  disabled,
  onClick,
  title,
  buttons,
}) => (
  <div className="flex flex-col items-center mb-2">
    <Row className="flex justify-start w-full">
      {/* <Button
        disabled={disabled}
        type="link"
        onClick={onClick}
        icon={<LeftOutlined />}
        className="text-[rgb(153,153,153)] flex p-0 items-center "
      >
        {"Back"}
      </Button> */}
    </Row>
    <Row className="w-full flex flex-row items-start md:items-center gap-y-2">
      {title && <p className="font-bold text-[24px]">{title}</p>}

      <div className="ml-auto hidden md:block w-[50px]">&nbsp;</div>

      <Col className="flex flex-1 justify-end gap-3 w-auto">
        {buttons.map((button, index) => (
          <PrimaryButtonUI
            key={index}
            className="w-fit"
            disabled={button.disabled}
            onClick={button.onClick}
            label={button.label}
            buttonColor={button.buttonColor}
            loading={button.loading}
          />
        ))}
      </Col>
    </Row>
  </div>
);

export const IconDescriptionButtonUI: React.FC<IconDescriptionButtonProps> = ({
  label,
  disabled,
  icon,
  buttonIcon,
  description,
  onClick,
}) => (
  <Button
    disabled={disabled}
    type="link"
    onClick={onClick}
    className="iconDescriptionButton"
  >
    <div className="flex w-5/6">
      <div className="pr-2 pt-0.5 ">{icon}</div>
      <div>
        <div className="text-lg flex justify-start">{label}</div>
        <div className="description-Gray whitespace-normal">{description}</div>
      </div>
    </div>
    <div className="iconDescriptionButton-Arrow">{buttonIcon}</div>
  </Button>
);

export const IconButtonUI: React.FC<IconButtonProps> = ({ icon, onClick }) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 480); // Adjust based on `xs` size
    };

    handleResize(); // Run once on mount
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <button
      onClick={onClick}
      // className="absolute inset-0 bg-buttonOrange/30
      // rounded-full scale-0 transition-transform
      // duration-500 opacity-0 "
      className={`bg-buttonPurple icon-button
        rounded-md flex items-center justify-center ${
          isMobile ? "w-7 h-7" : "w-9 h-9"
        } hover:shadow-[0px_0px_10px_rgba(109,109,125,0.5)] transition-all duration-300
        `}
    >
      {/* Button Icon */}
      <span className={isMobile ? "text-sm" : "text-lg"}>{icon}</span>
    </button>
  );
};

export const ForgotPasswordPreviousButtonUI: React.FC<PreviousButtonProps> = ({
  label,
  disabled,
  onClick,
  icon,
}) => (
  <Button
    disabled={disabled}
    type="link"
    onClick={onClick}
    icon={icon}
    className="text-[#999999] flex justify-left p-0 items-center "
  >
    {label}
  </Button>
);

export const CounterComponent: React.FC<CounterComponentProps> = ({
  uom,
  onCountChange,
  className,
  defaultValue,
  disabled,
  max,
  min,
  minimumDisable,
  multiplication,
  minimumOrder,
}) => {
  const [count, setCount] = useState(minimumOrder ?? 1);

  useEffect(() => {
    //  need to support value 0
    if (defaultValue || (defaultValue !== undefined && defaultValue >= 0)) {
      setCount(defaultValue);
    } else {
      setCount(minimumOrder ?? 1);
    }
  }, [defaultValue]);

  const debouncedSetCount = debounce((val) => {
    setCount(val);
    onCountChange(val);
  }, 500);

  const decreaseCount = () => {
    let updatedCount =
      count - (multiplication && multiplication !== 0 ? multiplication : 1);
    if (updatedCount === 0) {
      updatedCount = 1;
    }
    setCount(updatedCount);
    onCountChange(updatedCount);
  };

  const increaseCount = () => {
    let updatedCount: number = count;

    if (updatedCount < (multiplication ?? 1)) {
      updatedCount = multiplication ?? 1;
    } else {
      updatedCount += multiplication || 1;
    }

    setCount(updatedCount);
    onCountChange(updatedCount);
  };

  // const increaseCount = () => {
  //   let updatedCount = count;

  //   updatedCount +=
  //     defaultValue === 1 && multiplication === 0 ? 1 : multiplication || 1;

  //   setCount(updatedCount);
  //   onCountChange(updatedCount);
  // };

  return (
    <div className="flex flex-col items-center justify-between p-1">
      <Row className="gap-x-2">
        <Button
          disabled={count <= (minimumOrder ?? 1) || disabled || minimumDisable}
          onClick={decreaseCount}
          className={
            className
              ? className + " counter"
              : "flex items-center justify-center rounded-xl bg-[#F5F5F5] p-4 border-0"
          }
          style={{
            boxShadow: "rgba(0, 0, 0, 0.15) 1.4px 1.4px 1.2px",
          }}
        >
          -
        </Button>
        <div className="w-[55px]">
          <NumberInput
            defaultValue={defaultValue}
            disabled={
              disabled || (multiplication && multiplication > 1) ? true : false
            }
            value={count}
            max={max ?? 100000000}
            min={min ?? 0}
            onChange={(val) => debouncedSetCount(val)}
          ></NumberInput>
        </div>
        <Button
          disabled={count >= (max ?? 1000) || disabled}
          onClick={increaseCount}
          className={
            className
              ? className + " counter"
              : "flex items-center justify-center rounded-xl bg-[#F5F5F5] p-4 border-0"
          }
          style={{
            boxShadow: "rgba(0, 0, 0, 0.15) 1.4px 1.4px 1.2px",
          }}
        >
          +
        </Button>
      </Row>
      <Row>
        <p>{uom}</p>
      </Row>
    </div>
  );
};

export const SecondaryButtonUI: React.FC<SecondaryButtonProps> = ({
  label,
  disabled,
  htmlType,
  loading,
  onClick,
  icon,
  className,
}) => (
  <Button
    className={className ? className : "buttonStyle secondaryButtonBg "}
    style={{
      boxShadow: "rgba(0, 0, 0, 0.15) 2.4px 2.4px 2.2px",
    }}
    disabled={disabled}
    htmlType={htmlType}
    loading={loading}
    onClick={onClick}
    icon={icon}
  >
    {label}
  </Button>
);

export const EditButtonUI: React.FC<IconLabelButtonProps> = ({
  label,
  disabled,
  onClick,
  icon,
  className,
}) => (
  <Button
    disabled={disabled}
    onClick={onClick}
    icon={icon}
    className="text-buttonOrange flex items-center border-none bg-transparent"
  >
    {label}
  </Button>
);

export const RemoveButtonUI: React.FC<IconLabelButtonProps> = ({
  label,
  disabled,
  onClick,
  icon,
  className,
}) => (
  <Button
    disabled={disabled}
    onClick={onClick}
    icon={icon}
    className="text-red-400 flex items-center border-none bg-transparent"
  >
    {label}
  </Button>
);
export const SearchBar: React.FC<SearchBarProps> = ({ buttons }) => {
  return (
    <div className="flex flex-row justify-end mb-7 mt-2">
      {/* <FormTextInput
        maxLength={0}
        placeholder={"SearchBar"}
        className="w-[330px]"
      /> */}
      <Col className="flex flex-row gap-x-3">
        {buttons.map((button, index) => (
          <PrimaryButtonUI
            key={index}
            className="w-fit"
            disabled={button.disabled}
            onClick={button.onClick}
            label={button.label}
          ></PrimaryButtonUI>
        ))}
        {/* {firstButtonLabel ? (
          <PrimaryButtonUI
            label={firstButtonLabel}
            htmlType="submit"
            className="w-fit"
            disabled={disabledButton === "first"}
          ></PrimaryButtonUI>
        ) : null}
        {secondButtonLabel ? (
          <PrimaryButtonUI
            label={secondButtonLabel}
            htmlType="submit"
            className="w-fit"
            disabled={disabledButton === "second"}
          ></PrimaryButtonUI>
        ) : null}
        {thirdButtonLabel ? (
          <PrimaryButtonUI
            label={thirdButtonLabel}
            htmlType="submit"
            className="w-fit"
            disabled={disabledButton === "third"}
          ></PrimaryButtonUI>
        ) : null} */}
      </Col>
    </div>
  );
};

export const ClearFilterButton: React.FC<ClearFilterProps> = ({
  disabled,
  onClick,
}) => (
  <Button
    danger
    type="text"
    className="items-center flex font-bold"
    disabled={disabled}
    onClick={onClick}
  >
    <CloseOutlined className="text-xs" />{" "}
    <p className="text-xs">Clear Filter</p>
  </Button>
);
