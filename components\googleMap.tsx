import React, { useEffect, useRef, useState } from "react";
import GoogleMapReact from "google-map-react";
import { GOOGLE_PLACE_API_KEY } from "./config";
import { FormTextInput } from "./input";

interface MarkerProps {
  lat: number;
  lng: number;
  text: string;
  color?: string;
  isUserLocation?: boolean;
}

const Location: React.FC<MarkerProps> = ({ color, isUserLocation }) => (
  <div className={`google-map-pin${isUserLocation ? " user-location" : ""}`}>
    <div className="pin" style={{ background: color || "red" }}></div>
    {isUserLocation ? null : <div className="pulse"></div>}
  </div>
);

const GoogleMap = ({
  newLat,
  newLng,
  onLocationPinpointed,
  pointLat,
  pointLng,
}: {
  newLat: number;
  newLng: number;
  onLocationPinpointed: (
    lat: number,
    lng: number,
    pinpointedAddress: string
  ) => void;
  pointLat: number;
  pointLng: number;
}) => {
  const [pinpointedLocation, setPinpointedLocation] =
    useState<MarkerProps | null>(null);
  const [pinpointedAddress, setPinpointedAddress] = useState<string | null>(
    null
  );
  const [searchQuery, setSearchQuery] = useState("");
  const searchInputRef = useRef<HTMLInputElement | null>(null);

  // Define the getAddressForCoordinates function to fetch the address
  const getAddressForCoordinates = async (lat: number, lng: number) => {
    return new Promise<string>((resolve, reject) => {
      const geocoder = new window.google.maps.Geocoder();
      geocoder.geocode(
        { location: { lat, lng } },
        (results: any, status: any) => {
          if (status === "OK" && results.length > 0) {
            const address = results[0].formatted_address;
            resolve(address);
          } else {
            reject("Address not found");
          }
        }
      );
    });
  };

  // Use newLat and newLng to set the new pinpoint location
  useEffect(() => {
    if (newLat !== 0 && newLng !== 0) {
      const newPinpointLocation = {
        lat: pinpointedLocation?.lat || newLat,
        lng: pinpointedLocation?.lng || newLng,
        text: "New Pinpoint",
        color: "red",
      };

      // Fetch the address for the new coordinates
      getAddressForCoordinates(newLat, newLng)
        .then((newPinpointAddress) => {
          setPinpointedLocation(newPinpointLocation);
          setPinpointedAddress(newPinpointAddress);
          // newLat = 0
          // newLng = 0
          // Call the callback function and pass the coordinates and address
          onLocationPinpointed(newLat, newLng, newPinpointAddress);
        })
        .catch((error) => {
          console.error("Error reverse geocoding the location:", error);
        });
    }
  }, [newLat, newLng, onLocationPinpointed]);

  const defaultCenter = pinpointedLocation || {
    lat: pointLat || 3.1279820778375433,
    lng: pointLng || 101.72485733925974,
  };
  const defaultZoom = 12;

  const handleMapClick = (event: any) => {
    // Handle map click event and get the coordinates
    const { lat, lng, pinpointedAddress } = event;
    const pinpointedLocationObject = {
      lat,
      lng,
      pinpointedAddress,
      text: "Pinpointed Location",
    };
    setPinpointedLocation(pinpointedLocationObject);

    // Call the callback function and pass the coordinates
    // Reverse geocode to get the address
    const geocoder = new window.google.maps.Geocoder();
    geocoder.geocode(
      { location: { lat, lng } },
      (results: any, status: any) => {
        if (status === "OK" && results.length > 0) {
          const address = results[0].formatted_address;
          setPinpointedAddress(address);
          onLocationPinpointed(lat, lng, address);
        } else {
          console.error("Error reverse geocoding the location:", status);
          onLocationPinpointed(lat, lng, "Address not found");
        }
      }
    );
  };

  const handleSearch = (event: any) => {
    const geocoder = new window.google.maps.Geocoder();
    event.preventDefault();
    geocoder.geocode({ address: searchQuery }, (results, status) => {
      results = results?.length ? results : [];
      if (status === "OK" && results?.length > 0) {
        const result = results[0].geometry.location;
        const lat = result.lat();
        const lng = result.lng();
        const pinpointedLocationObject = {
          lat,
          lng,
          text: "Searched Location",
        };
        setPinpointedLocation(pinpointedLocationObject);

        // Set the address for the searched location
        const address = results[0].formatted_address;
        setPinpointedAddress(address);

        onLocationPinpointed(lat, lng, address);
      } else {
        console.error("Error geocoding the search:", status);
      }
    });
  };

  return (
    <div>
      <div style={{ height: "50vh", width: "100%", marginBottom: "20px" }}>
        <GoogleMapReact
          bootstrapURLKeys={{ key: GOOGLE_PLACE_API_KEY }}
          center={
            newLat === 0 && newLng === 0
              ? defaultCenter
              : { lat: newLat, lng: newLng }
          }
          defaultZoom={defaultZoom}
          onClick={handleMapClick}
        >
          {newLat === 0 && newLng === 0 ? (
            <Location
              lat={defaultCenter.lat}
              lng={defaultCenter.lng}
              text="User Location"
              color="red"
              isUserLocation
            />
          ) : (
            pinpointedLocation && (
              <Location
                lat={pinpointedLocation.lat}
                lng={pinpointedLocation.lng}
                text={pinpointedLocation.text}
                color="red"
              />
            )
          )}
        </GoogleMapReact>
      </div>
      <div className="search-box mb-3 flex">
        <FormTextInput
          placeholder="Search for a location"
          ref={searchInputRef}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          maxLength={300}
          className="border-none"
        />
        <div
          className="line"
          style={{
            width: "1px",
            height: "35px",
            backgroundColor: "#cccccc",
            marginRight: "5px",
            marginTop: "0px",
          }}
        ></div>
        <button onClick={handleSearch} className="w-[250px]">
          Search
        </button>
      </div>
      {pinpointedAddress && <p>Pinpointed Address: {pinpointedAddress}</p>}
    </div>
  );
};

export default GoogleMap;
