import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Row, Table, Tooltip, message } from "antd";
import _, { isArray } from "lodash";
import { CounterComponent, PrimaryButtonUI } from "./buttonUI";
import { ExpandableConfig } from "antd/lib/table/interface";
import {
  CaretDownFilled,
  CaretRightFilled,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { NumberThousandSeparator, PUBLIC_BUCKET_URL } from "@/stores/utilize";
import PreOrder from "../assets/logo/preOrder.svg";
import ArrowRight from "../assets/icon/right.svg";
import Vector from "../assets/icon/vector.svg";
import { ConfirmModalProps, Promotion, PromotionCriteria } from "./type";
import React, { useRef, useEffect, useState } from "react";

interface ProductBlockProps {
  productName: string;
  price: number;
  product: any;
  // discountPrice: number;
  // companyName: string;
  // sellingAmount: number;
  image: string;
  className?: string;
  onclick?: (value?: number) => void;
}

interface FixedBottomBarProps {
  quantityItem: number;
  totalPrice: number;
  savedPrice: number;
  onclick?: () => void;
  buttonDisabled?: boolean;
  buttonLoading?: boolean;
}

interface TableProps {
  scroll?: Object | boolean;
  pagination?: Object | boolean;
  dataSource: any[];
  columns: any[];
  rowKey?: any;
  rowSelection?: Object;
  bordered?: boolean;
  onRow?: any;
  pageSize?: number;
  onChange?: () => void;
  loadMoreData?: () => void;
  loader?: boolean;
  cursor?: any;
  endMessage?: string;
  moreMessage?: string;
  emptyMessage?: string;
  expandable?: ExpandableConfig<any>;
  summaryTitle?: string;
  summaryValue?: string | number;
  summaryColspan?: number;
  components?: any;
  EditableCell?: any;
  expandRowKeys?: string[];
  emptyText?: string;
  className?: string;
  onLoadMore?: any;
  subtotal?: string;
  loading?: boolean;
  screenSize?: boolean;
  isHeaderNotShow?: boolean;
}

type RenderExpandIconProps<T> = {
  expanded: boolean;
  onExpand: (
    record: T,
    event: React.MouseEvent<HTMLElement, MouseEvent>
  ) => void;
  record: T;
};

export const MessageErrorUI = (content: string | string[]) => {
  let arrContent;
  if (isArray(content)) {
    arrContent = [];
    content.forEach((item: string) => {
      arrContent.push(
        <>
          <span>{item}</span>
          <br />
        </>
      );
    });
  } else {
    arrContent = content;
  }

  message.error({
    content: arrContent,
    style: {
      fontSize: "16px",
      alignItems: "center",
      alignContent: "center",
    },
    duration: 3,
  });
};

export const MessageSuccessUI = (content: string) => {
  message.success({
    content: content,
    style: {
      fontSize: "16px",
      alignItems: "center",
      alignContent: "center",
    },
    duration: 3,
  });
};

export const MessageInfoUI = (content: string) => {
  message.info({
    content: content,
    style: {
      fontSize: "16px",
      alignItems: "center",
      alignContent: "center",
    },
    duration: 3,
  });
};

export const ProductBlock: React.FC<ProductBlockProps> = ({
  productName,
  price,
  product,
  image,
  className,
}) => {
  const imageRef = useRef<HTMLImageElement>(null);
  const [widthFill, setWidthFill] = useState(false);
  const [heightFill, setHeightFill] = useState(false);

  useEffect(() => {
    const handleImageLoad = () => {
      const img = imageRef.current;

      // Check if img is not null
      if (img) {
        const { width, height } = img;

        // If width or height is less than 150 pixels, fill width or height
        if (width < 150) {
          setWidthFill(true);
          setHeightFill(false);
        } else if (height < 150) {
          setHeightFill(true);
          setWidthFill(false);
        } else {
          // Determine if the image is wider or taller than its container
          if (width > height) {
            setWidthFill(true); // Apply width fill
            setHeightFill(false);
          } else {
            setWidthFill(false);
            setHeightFill(true); // Apply height fill
          }
        }
      }
    };

    if (imageRef.current && imageRef.current.complete) {
      // If the image is already loaded, handle the image load immediately
      handleImageLoad();
    } else {
      // Otherwise, add an event listener for the image's load event
      imageRef.current?.addEventListener("load", handleImageLoad);
    }

    return () => {
      // Cleanup event listener on component unmount
      imageRef.current?.removeEventListener("load", handleImageLoad);
    };
  }, [image]);

  return (
    <Row className={className ? className : "flex flex-col h-[120px]  "}>
      {/* Image and promo icon */}
      <div className="relative w-full">
        <div
          className="h-[150px] flex justify-center items-center rounded-md overflow-hidden"
          style={{
            background:
              "linear-gradient(to bottom right, white, rgba(128, 128, 128, 0.1))",
          }}
        >
          <img
            ref={imageRef}
            src={image}
            className={`w-full h-full rounded-tl-md rounded-tr-md object-cover`}
            alt="Product Image"
          />
        </div>
        {renderPromoIcon(product)}
        {product?.tradeType === "PREORDER" ? (
          <div className="absolute top-0 left-0">
            <PreOrder src={PreOrder.src} />
          </div>
        ) : null}
      </div>

      {/* Product details */}
      <Row className="bg-white h-[100px] w-full p-2 space-y-1 rounded-b flex flex-col">
        <p className="textLabel line-clamp-2 overflow-hidden overflow-ellipsis capitalize">
          {productName.toLowerCase()}
        </p>
        <Row className="flex flex-grow items-end">
          {/* <Row className="flex flex-col flex-grow">
            <Col className="flex "> */}
          <p className="text-buttonOrange font-semibold flex items-baseline">
            <p className="text-buttonOrange textDescription pr-1">RM</p>
            <p className="text-buttonOrange textNumber flex font-semibold">
              {NumberThousandSeparator(price)}
            </p>
          </p>
          {/* </Col>
          </Row> */}
        </Row>
      </Row>
    </Row>
  );
};

export function priceLocaleString(data: any) {
  data.toLocaleString(undefined, {
    maximumFractionDigits: 2,
    minimumFractionDigits: 2,
  });
  return data;
}

export function FixedBottomBar({
  quantityItem,
  totalPrice,
  savedPrice,
  onclick,
  buttonDisabled,
  buttonLoading,
}: FixedBottomBarProps) {
  return (
    <div className="bg-white w-full fixed-bottom-bar flex md:justify-center justify-end sm:px-0 px-2">
      <div className=" flex flex-row h-[80px] gap-x-4 md:w-4/5 w-full sm:justify-end justify-between items-center">
        <Col className="md:text-[16px] text-[14px] font-semibold">
          <p>Total ({quantityItem} Items ):</p>
        </Col>
        <Col className="flex flex-col items-baseline">
          <p>
            <span className="text-buttonOrange textSubTitle pr-1">RM</span>
            <span className="textTitle font-semibold text-buttonOrange">
              {NumberThousandSeparator(totalPrice)}
            </span>
          </p>
          {savedPrice > 0 ? (
            <p className="textDescription font-semibold text-buttonOrange flex">
              Saved RM {NumberThousandSeparator(savedPrice)}
            </p>
          ) : null}
        </Col>
        <Col className="">
          <PrimaryButtonUI
            label={"CheckOut"}
            onClick={onclick}
            disabled={buttonDisabled}
            loading={buttonLoading}
          />
        </Col>
      </div>
    </div>
  );
}

// interface PromotionUIProps {
//   promotionName: number;
//   totalProduct: number;
//   savedPrice: number;
// }

// export function MyCartPromotionUI({quantityItem,
//   totalPrice,
//   savedPrice,
// }: PromotionUIProps) {
//   return(
//     <div className="flex-row grow w-11/12">
//             <img
//             src={"https://www.shutterstock.com/image-vector/sale-banner-template-design-big-600w-1018054066.jpg"}

//               className="rounded-md"
//               ></img>
//             <Row  className="flex-col ml-2 items-start">
//               <p className="text-titleBlue font-normal text-[13]px truncate w-full" numberOfLines={1}>
//                 {item.name}
//               </p>
//               <View className="truncate">
//                 {item?.discounts?.map((discount, index) => {
//                   return (
//                     <Text
//                       numberOfLines={2}
//                       className="text-secondaryGrey font-normal text-xs"
//                       key={${item.id}_${index}discount}>
//                       Buy {discount.quantityFrom} discount {discount.discountValue} {item.discountVolume}
//                     </Text>
//                   );
//                 })}
//               </View>
//               {
//                 item.discountUsed && item.budgetAllocated  && (
//                   <View>
//                     <Text className="truncate">
//                       {'Discount'} / {'Budget'}
//                     </Text>
//                     <Text className="truncate">
//                       { item.discountUsed } / { item.budgetAllocated }
//                     </Text>
//                   </View>
//                 )
//               }
//               {
//                  item.isDisabled && item.reason && ( <Text className="text-sm	text-red-600	">{item.reason}</Text> )
//               }
//             </View>
//           </div>
//   )
// }

export const ListingTableUI: React.FC<TableProps> = ({
  expandable,
  EditableCell,
  dataSource,
  columns,
  rowKey,
  bordered,
  loader,
  cursor,
  endMessage,
  moreMessage,
  emptyMessage,
  expandRowKeys,
  rowSelection,
  className,
  onLoadMore,
  loading,
  subtotal,
  screenSize,
  isHeaderNotShow,
}) => {
  const inputClassName = className
    ? `w-full tableUI border-rounded ${className} ${
        isHeaderNotShow ? "mobile-table" : ""
      }`
    : `w-full tableUI overflow-auto border-rounded ${
        isHeaderNotShow ? "mobile-table" : ""
      }`;

  return (
    <div className="w-full flex flex-col">
      {subtotal ? (
        <Table
          rowSelection={rowSelection}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 800 }}
          rowKey={rowKey}
          rowClassName={(record) =>
            expandRowKeys?.includes(record.id) ? "expandedRow" : "bg-white"
          }
          locale={{
            emptyText: (
              <p className="text-black italic">
                {emptyMessage != undefined
                  ? emptyMessage
                  : "No data is available"}
              </p>
            ),
          }}
          pagination={{ pageSize: 5 }}
          size="small"
          bordered={bordered ? bordered : true}
          components={{
            body: {
              // cell: EditableCell ? EditableCell : EditableCellTable, //edited here
            },
          }}
          expandable={expandable}
          // emptyText={emptyText}
          style={{ contentVisibility: "auto" }} //ContentVIsibility Optimization code
          className={inputClassName}
          loading={loading}
          footer={() => (
            <div>
              <div className="flex justify-between text-l font-bold">
                <span>Sub total:</span>
                <span>{subtotal}</span>
              </div>
            </div>
          )}
        />
      ) : (
        <Table
          rowSelection={rowSelection}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: screenSize ? 100 : 800 }}
          rowKey={rowKey}
          rowClassName={(record) =>
            expandRowKeys?.includes(record.id) ? "expandedRow" : "bg-white"
          }
          locale={{
            emptyText: (
              <p className="text-black italic">
                {emptyMessage != undefined
                  ? emptyMessage
                  : "No data is available"}
              </p>
            ),
          }}
          pagination={false}
          size="small"
          bordered={bordered ? bordered : true}
          components={{
            body: {
              // cell: EditableCell ? EditableCell : EditableCellTable, //edited here
            },
          }}
          expandable={expandable}
          // emptyText={emptyText}
          style={{ contentVisibility: "auto" }} //ContentVIsibility Optimization code
          className={inputClassName}
          loading={loading}
        />
      )}
      {endMessage !== "" ? (
        <div className="flex justify-center">
          {dataSource?.length === 0 ? (
            ""
          ) : cursor == 0 ? (
            <Button
              type="link"
              loading={loader}
              className="bg-transparent text-primaryBlue border-0 my-6"
              disabled={true}
              onClick={onLoadMore}
            >
              {endMessage !== undefined ? endMessage : "No more data..."}
            </Button>
          ) : (
            <Button
              type="link"
              loading={loader}
              className="bg-transparent text-primaryBlue border-0 my-6 hover:font-bold hover:text-primaryBlue focus:text-primaryBlue"
              onClick={onLoadMore}
            >
              {moreMessage !== undefined
                ? moreMessage
                : "Scroll to see more..."}
            </Button>
          )}
        </div>
      ) : null}
    </div>
  );
};

// export const PaymentTableUI: React.FC<TableProps> = ({
//   expandable,
//   dataSource,
//   columns,
//   rowKey,
//   bordered,
//   expandRowKeys,
//   rowSelection,
//   className,
//   subtotal,
// }) => {
//   const inputClassName = className
//     ? `w-full tableUI border-rounded ${className}`
//     : "w-full tableUI overflow-auto border-rounded";

//   // const subtotal = dataSource.reduce(
//   //   (acc, item) => acc + (item.paidAmount || 0.0),
//   //   0
//   // );

//   return (
//     <div className="w-full flex flex-col">
//       <Table
//         rowSelection={rowSelection}
//         dataSource={dataSource}
//         columns={columns}
//         rowKey={rowKey}
//         rowClassName={(record) =>
//           expandRowKeys?.includes(record.id) ? "expandedRow" : "bg-white"
//         }
//         pagination={{ pageSize: 5 }}
//         bordered={bordered ? bordered : true}
//         components={{
//           body: {},
//         }}
//         footer={() => (
//           <div>
//             <div className="flex justify-between text-l font-bold">
//               <span>Sub total:</span>
//               <span>{subtotal}</span>
//             </div>
//           </div>
//         )}
//         expandable={expandable}
//         style={{ contentVisibility: "auto" }} //ContentVIsibility Optimization code
//         className={inputClassName}
//       />
//     </div>
//   );
// };

export const ExpandIcon = <T extends unknown>({
  expanded,
  onExpand,
  record,
}: RenderExpandIconProps<T>) => (
  <>
    {expanded ? (
      <CaretDownFilled
        style={{ color: "red" }}
        onClick={(e: React.MouseEvent<HTMLElement, MouseEvent>) =>
          onExpand(record, e)
        }
      />
    ) : (
      <CaretRightFilled
        style={{ color: "gray" }}
        onClick={(e: React.MouseEvent<HTMLElement, MouseEvent>) =>
          onExpand(record, e)
        }
      />
    )}
  </>
);

export function getStatusStyles(status: any): [string, string, string] {
  let statusColor: string = "";
  let statusBackgroundColor: string = "";
  let statusBorderColor: string = "";

  switch (status) {
    case "ACTIVE":
    case "APPROVED":
    case "TRUE":
    case "VERIFIED":
    case "COMPLETED":
    case "STATEMENTED":
    case "INVOICED":
    case "DELIVERED":
    case "SUCCESS":
    case "VALID":
    case "COMPLETED":
      statusColor = "greenStatusColor";
      statusBackgroundColor = "greenStatusBackgroundColor";
      statusBorderColor = "greenStatusBorderColor";
      break;
    case "INACTIVE":
    case "REJECTED":
    case "CANCELLED":
    case "FALSE":
    case "EXPIRED":
    case "DELETED":
    case "MISSED":
    case "UNVERIFIED":
    case "INVALID":
    case "DATAVALIDATIONFAILED":
    case "IRBMSUBMISSIONFAILED":
      statusColor = "redStatusColor";
      statusBackgroundColor = "redStatusBackgroundColor";
      statusBorderColor = "redStatusBorderColor";
      break;
    case "ACKNOWLEDGED":
      statusColor = "brownStatusColor";
      statusBackgroundColor = "brownStatusBackgroundColor";
      statusBorderColor = "brownStatusBorderColor";
      break;
    case "PENDING":
    case "ONHOLD":
    case "PARTIAL VERIFIED":
    case "PARTIALAPPROVAL":
    case "PENDINGIRBMVALIDATION":
    case "NOTREQUIRED":
    case "PENDINGVERIFY":
      statusColor = "orangeStatusColor";
      statusBackgroundColor = "orangeStatusBackgroundColor";
      statusBorderColor = "orangeStatusBorderColor";
      break;
    case "PICKED":
    case "CLAIMED":
      statusColor = "purpleStatusColor";
      statusBackgroundColor = "purpleStatusBackgroundColor";
      statusBorderColor = "purpleStatusBorderColor";
      break;
    case "DRAFT":
    case "CONFIRMED":
    case "UNPLANNED":
    case "UNSETTLED":
    case "PROCESSING":
      statusColor = "blueStatusColor";
      statusBackgroundColor = "blueStatusBackgroundColor";
      statusBorderColor = "blueStatusBorderColor";
      break;
    case "PENDINGINVOICED":
    case "GENERATINGINVOICEPDF":
    case "SYSTEMPROCESSING":
    case "PENDINGVERIFY":
      statusColor = "purpleStatusColor";
      statusBackgroundColor = "purpleStatusBackgroundColor";
      statusBorderColor = "purpleStatusBorderColor";
      break;
  }

  return [statusColor, statusBackgroundColor, statusBorderColor];
}

export const statusApproval: React.FC<any> = (
  record: any,
  isEInvoiceStatus: boolean = false
) => {
  record = _.cloneDeep(record);
  const [statusColor, statusBackgroundColor, statusBorderColor] =
    getStatusStyles(
      isEInvoiceStatus
        ? record.eInvoiceStatus
        : record?.dataToDisplay
        ? record?.dataToDisplay
        : record?.paymentApprovalStatus
        ? record?.paymentApprovalStatus
        : record?.companyStatus
        ? record?.companyStatus
        : record?.isClaimed
        ? record?.isClaimed
        : record?.tradeStatus
        ? record?.tradeStatus
        : record?.status
    );

  return (
    <>
      <div className="flex flex-row gap-x-1">
        <div
          className={`tableRowNameDesign statusTag ${statusColor} ${statusBackgroundColor} ${statusBorderColor}`}
        >
          {_.capitalize(
            isEInvoiceStatus
              ? record.eInvoiceStatus
              : record.status === "PENDINGINVOICED"
              ? "Processing Invoice"
              : record.status === "GENERATINGINVOICEPDF"
              ? "Generating Invoice PDF"
              : record.status === "SYSTEMPROCESSING"
              ? "System Processing"
              : record.status === "CLAIMED"
              ? "Submitted"
              : record.status === "PENDINGVERIFY"
              ? "Pending Verify"
              : record.dataToDisplay ??
                record.paymentApprovalStatus ??
                record.status ??
                record.companyStatus ??
                record.isClaimed ??
                record.tradeStatus
          )}
        </div>
        {/* {record.approvalStatus === "PENDING" ? <div className={`tableRowNameDesign statusTag orangeStatusColor orangeStatusBackgroundColor orangeStatusBorderColor`}>{_.capitalize("Pending For Approval")}</div> : null} */}
      </div>
    </>
  );
};

export const TableUI: React.FC<TableProps> = ({
  expandable,
  dataSource,
  components,
  columns,
  rowKey,
  bordered,
  summaryTitle,
  summaryValue,
  summaryColspan,
  rowSelection,
}) => (
  <Table
    components={components}
    dataSource={dataSource}
    columns={columns}
    locale={{
      emptyText: <p className="text-black italic">No data is available</p>,
    }}
    // pagination={true}
    bordered={bordered}
    // className={`w-full tableUI overflow-auto ${bordered ? 'border-rounded' : ''}`}
    className="w-full tableUI overflow-auto border-rounded"
  />
);

export const constructAddress = (value: any) => {
  const tempAddressParts: any = [];
  const properties = ["address1", "address2", "city", "state", "country"];

  properties.forEach((property) => {
    if (value?.[property]) {
      tempAddressParts.push(value[property]);
    }
  });

  return tempAddressParts.join(", ");
};

const renderBundlePromo = (text: string = "") => {
  const background = "linear-gradient(to right, #0C24FF, #6883E6)";
  return (
    <div
      className={"z-[2] items-center justify-center rounded-tr-xl px-2 py-1"}
      style={{ background }}
    >
      <p className="text-[10px] font-bold text-white">{text}</p>
    </div>
  );
};

const renderSinglePromo = (text: string) => {
  const background = "linear-gradient(to right, #FE773D, #FFC85C)";
  return (
    <div
      className={"items-center justify-center rounded-tr-xl px-2 py-1"}
      style={{ marginLeft: -7, background }}
    >
      <p className="text-[10px] font-bold text-white" style={{ marginLeft: 5 }}>
        {text}
      </p>
    </div>
  );
};

const renderPromoPlus = () => {
  return (
    <div
      className="flex-row items-center justify-center"
      style={{
        width: 10,
        height: 10,
        borderRadius: 10,
        zIndex: 3,
        backgroundColor: "#FFFFFF",
        position: "absolute",
        left: 74,
        bottom: 3,
        display: "flex",
      }}
    >
      <p
        className="text-primaryBlack"
        style={{ fontSize: "large", marginBottom: 4 }}
      >
        +
      </p>
    </div>
  );
};

export const renderPromoIcon = (item: any) => {
  return (
    <div
      style={{
        position: "absolute",
        bottom: 0,
        display: "flex",
        height: "20px",
        left: 0,
        overflow: "hidden",
      }}
    >
      {item?.isBundlePromo && renderBundlePromo("Bundle Promo")}

      {item.isSinglePromo && item.isBundlePromo && renderPromoPlus()}

      {item?.isSinglePromo && renderSinglePromo("Single Promo")}
    </div>
  );
};

//promotion
export const RenderPromotionCard = (
  promotion: Promotion,
  onClick: any,
  { isShowArrow = true, isShowQuestion = false }
) => {
  const tieringType = promotion?.tieringType || "";
  const productGroup = promotion.productGroups?.length
    ? promotion.productGroups[0]
    : {};
  const isFoc = promotion?.discountType === "FOC";
  const images = promotion.images?.length ? promotion.images[0] : "";

  return (
    <div
      className="w-full mb-2 cursor-pointer border border-buttonOrange/50 rounded-lg shadow-sm hover:shadow-md transition-all duration-300  "
      style={{
        background:
          "linear-gradient(to right, rgba(249,246,243,255), rgba(251,249,248,255), transparent)",
      }}
      key={promotion.id}
      onClick={() => onClick(promotion)}
    >
      <div className="flex w-full items-center">
        <div className="flex w-11/12 p-1 ">
          <img
            src={
              images
                ? images
                : "https://www.shutterstock.com/image-vector/sale-banner-template-design-big-600w-1018054066.jpg"
            }
            alt="Promotion Image"
            className="rounded-md object-cover w-10 h-10"
          />
          <div className="flex flex-col ml-2 items-start overflow-hidden">
            <Tooltip title={promotion.name}>
              <p
                className="font-semibold textLabel w-full truncate"
                style={{
                  maxWidth:
                    isShowArrow || isShowQuestion
                      ? "calc(100% - 3rem)"
                      : "100%",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                {promotion.name}
              </p>
            </Tooltip>
            <div className="font-normal w-full">
              {tieringType === "TOTALBUY" &&
                productGroup?.promotionCriterias?.length === 1 &&
                promotion?.discounts?.map((discount, index) => (
                  <p
                    className="textDescription text-buttonOrange capitalize truncate"
                    key={(promotion?.id || "") + index}
                    style={{
                      maxWidth:
                        isShowArrow || isShowQuestion
                          ? "calc(100% - 3rem)"
                          : "100%",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}
                  >
                    Buy {discount?.quantityFrom} {!isFoc ? "discount" : "FOC"}{" "}
                    {discount?.discountValue}{" "}
                    {promotion?.discountVolume?.toLowerCase()}
                  </p>
                ))}
              {tieringType === "PRODUCTGROUP" &&
                productGroup?.promotionCriterias?.length &&
                productGroup?.promotionCriterias?.length >= 1 &&
                productGroup?.promotionCriterias?.map(
                  (discount: PromotionCriteria, index) => {
                    const isFirst = index === 0;
                    const value =
                      discount.individualMOQ ||
                      discount.groupMOQ ||
                      discount.totalBuy;
                    let discountValue = 0;

                    if (isFirst) {
                      if (
                        Array.isArray(promotion?.discounts) &&
                        promotion?.discounts?.length
                      ) {
                        discountValue =
                          promotion?.discounts[0].discountValue || 0;
                      }
                    } else if (promotion?.discounts?.length) {
                      discountValue =
                        promotion?.discounts[index].discountValue || 0;
                    }

                    return (
                      <p
                        className="textDescription text-buttonOrange capitalize truncate"
                        key={(promotion?.id || "") + index}
                        style={{
                          maxWidth:
                            isShowArrow || isShowQuestion
                              ? "calc(100% - 3rem)"
                              : "100%",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        Buy {value} {!isFoc ? "discount" : "FOC"}{" "}
                        {discountValue}{" "}
                        {promotion?.discountVolume?.toLowerCase()}
                      </p>
                    );
                  }
                )}
            </div>
          </div>
        </div>
        {isShowArrow && (
          <div className="flex-shrink pt-1">
            <ArrowRight height={25} />
          </div>
        )}
        {isShowQuestion && (
          <div className="flex-shrink pt-1">
            <Vector height={25} />
          </div>
        )}
      </div>
    </div>
  );
};

export const ProductBlockAddMinus: React.FC<ProductBlockProps> = ({
  productName,
  price,
  product,
  // discountPrice,
  // companyName,
  // sellingAmount,
  image,
  className,
  onclick,
}) => {
  return (
    <Row className={className ? className : `flex flex-col p-[10px]`}>
      <Row className="relative flex items-center justify-center h-[130px] w-[160px] bg-white overflow-hidden">
        {product?.tradeType === "PREORDER" ? (
          <div className="absolute top-0 left-0">
            <PreOrder src={PreOrder.src} />
          </div>
        ) : null}

        <img src={image} className="bg-white px-1" />
        {renderPromoIcon(product)}
      </Row>
      <Row className="bg-lightGrey h-[160px] w-[160px] p-2 space-y-1 rounded flex flex-col">
        <p className="font-semibold line-clamp-3 capitalize">
          {productName.toLowerCase()}
        </p>
        {/* <p className="text-[10px] text-gray-400 pt-[2px]">{companyName}</p> */}
        <Row className="flex flex-grow items-end">
          <Row className="flex flex-col flex-grow">
            {/* <Col className="flex font-bold">
              <p className="text-red-500 text-[10px] pt-[5px] pr-[2px]">RM </p>
              <p className="text-red-500 text-[14px]">{discountPrice}</p>
            </Col> */}
            <Col className="flex ">
              <p className="text-description text-buttonOrange">RM {price} </p>
            </Col>
            <Col className="flex">
              <Row className="w-5/5">
                <CounterComponent
                  className="h-[26px] w-[26px] flex items-center justify-center rounded-xl bg-buttonOrange p-4 border-0 text-white font-bold"
                  defaultValue={product.quantity || 0}
                  onCountChange={onclick ? onclick : () => {}}
                />
              </Row>
            </Col>
          </Row>
          {/* <Col>
            <p className="pt-1">{sellingAmount} sold</p>
          </Col> */}
        </Row>
      </Row>
    </Row>
  );
};

export const ModalConfirmUI = (contents: ConfirmModalProps) => {
  return Modal.confirm({
    className: "modalDesign",
    title: contents.title,
    icon: <ExclamationCircleOutlined />,
    content: contents.content,
    okText: contents.okText,
    cancelText: contents.cancelText,
    onOk: contents.onOk,
    onCancel: contents.onCancel,
  });
};

//--------------------------------------------------------------------
// use to display info using modal
export const ModalInfoUI = (contents: ConfirmModalProps) => {
  Modal.info({
    className: "modalDesign",
    title: contents.title,
    icon: <ExclamationCircleOutlined />,
    content: contents.content,
    footer: null, // Hide the default OK/Cancel buttons
    maskClosable: true,
    closable: true,
    onOk: contents.onOk,
    onCancel: contents.onCancel,
  });
};

// Sample for info modal
// ModalInfoUI({
//   title: t("Notice"),
//   content: (
//     <div>
//       <p>We would like to inform you that due to public holidays on <b>September 16th and 17th</b>, the checkout function will be temporarily unavailable.</p>
//       <br />
//       <p>We kindly ask you to try again on <b>September 18th</b>.</p>
//       <br />
//       <p>Thank you for your understanding and patience.</p>
//     </div>
//   ),
//   okText: t("Common.ok"),
//   cancelText: t("Common.cancel"),
//   onOk: () => { },
//   onCancel: () => { },
// });
//--------------------------------------------------------------------
