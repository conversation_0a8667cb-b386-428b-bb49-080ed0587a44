import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import Header, { supportedLocales } from "../../components/header";
import { BackButtonUI } from "@/components/buttonUI";
import { useRouter } from "next/router";
import { DataSource, PUBLIC_BUCKET_URL, encodeParams } from "@/stores/utilize";
import {
  GoodsReturn,
  GoodsReturnProduct,
  Invoice,
  Outlet,
  Product,
  ProductOrdered,
  ProductUOM,
  Retailer,
  SelectOption,
  UOM,
} from "@/components/type";
import { Col, Form, Row } from "antd";
import defaultImage from "../../assets/default/emptyImage.png";
import {
  FormTextInput,
  SelectInput,
  SingleDateInput,
} from "@/components/input";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import moment from "moment";
import apiHelper from "../api/apiHelper";
import { MessageErrorUI, MessageSuccessUI } from "@/components/ui";
import { returnModeOption } from "@/components/config";
import AppFooter from "@/components/footer";

function FullGoodsReturn() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [form] = Form.useForm();

  const [data, setData] = useState<Invoice[]>([]);
  // const [companyMap, setCompanyMap] = useState(new Map());
  // const [staffMap, setStaffMap] = useState(new Map());
  const [outletMap, setOutletMap] = useState(new Map());
  // const [invoiceMap, setInvoiceMap] = useState(new Map());
  const [productMap, setProductMap] = useState(new Map());
  // const [reasonMap, setReasonMap] = useState(new Map());
  const [uomMap, setUomMap] = useState(new Map());

  const [reasonOption, setReasonOption] = useState<SelectOption[]>([]);
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  const [outletData, setOutletData] = useState<Outlet>({});

  // use the retailStoreState;
  const outletId = useRetailerStore((state) => state.currentOutletData?.id);

  const headerItems = [
    {
      label: t("Header.dashboard"),
      route: "/profile/dashboard",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Header.invoice"),
      route: "/invoice/invoiceListing",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Header.goodsReturnSummary"),
      route: "/invoice/fullGoodsReturn",
      className: "labelTextStyle",
    },
  ];

  useEffect(() => {
    if (router.isReady && Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }
      getInvoice();
      getReasons();
    }
  }, [router.isReady, Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0 && outletId) {
      getOutletData(outletId);
    }
  }, [retailerAccess, outletId]);

  const getInvoice = (isClearFilter = false, otherViewBranchAccess = false) => {
    // const accessBranches = otherViewBranchAccess === false ? retailerAccess.companyBranchIds : otherCompanyBranchesIds;
    const ids: string = router.query.ids as string;
    let array: string[] = [];
    if (ids && ids.includes(",")) {
      array = ids.split(",");
    }
    setTimeout(() => {
      let params: any = {
        sort: "createdAt",
        sortOrder: "-1",
        id: array.length > 0 ? array : ids,
      };
      // if (!isAdmin) {
      //   params.companyBranchIds = accessBranches;
      // }
      // const checkAdminRights = isAdmin ? params : params + "&companyId=" + retailerAccess.companyId;
      // const checkFilterRights = filterSetting && !isClearFilter ? filterSetting : encodeParams(params);
      const dataSource = new DataSource(
        "invoices",
        encodeParams(params),
        false
      );

      // !isAdmin && !otherViewBranchAccess ? filterForm.setFieldValue("companyId", retailerAccess.companyId) : null;

      // if ((accessBranches && accessBranches.length > 0) || isAdmin) {
      dataSource
        .load()
        .then((res: any) => {
          if (res && res.items !== null) {
            let data = res.items;
            const objectMap = data.reduce(
              (accumulator: any, current: Invoice) => {
                accumulator["outletId"] = accumulator["outletId"] || [];
                if (
                  current.outletId &&
                  !outletMap.has(current.outletId) &&
                  !accumulator["outletId"].includes(current.outletId)
                ) {
                  accumulator["outletId"].push(current.outletId ?? "");
                }

                current.invoiceProducts?.reduce(
                  (acc: any, product: ProductOrdered) => {
                    accumulator["productId"] = accumulator["productId"] || [];
                    if (
                      product.productId &&
                      !productMap.has(product.productId) &&
                      !accumulator["productId"].includes(product.productId)
                    ) {
                      accumulator["productId"].push(product.productId ?? "");
                    }

                    accumulator["productUOMId"] =
                      accumulator["productUOMId"] || [];
                    if (
                      product.productUOMId &&
                      !uomMap.has(product.productUOMId) &&
                      !accumulator["productUOMId"].includes(
                        product.productUOMId
                      )
                    ) {
                      accumulator["productUOMId"].push(
                        product.productUOMId ?? ""
                      );
                    }

                    return acc;
                  },
                  {}
                );

                return accumulator;
              },
              {}
            );

            getOutlets(objectMap["outletId"]);
            getProduct(objectMap["productId"]);
            getUOM(objectMap["productUOMId"]);
            // getCompanyBranch(objectMap["companyBranchId"]);
            setData(data);
          }
        })
        .catch(() => {
          //* This Part need re-edit*//
        });
      // } else {
      //   setGoodsReturnListingInfoData([]);
      // }
    }, 500);
  };

  const getOutlets = async (id: string[] = []) => {
    let tempProductMap = new Map(outletMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "outlets",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch((error) => {
          id = [];
        });
        if (res !== null && res.items.length > 0) {
          setOutletMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Outlet) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }

    return tempProductMap;
  };

  const getProduct = async (id: string[] = []) => {
    let tempProductMap = new Map(productMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "productCatalogues",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setProductMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Product) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Product) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getUOM = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("uoms", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setUomMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: UOM) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: UOM) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getReasons = () => {
    const dataSource = new DataSource(
      "reason/supplierGoodsReturn/create",
      "status?=ACTIVE",
      true
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null) {
          let reasonOption: SelectOption[] = [];
          if (res !== null) {
            res.map((value: any) => {
              reasonOption.push({
                value: value.id,
                label: value.description,
              });
            });
          }
          setReasonOption(reasonOption);
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getOutletData = async (id: string[] = []) => {
    const dataSource = new DataSource(
      "outlets",
      encodeParams({ id: id }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setOutletData(res.items?.[0]);
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const submitGoodsReturn = () => {
    const formValue = form.getFieldsValue();

    const goodsReturnDataArray: GoodsReturn[] = [];

    data.map((item) => {
      let remapProduct: GoodsReturnProduct[] = [];

      item.invoiceProducts?.forEach((val) => {
        let tempData: any = {
          ...val,
          returnType: "FULL",
          invoiceId: item.id,
          reasonId: formValue[`reason${item.id}`],
          returnUOMId: val.productUOMId,
          returnQuantity: val.quantity,
        };

        remapProduct.push(tempData);
      });

      const d = new Date(formValue[`returnDate${item.id}`]);
      let goodsReturnData: GoodsReturn = {
        returnDate: moment(d).format("YYYY-MM-DDT00:00:00") + "Z",
        returnMode: formValue[`returnMode${item.id}`],
        outletId: item.outletId,
        remark: formValue[`remark${item.id}`],
        products: remapProduct,
        staffId: "655da18670f71f16dee21d78",
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchIds?.[0],
        companyBranchId: outletData?.companyBranchId ?? "",
      };

      goodsReturnDataArray.push(goodsReturnData);
    });

    goodsReturnDataArray.forEach((item) => {
      apiHelper
        .POST("goodsReturn", item, {})
        ?.then(() => {
          MessageSuccessUI(t("Approval.Submit.Success"));
          router.push("/goodsReturn/goodsReturnListing");
        })
        ?.catch(() => {
          //* This Part need re-edit*//
          MessageErrorUI(t("Approval.Submit.Failed"));
        });
    });
  };

  const buttons = [
    {
      label: t("Invoice.makePayment"),
      onClick: submitGoodsReturn,
      // disabled: !(selectedRowData.length > 0),
    },
  ];

  const showContent = () => {
    return (
      <div className="flex flex-col gap-y-7">
        <BackButtonUI
          title={t("Invoice.goodsReturnSummary")}
          buttons={buttons}
        ></BackButtonUI>

        <div className="bg-white flex-row flex justify-between py-2 font-bold">
          <p className="w-2/5"></p>
          <p className=" w-1/5 flex justify-center">{t("Invoice.invoiceNo")}</p>
          <p className=" w-1/5 flex justify-center">
            {t("Invoice.invoiceQuantity")}
          </p>
          <p className="w-1/5 flex justify-center">
            {t("Invoice.returnQuantity")}
          </p>
        </div>
        {data.map((invoiceData) => {
          return (
            <Row className="w-full">
              <div className="bg-purple px-5 py-1 w-full" key={invoiceData.id}>
                <p className="text-textBlue font-bold">
                  {outletMap.get(invoiceData.outletId)?.name}
                </p>
              </div>
              {invoiceData.invoiceProducts?.map((item: ProductOrdered) => {
                const product = productMap.get(item.productId);
                return (
                  <div
                    className="w-full flex-col border-gray-300"
                    key={item.productId}
                  >
                    <Row className="w-full flex justify-between  bg-white">
                      <Col className="flex items-center pt-1 w-2/5">
                        <img
                          className="object-contain h-[80px] w-[80px] p-2"
                          src={(() => {
                            const pictures = product?.productUOM.find(
                              (val: ProductUOM) =>
                                val.productUOMId === item.productUOMId
                            )?.pictures;
                            return pictures
                              ? PUBLIC_BUCKET_URL + pictures
                              : defaultImage.src;
                          })()}
                          loading="lazy"
                          alt="Product Image"
                        />
                        <div className="flex flex-col w-full">
                          <p className="font-bold text-[14px]">
                            {product?.name}&nbsp;
                          </p>
                          <p className="text-gray-500 text-[10px] w-full flex ">
                            <span>
                              {t("Invoice.productCode")}: {item.promotionCodes}
                            </span>
                          </p>
                          <p className="text-gray-500 text-[10px] w-full flex ">
                            <span>
                              {t("Invocie.unitPrice")}: {item.price}
                            </span>
                          </p>
                          <p className="text-gray-500 text-[10px] w-full flex ">
                            <span>
                              {t("Invoice.taxRate")}: {item.taxRate}%
                            </span>
                          </p>
                        </div>
                      </Col>

                      <Col className="flex items-center justify-center w-1/5">
                        {invoiceData.invoiceNo}
                      </Col>
                      <Col className="flex items-center justify-center w-1/5">
                        {item.quantity +
                          " " +
                          uomMap.get(item.productUOMId)?.name}
                      </Col>
                      <Col className="flex items-center justify-center w-1/5">
                        <p>
                          {item.quantity +
                            " " +
                            uomMap.get(item.productUOMId)?.name}
                        </p>
                      </Col>
                    </Row>
                  </div>
                );
              })}
              <div
                className="bg-lightGrey px-4 py-2 w-full "
                key={invoiceData.id}
              >
                <Form
                  className="w-full "
                  form={form}
                  layout="horizontal"
                  scrollToFirstError
                >
                  <Row className="w-full gap-y-2">
                    <Row className="w-full flex gap-x-3">
                      <Form.Item
                        name={"reason" + invoiceData.id}
                        className="flex-1"
                        /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Invoice.reason")}
                          </p>
                        }
                      >
                        <SelectInput
                          placeholder={t("Invoice.selectReason")}
                          showArrow={true}
                          options={reasonOption}
                          onChange={() => { }}
                        />
                      </Form.Item>
                      <Form.Item
                        name={"returnMode" + invoiceData.id}
                        className="flex-1"
                        /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Invoice.returnMode")}
                          </p>
                        }
                      >
                        <SelectInput
                          placeholder={t("Invoice.selectReturnMode")}
                          showArrow={true}
                          options={returnModeOption}
                          onChange={() => { }}
                        />
                      </Form.Item>
                      <Form.Item
                        name={"returnDate" + invoiceData.id}
                        className="flex-1 w-1/5"
                        /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Invoice.returnDate")}
                          </p>
                        }
                      >
                        <SingleDateInput onChange={() => { }} />
                      </Form.Item>
                    </Row>
                    <Row className="w-full">
                      <Form.Item
                        name={"remark" + invoiceData.id}
                        className="flex-1"
                        /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                          <p className="text-neutral700 text-[12px]">
                            {t("Invoice.remark")}
                          </p>
                        }
                      >
                        <FormTextInput
                          placeholder={t("Invoice.remark")}
                          maxLength={100}
                        />
                      </Form.Item>
                    </Row>
                  </Row>
                </Form>
              </div>
            </Row>
          );
        })}
      </div>
    );
  };

  return (
    <div className="flex-1 bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => { }} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
      </Content>
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default FullGoodsReturn;
