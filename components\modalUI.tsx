import { Modal } from "antd";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import React from "react";

interface ModalProps {
  content?: React.ReactNode;
  visible: boolean;
  width?: string;
  onOk?: () => void;
  onCancel?: () => void;
  okText?: string;
  cancelText?: string;
  footer?: React.ReactNode;
  title: string | React.ReactNode;
  className?: string;
  closable?: boolean;
  destroyOnClose?: boolean;
  maskClosable?: boolean;
}

interface ConfirmModalProps {
  title: any;
  content: React.ReactNode;
  okText: string;
  cancelText?: string;
  onOk: () => void;
  onCancel?: () => void;
  className?: string;
}

export const ModalUI: React.FC<ModalProps> = ({
  className,
  title,
  content,
  visible,
  width,
  onOk,
  onCancel,
  destroyOnClose,
  footer,
  maskClosable,
  closable,
}) => (
  <Modal
    className={className}
    closable={closable}
    footer={footer || null}
    open={visible}
    title={title}
    width={width}
    onOk={onOk}
    onCancel={onCancel}
    destroyOnClose={destroyOnClose}
    maskClosable={maskClosable}
  >
    {content}
  </Modal>
);

export const ModalConfirmUI = (contents: ConfirmModalProps) => {
  return Modal.confirm({
    className: "modalDesign",
    title: contents.title,
    icon: <ExclamationCircleOutlined />,
    content: contents.content,
    okText: contents.okText,
    cancelText: contents.cancelText,
    onOk: contents.onOk,
    onCancel: contents.onCancel,
  });
};
