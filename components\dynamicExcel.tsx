import * as XLSX from "xlsx";
import saveAs from "file-saver";
import { Message<PERSON>rrorUI, MessageInfoUI, MessageSuccessUI, ModalConfirmUI } from "./ui";
import { convertFieldsToNumber, convertFieldsToString, convertFieldsToTime, DataSourceWithPageNumber, getParamsFromLocalStorage } from "../stores/utilize";
import { Button, Modal, Progress, Row, Upload } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import _ from "lodash";
import apiHelper from "../pages/api/apiHelper";
import { useUploadPercentageStore } from "../stores/store";

// const { t } = useTranslation("common");

// *************************************************************************************
// *** EXCEL FILE - GENERATE FUNCTION ***
// *************************************************************************************
export const generateExcelReport = async (pathName: string, data: any) => {
    const transformedText = transformToPlural(pathName);

    // check if page are in filter state
    const filterParams = getParamsFromLocalStorage(transformedText, `${pathName}Filter`);

    const dataSource = new DataSourceWithPageNumber(`${transformedText}`, filterParams, true);
    const res: any = await dataSource.load();

    let excelDataExport: any[] = [];
    let message = "";
    if (filterParams && filterParams !== "") {
        excelDataExport = data;
        message = "Filter Data are Generated";
    } else {
        excelDataExport = res;
        message = "Full Data are Generated";
    }

    // data sheet mapping
    const allExcelData = excelDataExport.map((item: any) => {
        const { id, ...row } = item;
        for (const key in row) {
            if (Object.prototype.hasOwnProperty.call(row, key)) {
                row[key] = row[key] ?? "NULL";
                if (row[key] === "" || row[key] === undefined || row[key] === null) {
                    row[key] = "NULL";
                }
            }

            // remove data if key includes Id
            if (key?.includes("Id")) {
                delete row[key]
            }
        }
        return { id, ...row };
    });

    // Export excel data
    exportExcelFunction({
        fileName: pathName,
        dataSource: { pathName: allExcelData },
        isProtect: "AUTO",
        isDynamicColumn: true,
        isAutoFilterOn: true,
    })

    MessageSuccessUI(message);
}

// *************************************************************************************
// *** EXCEL FILE - IMPORT FUNCTION ***
// *************************************************************************************
//bulk update Modal
export const bulkUpdateModal = (dataSource: { [key: string]: any[] }, pathName: string, t: any) => {
    ModalConfirmUI({
        title: t("ImportExcel"),
        // title: "Import Data by Excel",
        content: (
            <div className="flex flex-col justify-center items-center gap-y-2">
                <Row className="mt-3">
                    <Upload
                        onChange={(info) => handleFileChange(info, dataSource, pathName, t)}
                        showUploadList={{
                            showPreviewIcon: false,
                        }}
                        className="uploaderRoutePlan"
                        beforeUpload={(file) => {
                            if (file.type !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" && file.type !== "application/vnd.ms-excel") {
                                MessageErrorUI(`${file.name} is an invalid file format. Please change the file extension to .xlsx or .xls`);
                                return Upload.LIST_IGNORE;
                            } else if (file.size > 5242880) {
                                MessageErrorUI(`${file.name} is too large. Please upload another document that is smaller than 5MB.`);
                                return Upload.LIST_IGNORE;
                            } else {
                                return false;
                            }
                        }}
                        maxCount={1}
                        customRequest={() => { }}
                    >
                        <div className="flex items-center gap-x-4 text-[14px] p-2 border-dashed border-2 text-brightBlue500 bg-lightWhite font-semibold">
                            <PlusOutlined />
                            <p>{t("UploadExcel")}</p>
                            {/* <p>Upload Excel</p> */}

                        </div>
                    </Upload>
                </Row>
                <Row>
                    <Button
                        className="flex flex-1 p-0 text-[11px]"
                        type="link"
                        onClick={() => exportExcelFunction({
                            dataSource: dataSource,
                            isProtect: "AUTO",
                            isDynamicColumn: true,
                            isAutoFilterOn: true,
                        })}
                    >
                        {t("ExcelSample")}
                        {/* <p>Download Sample Excel Format</p> */}
                    </Button>
                </Row>
            </div>
        ),
        okText: "Yes",
        cancelText: "No",
        onOk: () => {
            // setNumber(number + 1);
        },
        onCancel: () => {
            // isModalVisible = false;
            // setMyData([]);
        },
    });
};

export const handleFileChange = (info: any, dataSource: { [key: string]: any[] }, pathName: string, t: any) => {
    const file = info.file;
    const reader = new FileReader();
    if (file) {
        reader.onload = async (event: any) => {
            const data: ArrayBuffer | null = event.target ? event.target.result : null;
            if (data) {
                // check if excel uploaded is empty
                // if (isEmptyObject(dataSource)) {
                // MessageErrorUI(t("ErrExcelSheetorDataNotFound"));
                //     return;
                // }

                const workbook = XLSX.read(data, { type: "array" });
                // use to store data read from excel
                let jsonData: { [key: string]: any[] } = {};
                let keyType: { [key: string]: any[] } = {};

                // use to store data after processed
                let modifiedData: { [key: string]: any[] } = {};
                let errorData: { [key: string]: any } = {};

                // determine whether remap needed
                let isMappingNeeded: boolean = false
                let errorMappingCode: string[] = []

                // Convert data from Excel to JSON format
                const sheetNames = Object.keys(dataSource);

                if (sheetNames && sheetNames.length > 0 && pathName !== sheetNames[1]) {
                    MessageErrorUI(t("ErrExcelSheetorDataNotFound"));
                }

                // Pump in to respective variables
                for (let i = 0; i < sheetNames.length; i++) {
                    const sheetName = sheetNames[i];
                    const worksheet = workbook.Sheets[sheetName];

                    // Skip protected sheet
                    if (sheetName.includes("ReadMe") || sheetName.includes("SelectionOptions") || sheetName.includes("productList(P!)")) continue;

                    // Special handle for keys conversion
                    if (sheetName.includes("keyType") && worksheet) {
                        const keyTypeJson: any[] = XLSX.utils.sheet_to_json(worksheet);
                        for (let j = 0; j < keyTypeJson.length; j++) {
                            const row = keyTypeJson[j];
                            for (const key in row) {
                                if (Object.prototype.hasOwnProperty.call(row, key)) {
                                    const value = row[key];
                                    keyType[key] = keyType[key] ? [...keyType[key], value] : [value];
                                }
                            }
                        }
                    } else if (worksheet) {
                        jsonData[sheetName] = XLSX.utils.sheet_to_json(worksheet);
                    }
                }
                // Remap the data type to pre-defined type
                if (!isEmptyObject(jsonData) && !isEmptyObject(keyType)) {
                    for (const [sheetName, rows] of Object.entries(jsonData)) {
                        for (let val = 0; val < rows.length; val++) {
                            const rowData = rows[val];
                            // assigning index for better display when in error 
                            rowData.indexNo = val + 1;

                            Object.keys(keyType).forEach((key) => {
                                switch (key) {
                                    case "string":
                                        // Convert fields to string
                                        convertFieldsToString(rowData, keyType["string"]);
                                        break;

                                    case "number":
                                        // Convert fields to number
                                        convertFieldsToNumber(rowData, keyType["number"]);
                                        break;

                                    case "time":
                                        // Convert fields to time
                                        convertFieldsToTime(rowData, keyType["time"]);
                                        break;

                                    case `${sheetName}(RP!)`:
                                        // Check which required keys are missing or invalid
                                        validateRequiredFields(sheetName, rowData, keyType);
                                        break;

                                    default:
                                        validateMissingOrInvalidFields(rowData)
                                        // No action for unknown keys
                                        break;
                                }
                            });

                            // Check if data mapping is require
                            if ("newKeyMapping" in rowData) {
                                isMappingNeeded = true
                            }
                            if (!rowData.hasAllRequiredFields || (rowData?.newKeyMapping && errorMappingCode.includes(rowData?.newKeyMapping))) {
                                errorData[`${sheetName}`] = errorData[`${sheetName}`] ? [...errorData[`${sheetName}`], rowData] : [rowData]

                                // Add all mappingCode which related to error data
                                if (isMappingNeeded) {
                                    const existingErrMappingCode = Array.from(new Set([...errorMappingCode, rowData?.newKeyMapping]));
                                    errorMappingCode = existingErrMappingCode
                                }

                            } else {
                                modifiedData[`${sheetName}`] = modifiedData[`${sheetName}`] ? [...modifiedData[`${sheetName}`], rowData] : [rowData]
                            }

                        }
                    }

                    // Recheck on whether there related data from errMappingKey
                    if (isMappingNeeded) {
                        recheckError(modifiedData, errorMappingCode)
                        for (const [sheetName, rows] of Object.entries(modifiedData)) {
                            for (let val = 0; val < rows.length; val++) {
                                const rowData = rows[val];
                                if (!rowData.hasAllRequiredFields || (rowData?.newKeyMapping && errorMappingCode.includes(rowData?.newKeyMapping))) {
                                    errorData[`${sheetName}`] = errorData[`${sheetName}`] ? [...errorData[`${sheetName}`], rowData] : [rowData]
                                    modifiedData[sheetName].splice(val, 1);
                                }
                            }
                        }

                    }
                }

                // remap undergoing from here
                let expectedResult: any = []
                if (isMappingNeeded) {
                    expectedResult = transformDataDynamic(modifiedData, keyType[`layering`]);
                }

                // handling if error data exist, then export in excel
                if (errorData && Object.keys(errorData).length) {
                    let errorDataDisplay: { [key: string]: any } = {}
                    for (const [sheetName, data] of Object.entries(errorData)) {

                        // process line by line to read the error data
                        for (let i = 0; i < data.length; i++) {
                            if (data[i].missingFields && data[i].missingFields.length > 0) {
                                const missingFields = { note: `Missing or Invalid field(s) for data line ${data[i]?.indexNo} included: ` + data[i].missingFields.join(", ") }
                                // If no entry exists for the sheetName, initialize it
                                if (!errorDataDisplay[sheetName]) {
                                    errorDataDisplay[sheetName] = []
                                    errorDataDisplay[sheetName] = [missingFields];
                                } else {
                                    // Append missingFields to the existing entry
                                    errorDataDisplay[sheetName] = [...errorDataDisplay[sheetName], missingFields];
                                }

                            }
                        }
                    }

                    ModalConfirmUI({
                        // title: t("ErrorDataInserted"),
                        // content: t("PrintErrorExcel"),
                        // okText: t("Confirm.Delete.Form.3"),
                        // cancelText: t("Confirm.Delete.Form.4"),
                        title: "Error Data Inserted",
                        content: "Do you want to print out the error data in excel?",
                        okText: "Yes",
                        cancelText: "No",
                        onOk: () => {
                            // Export excel data
                            exportExcelFunction({
                                fileName: "ErrorData",
                                dataSource: errorDataDisplay,
                                isProtect: "AUTO",
                                isDynamicColumn: false,
                                isAutoFilterOn: false,
                            })
                        },
                        onCancel: () => { },
                    });

                } else {
                    upsertExcelData(expectedResult, pathName)
                }
            }
        };
        reader.readAsArrayBuffer(file);
    }
};

// *************************************************************************************
// *** EXCEL FILE - Props ***
// *************************************************************************************
interface ProtectProps {
    password: string;
    objects: boolean;
    scenarios: boolean;
    formatCells: boolean;
    formatColumns: boolean;
    formatRows: boolean;
    insertColumns: boolean;
    insertRows: boolean;
    deleteColumns: boolean;
    deleteRows: boolean;
    selectLockedCells: boolean;
    selectUnlockedCells: boolean;
    autoFilter: boolean;
    sort: boolean;
}

interface ExcelPropsType {
    fileName?: string;
    isProtect: "TRUE" | "FALSE" | "AUTO";
    isProtectProps?: any;
    isDynamicColumn: boolean;
    isAutoFilterOn: boolean;
    dataSource: { [key: string]: any[] };
}

const defaultProtectProps: ProtectProps = {
    password: "yourPassword", // Optional password
    objects: true,           // Protect shapes
    scenarios: true,         // Protect scenarios
    formatCells: false,      // false: allow cell formatting
    formatColumns: false,    // false: allow column formatting
    formatRows: false,       // false: allow row formatting
    insertColumns: true,    // true: disallow column insertion
    insertRows: true,       // true: disallow row insertion
    deleteColumns: true,    // true: disallow column deletion
    deleteRows: true,       // true: disallow row deletion
    selectLockedCells: false, // false: allow selecting locked cells
    selectUnlockedCells: false, // false: allow selecting unlocked cells
    autoFilter: false, // false: allow autoFilter
    sort: false, // false: allow sort
}

// Export excel
export const exportExcelFunction = ({ fileName = "sample", dataSource, isProtect, isDynamicColumn, isAutoFilterOn, isProtectProps = defaultProtectProps }: ExcelPropsType) => {
    // Check if dataSource have any data
    if (Object.keys(dataSource).length === 0 || isEmptyObject(dataSource)) {
        MessageInfoUI("No dataSource provided.");
        return;
    }

    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Prepare workbook metadata for hidden sheets
    workbook.Workbook = workbook.Workbook || { Sheets: [] };

    // Iterate through the keys of informationText and generate sheets
    for (const [sheetName, sheetData] of Object.entries(dataSource)) {
        let worksheet: any;

        // Remove "id" key and other unnecessary fields from all objects in sheetData
        const unnecessaryFields = ['deletedAt']
        const cleanedSheetData = sheetData.map((row: any) =>
            Object.fromEntries(Object.entries(row).filter(([key]) => !key.includes("Id") && !unnecessaryFields.includes(key)))
        );

        // Break sheetName when detected keyType
        if (sheetName.includes("keyType")) {
            // Convert and append the keyType worksheet
            const transformedKeyType = transformKeyTypeForExcel(cleanedSheetData);
            worksheet = XLSX.utils.json_to_sheet(transformedKeyType);

            // Append the sheet
            XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

            // Mark the sheet as hidden
            const sheetIndex = workbook.SheetNames.indexOf(sheetName);
            if (workbook.Workbook.Sheets) {
                workbook.Workbook.Sheets[sheetIndex] = workbook.Workbook.Sheets[sheetIndex] || {};
                workbook.Workbook.Sheets[sheetIndex].Hidden = 1;
            }
        } else {
            worksheet = XLSX.utils.json_to_sheet(cleanedSheetData);

            // Append the sheet
            XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
        }

        // If define as protected then lock the related sheet
        if (isProtect === "TRUE" || (isProtect === "AUTO" && sheetName.includes("P!"))) {
            worksheet['!protect'] = isProtectProps;
        }

        // Dynamically calculate column widths based on content length
        if (isDynamicColumn) {
            const columnWidths = Object.keys(dataSource[sheetName][0] || {}).map((key) => {
                const maxLength = Math.max(
                    key.length, // Header length
                    ...dataSource[sheetName].map((row) => {
                        const value = row[key];
                        if (Array.isArray(value)) {
                            // Calculate the maximum length of elements in the array
                            return Math.max(...value.map((item) => item.toString().length), 0);
                        }
                        return value ? value.toString().length : 0; // Default for non-array values
                    })
                );

                return { width: maxLength + 2 }; // Add padding for better spacing
            });
            worksheet["!cols"] = columnWidths;
        }

        // Dynamically calculate the range for the autofilter
        if (isAutoFilterOn) {
            const columnCount = Object.keys(dataSource[sheetName][0] || {}).length; // Get the number of columns
            if (columnCount > 0) {
                const lastColumnLetter = XLSX.utils.encode_col(columnCount - 1); // Get the last column letter
                worksheet['!autofilter'] = { ref: `A1:${lastColumnLetter}1` }
            }
        }

        // XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    }

    // Generate the Excel file and trigger a download
    const excelFile = XLSX.write(workbook, { type: "array", bookType: "xlsx" });
    saveAs(new Blob([excelFile]), `${fileName}Excel.xlsx`);
};

// *************************************************************************************
// *** API call ***
// *************************************************************************************
async function upsertExcelData(data: any, pathName: any, version: string = "v1") {
    if (!pathName) {
        console.log("pathName is incorrect")
        return;
    }

    const batchSize = 50;
    const totalEntries = data.length;
    const numBatches = Math.ceil(totalEntries / batchSize);
    let successfulCount = 0; // Variable to track successful API calls
    // set to default value
    useUploadPercentageStore.setState({ percentage: 0 });
    useUploadPercentageStore.setState({ isUplaod: false });
    try {
        useUploadPercentageStore.setState({ isUplaod: true });
        for (let i = 0; i < numBatches; i++) {
            const startIdx = i * batchSize;
            const endIdx = (i + 1) * batchSize;
            const batchData = data.slice(startIdx, endIdx);
            // POST when no id found
            // if (!("id" in batchData[0])) {
            await apiHelper.POST(`${pathName}`, batchData[0], "", version);
            // }

            // PUT when id found
            // if ("id" in batchData[0]) {
            //     await apiHelper.PUT("productCatalogue?id=" + batchData[0].id, batchData[0]).catch(() => MessageErrorUI(`Unable to update Product with id: ${batchData[0]?.id}`));
            // }
            successfulCount += batchData.length; // Increment successful count
            let precent = (successfulCount / totalEntries) * 100;
            useUploadPercentageStore.setState({ percentage: precent });
        }

        // MessageSuccessUI(t("Approval.Submit.Success"));
        MessageSuccessUI("Request Submitted for Approval");
    } catch (err) {
        // MessageErrorUI(t("Approval.Submit.Failed"));
        MessageErrorUI("Failed to Submit Request for Approval");
    }

    // getAllProducts(true);

    // MessageSuccessUI(successfulCount + " " + t("ExcelDoneCreate/Update"));
    MessageSuccessUI(successfulCount + " data has been created/updated");
    // set back to 0 and false
    useUploadPercentageStore.setState({ percentage: 0 });
    useUploadPercentageStore.setState({ isUplaod: false });
}

export const showUploading = (t: any) => {
    const percentage = useUploadPercentageStore((state: any) => state.percentage);
    const isUpload = useUploadPercentageStore((state: any) => state.isUpload);
    return (
        <Modal className="mt-24" closable={false} open={isUpload} footer={null}>
            <div className="h-160px flex justify-center items-center flex-col">
                <p className="font-bold text-xl mb-4">{t('Uploading...')}</p>
                <Progress type="circle" percent={percentage} />
            </div>
        </Modal>
    )
};

// *************************************************************************************
// *** Utilize - Props for Excel ***
// *************************************************************************************
const transformToPlural = (text: string): string => {
    if (text.endsWith("y") && !/[aeiou]y$/.test(text)) {
        // If the word ends with 'y' and it's not preceded by a vowel, replace 'y' with 'ies'
        return text.slice(0, -1) + "ies";
    } else if (text.endsWith("s") || text.endsWith("x") || text.endsWith("z") || text.endsWith("ch") || text.endsWith("sh")) {
        // If the word ends with 's', 'x', 'z', 'ch', or 'sh', add 'es'
        return text + "es";
    } else {
        // Default case: just add 's'
        return text + "s";
    }
};

// check if excel sheet data is empty
export function isEmptyObject(obj: any): boolean {
    // Check if an object is empty or contains only empty nested objects/arrays
    return (
        Object.keys(obj).length === 0 ||
        Object.values(obj).every(value => {
            if (Array.isArray(value)) {
                return value.length === 0 || value.every(item => isEmptyObject(item));
            } else if (typeof value === 'object' && value !== null) {
                return isEmptyObject(value);
            }
            return false;
        })
    );
}

// Check which required keys are missing or invalid
export function validateRequiredFields(sheetName: string, rowData: any, keyType: Record<string, string[]>): void {
    // Determine the required fields for the given sheetName
    const requiredFields = keyType[`${sheetName}(RP!)`];

    // Identify missing or invalid fields
    const missingFields = requiredFields.filter((field) => {
        return !(field in rowData) || rowData[field] === null || rowData[field] === undefined;
    });

    // Update rowData with validation results
    rowData.hasAllRequiredFields = missingFields.length === 0;
    rowData.missingFields = missingFields;
    // Bypass requiredField checking if 'id' exists and is valid
    if (("id" in rowData) && rowData.id && rowData.id.trim()) {
        rowData.hasAllRequiredFields = true;
        rowData.missingFields = []; // Clear missing fields for updates
    }
}

// Function to find missing or invalid fields dynamically
export const validateMissingOrInvalidFields = (rowData: { [key: string]: any }): any => {
    const data = Object.keys(rowData).filter((field) => {
        const value = rowData[field];
        // Check for missing or invalid data
        return (
            value === null || // Field is explicitly null
            value === undefined || // Field is undefined
            (typeof value === "string" && value.includes("Invalid date")) || // Field contains "Invalid date"
            (typeof value === "number" && isNaN(value)) // Field is a number but invalid (NaN)
        );
    });

    if (data && data.length > 0) {
        rowData.hasAllRequiredFields = false;

        const existingMissingFields = rowData?.missingFields ?? [];
        rowData.missingFields = Array.from(new Set([...existingMissingFields, ...data]));
    }
};

/**
 * @param P! 'meaning protected sheet'
 * @param RR! 'meaning required field'
 */


// Perform construct new Json structure which regrouping/remapping based on mappingKey
export function transformDataDynamic(source: any, configSetting: any) {
    const config = transformExcelToJson(configSetting);
    const processData = (key: string, config: any, source: any) => {
        const { mergeKey, child } = config;

        // Fetch the current data set
        const data = source[key] || [];

        // Map through the parent data
        return data.map((item: any) => {
            const result: any = { ...item };

            // Process child configurations if present
            if (child) {
                for (const childKey in child) {
                    const childConfig = child[childKey];
                    const childData = source[childKey]?.filter(
                        (childItem: any) =>
                            childItem[childConfig.mergeKey] === item[mergeKey]
                    );

                    if (childData && childData.length > 0) {
                        switch (childConfig.mode) {
                            case "array":
                                // Assign as an array
                                result[childKey] = childData;
                                break;
                            case "object":
                                // Assign as a single object (first match)
                                result[childKey] = childData[0] || {};
                                break;
                            case "direct":
                                // Flatten properties into the parent object
                                Object.assign(result, childData[0] || {});
                                break;
                            default:
                                console.warn(`Unknown mode: ${childConfig.mode}`);
                        }
                    }
                }
            }

            return result;
        });
    };

    for (const key in config) {
        if (source[key]) {
            return processData(key, config[key], source);
        }
    }

    return [];
}

// Handling again when mappingKey appear
export function recheckError(source: any, errCodes: string[]) {
    for (const sheetName of Object.keys(source)) {
        for (let i = 0; i < source[sheetName].length; i++) {
            if (source[sheetName][i]?.newKeyMapping) {
                if (errCodes.includes(source[sheetName][i].newKeyMapping)) {
                    source[sheetName][i].hasAllRequiredFields = false;

                    const existingMissingFields = source[sheetName][i]?.missingFields ?? [];
                    source[sheetName][i].missingFields = Array.from(new Set([...existingMissingFields, "mapped data incorrect"]));

                }
            }
        }
    }
    return source
}

/**
 * as below was mode can be selected in mapping type
 * @param formatParams // parentKey-childKey-mergeKey-mode in excel (exp: "productCatalogue-productUOM-newKeyMapping-array")
 * @param array // Nesting productUOM as an array
 * @param direct // Merges productUOM properties directly into parent
 * @param object // Includes only the first matching parent
 */

interface Config {
    [key: string]: {
        mergeKey: string;
        mode: "array" | "object" | "direct";
        child?: Config;
    };
}

// Based on configSetting set a new config format
const transformExcelToJson = (excelFilePath: string[]): Config => {
    // Transform data
    const result: Config = {};

    excelFilePath.forEach((row: any) => {
        const [parentKey, childKey, mergeKey, mode] = row.split("-");
        if (!result[parentKey]) {
            result[parentKey] = {
                mergeKey,
                mode,
            };
        }

        if (childKey) {
            result[parentKey].child = {
                ...(result[parentKey].child || {}), // Retain existing children
                [childKey]: {
                    mergeKey,
                    mode,
                },
            };
        }
    });

    return result;
};

// Convert keyType to a format suitable for Excel
export const transformKeyTypeForExcel = (keyType: any[]) => {
    const categories = Object.keys(keyType[0]);
    const maxLengths = categories.map((category) => keyType[0][category].length);

    // Create a 2D array where each column corresponds to a category
    const transformedData = [];
    for (let i = 0; i < Math.max(...maxLengths); i++) {
        const row: any = {};
        categories.forEach((category) => {
            if (keyType[0][category][i]) {
                row[category] = keyType[0][category][i] || ""; // Fills empty cells with an empty string
            }
        });
        transformedData.push(row);
    }

    return transformedData;
};

// must have each sheetName(RP!)