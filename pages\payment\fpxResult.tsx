import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Result, Spin } from "antd";
import { Fpx } from "@/components/type";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/router";
import { FpxDefaultValues } from "@/components/defaultValue";
import apiHelper from "../api/apiHelper";
import { NumberThousandSeparator } from "@/stores/utilize";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { supportedLocales } from "@/components/header";

import { NextApiRequest, NextApiResponse } from "next";

const FpxResult: React.FC = () => {
  const [fpxData, setFpxData] = useState<Fpx>(FpxDefaultValues);
  const { t } = useTranslation("common");
  const router = useRouter();
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (router.isReady) {
      fetchFpxData();
    }
  }, [router.isReady]);

  const fetchFpxData = async () => {
    try {
      setLoading(true);

      const fpxId = sessionStorage.getItem("fpxId");

      if (!fpxId) return;

      const fetchResult: any = await apiHelper.GET(
        `payment/fpxes?id=${fpxId}`,
        {},
        "",
        "v2"
      );

      if (fetchResult?.items && fetchResult?.items.length > 0) {
        setFpxData(fetchResult.items[0]);
      }
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const navigateBackToListing = () => {
    sessionStorage.removeItem("fpxId");
    // router.replace("../payments/paymentListing");
    router.push(`/payment/paymentListing`);
  };

  const ShowSuccessUI = () => (
    <Result
      className="absolute left-0 m-0 top-[50%] translate-y-[-50%] w-full"
      status="success"
      title={`${t("Fpx.Successfultitle")}`}
      subTitle={`RM ${NumberThousandSeparator(fpxData?.totalAmt ?? 0)} ${t(
        "Fpx.SuccessfulSubTitle"
      )}`}
      extra={[
        <Button type="primary" key="console" onClick={navigateBackToListing}>
          {t("Fpx.successfulButton")}
        </Button>,
      ]}
    />
  );

  const ShowProcessingUI = () => (
    <Result
      className="absolute left-0 m-0 top-[50%] translate-y-[-50%] w-full"
      status="info"
      title={t("Fpx.ProcessingTitle")}
      subTitle={t("Fpx.ProcessingSubTitle")}
      extra={[
        <Button type="primary" key="console" onClick={navigateBackToListing}>
          {t("Fpx.ProcessingButton")}
        </Button>,
      ]}
    />
  );

  const ShowFpxFailUI = () => (
    <Result
      className="absolute left-0 m-0 top-[50%] translate-y-[-50%] w-full"
      status="error"
      title={t("Fpx.UnsuccessfulTitle")}
      subTitle={t("Fpx.UnSuccessfulSubTitle")}
      extra={[
        <Button type="primary" key="console" onClick={navigateBackToListing}>
          {t("Fpx.UnsuccessfulButton")}
        </Button>,
      ]}
    />
  );

  if (loading) {
    return (
      <div
        style={{
          position: "absolute",
          left: 0,
          width: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <Spin size="large" />
      </div>
    );
  }
  if (fpxData?.status === "SUCCESS") {
    return <ShowSuccessUI />;
  }
  if (fpxData?.status === "FAILED") {
    return <ShowFpxFailUI />;
  }

  return <ShowProcessingUI />;
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default FpxResult;
