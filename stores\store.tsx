import { create } from "zustand";
import { MessageErrorUI } from "../components/ui";
import { handleApiError } from "./utilize";
import type { MenuProps } from 'antd';
import { OutletNameAPI } from "@/components/type";

export interface Retailer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  contact: string;
  ic: string;
  companyId: string;
  staffLevelId: string;
  roleIds: string[];
  policies: string[] | {};
  viewSubDistributor: string;
  filterParams: { [key: string]: string };
}

interface RetailerStoreState {
  err: any[]; // Adjust the type of err as needed
  retailer: Retailer | null;
  outletNameData: OutletNameAPI | null;
  error: boolean;
  clear: () => void;
  reset: () => void;
  displayError: () => void;
  isAdmin: boolean;
  isCheckingAdmin: boolean;
  isRetailerAdmin: boolean;
  policies: Record<string, any>; // Adjust the type of policies as needed
  filterParams: { [key: string]: string };
  currentOutletData: any;
  outletSelection: MenuProps['items']; // Adjust the type of outletSelection as needed
  cart: number;
  setCart: (newCart: number) => void;
  setCurrentOutlet: (newOutletId: any) => void;
  setOutletSelection: (outletSelection: MenuProps['items']) => void;
}

const useRetailerStore = create<RetailerStoreState>((set, get) => ({
  err: [],
  retailer: null,
  outletNameData: null,
  error: false,
  clear: () => set({ retailer: null }),
  reset: () => set({ error: false }),
  displayError: () => {
    let errorData = get();
    const errorMessage = handleApiError(errorData.err);
    if (!errorMessage.includes("Documents")) {
      MessageErrorUI([
        handleApiError(errorData.err) + ". We apologize for the inconvenience.",
        "Please try again later or contact support for assistance."
      ]);
    }
  },
  isAdmin: false,
  isCheckingAdmin: false,
  isRetailerAdmin: false,
  policies: {},
  filterParams: {},
  currentOutletData: null,
  outletSelection: [], // Adjust the initial value as needed
  cart: 0,
  setCart: (newCart: number) => set({ cart: newCart }),
  setCurrentOutlet: (newOutletDetails: any) => set({ currentOutletData: newOutletDetails }),
  setOutletSelection: (outletSelection: MenuProps['items']) => set({ outletSelection })
}));

// handling percentage zustand for uploading
export const useUploadPercentageStore = create((set) => ({
  percentage: 0,
  isUpload: false,
  increasePercentage: () => set((percentage: number) => ({ percentage }))
}))

export default useRetailerStore;
